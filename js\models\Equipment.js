/**
 * 装备模型类
 */
class Equipment {
  constructor({
    id,
    name,
    type,
    level = 1,
    quality = 'common',
    attributes = {},
    randomAttributes = [],
    tier = 1,
    requiredRealm = ''
  }) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.level = level;
    this.quality = quality;
    this.attributes = attributes;
    this.randomAttributes = randomAttributes; // 随机属性词条数组
    this.tier = tier || 1; // 装备阶级，默认为1阶
    this.requiredRealm = requiredRealm || ''; // 装备需要的境界要求
  }

  // 获取装备类型名称
  getTypeName() {
    const typeNames = {
      'weapon': '武器',
      'armor': '护甲',
      'accessory': '饰品',
      'artifact': '灵宝'
    };

    return typeNames[this.type] || '未知';
  }

  // 获取装备品质颜色
  getQualityColor() {
    const qualityColors = {
      'common': '#ffffff',    // 白色
      'uncommon': '#1eff00',  // 绿色
      'rare': '#0070dd',      // 蓝色
      'epic': '#a335ee',      // 紫色
      'legendary': '#ff8000'  // 橙色
    };

    return qualityColors[this.quality] || '#ffffff';
  }

  // 获取装备品质名称
  getQualityName() {
    const qualityNames = {
      'common': '普通',
      'uncommon': '优秀',
      'rare': '精良',
      'epic': '史诗',
      'legendary': '传说'
    };

    return qualityNames[this.quality] || '未知';
  }

  // 获取装备属性描述
  getAttributesDescription() {
    let descriptions = [];

    // 添加装备阶级和境界要求
    descriptions.push(`阶级: ${this.tier}阶装备`);

    // 如果有境界要求，显示境界要求
    if (this.tier > 1 && this.requiredRealm) {
      descriptions.push(`境界要求: ${this.requiredRealm}`);
    }

    descriptions.push(''); // 空行

    // 添加基础属性标题
    descriptions.push('基础属性:');

    // 遍历基础属性
    Object.keys(this.attributes).forEach(attr => {
      const value = this.attributes[attr];

      // 根据属性类型生成描述
      switch (attr) {
        case 'hp':
          descriptions.push(`生命值 +${value}`);
          break;
        case 'attack':
          descriptions.push(`攻击力 +${value}`);
          break;
        case 'defense':
          descriptions.push(`防御力 +${value}`);
          break;
        case 'speed':
          descriptions.push(`速度 +${value}`);
          break;
        case 'critRate':
          descriptions.push(`暴击率 +${(value * 100).toFixed(1)}%`);
          break;
        case 'critDamage':
          descriptions.push(`暴击伤害 +${(value * 100).toFixed(1)}%`);
          break;
        case 'penetration':
          descriptions.push(`穿透 +${value}`);
          break;
        case 'daoRule':
          descriptions.push(`大道法则 +${value}`);
          break;
        default:
          descriptions.push(`${attr} +${value}`);
      }
    });

    // 添加随机属性
    if (this.randomAttributes && this.randomAttributes.length > 0) {
      descriptions.push('\n随机属性:');

      this.randomAttributes.forEach(attr => {
        let attrDesc = '';

        switch (attr.type) {
          case 'hp':
            attrDesc = `生命值 +${attr.value}`;
            break;
          case 'attack':
            attrDesc = `攻击力 +${attr.value}`;
            break;
          case 'defense':
            attrDesc = `防御力 +${attr.value}`;
            break;
          case 'speed':
            attrDesc = `速度 +${attr.value}`;
            break;
          case 'critRate':
            attrDesc = `暴击率 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'critDamage':
            attrDesc = `暴击伤害 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'penetration':
            attrDesc = `穿透 +${attr.value}`;
            break;
          case 'daoRule':
            attrDesc = `大道法则 +${attr.value}`;
            break;
          case 'hpBonus':
            attrDesc = `生命加成 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'attackBonus':
            attrDesc = `攻击加成 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'defenseBonus':
            attrDesc = `防御加成 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'damageBonus':
            attrDesc = `伤害加成 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'healEffect':
            attrDesc = `治疗效果 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'damageReduction':
            attrDesc = `伤害减免 +${(attr.value * 100).toFixed(1)}%`;
            break;
          case 'dodgeRate':
            attrDesc = `闪避率 +${(attr.value * 100).toFixed(1)}%`;
            break;
          default:
            attrDesc = `${attr.type} +${attr.value}`;
        }

        descriptions.push(attrDesc);
      });
    }

    return descriptions.join('\n');
  }

  // 升级装备
  upgrade() {
    // 升级成功率
    const successRate = Math.max(0.9 - (this.level - 1) * 0.05, 0.3);

    // 判断是否升级成功
    const isSuccess = Math.random() < successRate;

    if (isSuccess) {
      // 升级成功，提高装备等级
      this.level++;

      // 根据品质增加不同的属性值
      const qualityMultipliers = {
        'common': 1,
        'uncommon': 1.2,
        'rare': 1.5,
        'epic': 2,
        'legendary': 3
      };

      const multiplier = qualityMultipliers[this.quality] || 1;

      // 增加基础装备属性
      Object.keys(this.attributes).forEach(attr => {
        // 根据属性类型增加不同的属性值
        let increment = 0;
        switch (attr) {
          case 'hp':
            increment = 10 * multiplier;
            break;
          case 'attack':
            increment = 3 * multiplier;
            break;
          case 'defense':
            increment = 2 * multiplier;
            break;
          case 'speed':
            increment = 1 * multiplier;
            break;
          case 'critRate':
            increment = 0.01 * multiplier;
            break;
          case 'critDamage':
            increment = 0.02 * multiplier;
            break;
          case 'penetration':
            increment = 1 * multiplier;
            break;
          case 'daoRule':
            increment = 1 * multiplier;
            break;
          default:
            increment = 1 * multiplier;
        }

        // 对于百分比属性，保留小数点
        if (attr === 'critRate' || attr === 'critDamage') {
          this.attributes[attr] = parseFloat((this.attributes[attr] + increment).toFixed(2));
        } else {
          this.attributes[attr] += Math.floor(increment);
        }
      });

      // 升级随机属性
      if (this.randomAttributes && this.randomAttributes.length > 0) {
        this.randomAttributes.forEach(attr => {
          let increment = 0;

          // 根据属性类型增加不同的属性值
          switch (attr.type) {
            case 'hp':
              increment = 5 * multiplier;
              break;
            case 'attack':
              increment = 2 * multiplier;
              break;
            case 'defense':
              increment = 1.5 * multiplier;
              break;
            case 'speed':
              increment = 0.5 * multiplier;
              break;
            case 'critRate':
            case 'dodgeRate':
              increment = 0.005 * multiplier;
              break;
            case 'critDamage':
            case 'hpBonus':
            case 'attackBonus':
            case 'defenseBonus':
            case 'damageBonus':
            case 'healEffect':
            case 'damageReduction':
              increment = 0.01 * multiplier;
              break;
            case 'penetration':
            case 'daoRule':
              increment = 0.5 * multiplier;
              break;
            default:
              increment = 0.5 * multiplier;
          }

          // 对于百分比属性，保留小数点
          if (['critRate', 'critDamage', 'hpBonus', 'attackBonus', 'defenseBonus',
               'damageBonus', 'healEffect', 'damageReduction', 'dodgeRate'].includes(attr.type)) {
            attr.value = parseFloat((attr.value + increment).toFixed(3));
          } else {
            attr.value += Math.floor(increment);
          }
        });
      }

      return true;
    }

    return false;
  }

  // 获取装备评分
  getScore() {
    let score = 0;

    // 根据品质增加基础分
    const qualityScores = {
      'common': 10,
      'uncommon': 20,
      'rare': 40,
      'epic': 80,
      'legendary': 160
    };

    score += qualityScores[this.quality] || 0;

    // 根据等级增加分数
    score += this.level * 10;

    // 根据基础属性增加分数
    Object.keys(this.attributes).forEach(attr => {
      const value = this.attributes[attr];

      switch (attr) {
        case 'hp':
          score += value * 0.1;
          break;
        case 'attack':
          score += value * 2;
          break;
        case 'defense':
          score += value * 1.5;
          break;
        case 'speed':
          score += value * 1;
          break;
        case 'critRate':
          score += value * 100;
          break;
        case 'critDamage':
          score += value * 50;
          break;
        case 'penetration':
          score += value * 3;
          break;
        case 'daoRule':
          score += value * 5;
          break;
        default:
          score += value;
      }
    });

    // 根据随机属性增加分数
    if (this.randomAttributes && this.randomAttributes.length > 0) {
      this.randomAttributes.forEach(attr => {
        const value = attr.value;

        switch (attr.type) {
          case 'hp':
            score += value * 0.15; // 随机属性的生命值比基础属性更值钱
            break;
          case 'attack':
            score += value * 2.5;
            break;
          case 'defense':
            score += value * 2;
            break;
          case 'speed':
            score += value * 1.5;
            break;
          case 'critRate':
            score += value * 120;
            break;
          case 'critDamage':
            score += value * 60;
            break;
          case 'penetration':
            score += value * 4;
            break;
          case 'daoRule':
            score += value * 6;
            break;
          case 'hpBonus':
          case 'attackBonus':
          case 'defenseBonus':
            score += value * 80;
            break;
          case 'damageBonus':
            score += value * 100;
            break;
          case 'healEffect':
            score += value * 70;
            break;
          case 'damageReduction':
            score += value * 90;
            break;
          case 'dodgeRate':
            score += value * 110;
            break;
          default:
            score += value * 2; // 默认随机属性比基础属性更值钱
        }
      });
    }

    return Math.floor(score);
  }

  // 检查角色是否可以装备该装备
  canEquip(character) {
    if (!character) return false;

    // 如果是1阶装备，所有角色都可以装备
    if (this.tier === 1) return true;

    // 如果没有境界要求，所有角色都可以装备
    if (!this.requiredRealm) return true;

    // 获取角色境界
    const characterRealm = character.cultivation || '';

    // 境界比较函数
    const compareRealms = (realm1, realm2) => {
      // 境界等级排序
      const realmOrder = [
        '练气期', '筑基期', '金丹期', '元婴期',
        '化神期', '返虚期', '合道期', '渡劫期', '大乘期'
      ];

      // 提取基础境界
      const getBaseRealm = (realm) => {
        for (const baseRealm of realmOrder) {
          if (realm.includes(baseRealm)) {
            return baseRealm;
          }
        }
        return realm;
      };

      // 获取境界索引
      const realm1Index = realmOrder.indexOf(getBaseRealm(realm1));
      const realm2Index = realmOrder.indexOf(getBaseRealm(realm2));

      // 如果找不到境界，返回-1
      if (realm1Index === -1 || realm2Index === -1) return false;

      // 比较境界等级
      return realm1Index >= realm2Index;
    };

    // 检查角色境界是否达到装备要求
    return compareRealms(characterRealm, this.requiredRealm);
  }

  // 转换为JSON格式
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      level: this.level,
      quality: this.quality,
      attributes: { ...this.attributes },
      randomAttributes: [...this.randomAttributes],
      tier: this.tier,
      requiredRealm: this.requiredRealm
    };
  }
}

export default Equipment;