# 后台修炼修复说明

## 问题描述

从调试台记录可以看到，游戏在用户还没有完成登录流程时就开始了后台修炼计算：

```
JingshiScene.js:1096 修炼完成，增加灵力: 10 (周期数: 1)
JingshiScene.js:1113 检查突破条件 - 当前灵力: 30, 所需灵力: 496, 境界: 练气期一层
```

这违反了您的要求：**后台修炼计算应该只在玩家点击"开始游戏"之后才进行，而不是在新用户开始游戏之前自动运行**。

## 问题原因

1. **游戏循环中的无条件修炼检查**：
   - 游戏循环在启动后立即开始检查修炼进度
   - 没有检查用户登录状态

2. **JingshiScene中的自动启动**：
   - `checkAndUpdateMeditation()` 静态方法没有登录状态检查
   - `drawCircularProgress()` 方法也会自动启动修炼

3. **缺少登录状态控制**：
   - 修炼系统没有与登录系统关联
   - 没有区分登录前和登录后的状态

## 修复方案

### 1. 修改游戏循环 (game.js)

**修改前**：
```javascript
// 检查和更新静室修炼进度
// 无论当前场景是什么，都检查和更新静室修炼进度
if (typeof JingshiScene !== 'undefined') {
  // 如果修炼未启动，则启动修炼
  if (!JingshiScene.meditationStarted) {
    JingshiScene.meditationStarted = true;
    JingshiScene.lastRotationTime = Date.now();
    console.log('自动修炼已启动（游戏循环中）');
  }
  // 检查和更新修炼进度
  JingshiScene.checkAndUpdateMeditation();
}
```

**修改后**：
```javascript
// 检查和更新静室修炼进度
// 只有在用户登录完成后才进行后台修炼计算
if (typeof JingshiScene !== 'undefined' && this.loginManager && this.loginManager.isLoggedIn) {
  // 如果修炼未启动，则启动修炼
  if (!JingshiScene.meditationStarted) {
    JingshiScene.meditationStarted = true;
    JingshiScene.lastRotationTime = Date.now();
    console.log('自动修炼已启动（用户登录后）');
  }
  // 检查和更新修炼进度
  JingshiScene.checkAndUpdateMeditation();
}
```

### 2. 修改JingshiScene静态方法

**修改前**：
```javascript
// 静态方法：检查并更新修炼进度
static checkAndUpdateMeditation() {
  // 获取当前时间
  const now = Date.now();
  // ... 直接开始修炼计算
}
```

**修改后**：
```javascript
// 静态方法：检查并更新修炼进度
static checkAndUpdateMeditation() {
  // 只有在用户登录后才进行修炼计算
  if (!game.loginManager || !game.loginManager.isLoggedIn) {
    return;
  }
  
  // 获取当前时间
  const now = Date.now();
  // ... 继续修炼计算
}
```

### 3. 修改圆形进度条绘制

**修改前**：
```javascript
// 确保修炼已启动
if (!JingshiScene.meditationStarted) {
  JingshiScene.meditationStarted = true;
  JingshiScene.lastRotationTime = now - 1000;
  console.log('自动修炼已启动（圆形进度条中）');
}
```

**修改后**：
```javascript
// 确保修炼已启动（只有在用户登录后）
if (!JingshiScene.meditationStarted && game.loginManager && game.loginManager.isLoggedIn) {
  JingshiScene.meditationStarted = true;
  JingshiScene.lastRotationTime = now - 1000;
  console.log('自动修炼已启动（圆形进度条中）');
}
```

### 4. 登录完成后重置修炼状态

在 `LoginManager.js` 中添加修炼状态重置：

```javascript
// 重置修炼状态，确保登录后重新开始修炼计算
if (JingshiScene) {
  JingshiScene.meditationStarted = false;
  JingshiScene.lastRotationTime = null;
  console.log('修炼状态已重置，等待用户开始游戏');
}
```

## 修复效果

### ✅ 修复前的问题

- 游戏启动后立即开始修炼计算
- 新用户在登录前就有修炼收益
- 违反了"点击开始游戏后才计算"的要求

### ✅ 修复后的行为

1. **新用户登录流程**：
   ```
   游戏启动 → 显示隐私授权弹窗 → 用户授权 → 服务器选择 → 登录完成 → 开始修炼计算
   ```

2. **老用户自动登录**：
   ```
   游戏启动 → 自动登录 → 登录完成 → 开始修炼计算
   ```

3. **修炼计算控制**：
   - 只在 `loginManager.isLoggedIn = true` 后才开始
   - 登录完成后重置修炼状态
   - 确保修炼从登录后开始计算

## 预期的调试台输出

### 新用户（修复后）

```
游戏加载完成，开始初始化登录流程
开始初始化登录流程...
未发现登录历史记录
用户首次使用，需要进行完整登录流程
显示隐私权限授权弹窗
// 用户授权后...
获取openid成功: xxx
从云数据库加载游戏状态...
修炼状态已重置，等待用户开始游戏
登录流程完成
自动修炼已启动（用户登录后）  // 这时才开始修炼
修炼完成，增加灵力: 10 (周期数: 1)
```

### 老用户（修复后）

```
游戏加载完成，开始初始化登录流程
开始初始化登录流程...
发现本地授权信息和openid记录
检测到用户曾经登录过，开始自动登录流程
获取openid成功: xxx
从云数据库加载游戏状态...
修炼状态已重置，等待用户开始游戏
登录流程完成
自动修炼已启动（用户登录后）  // 这时才开始修炼
修炼完成，增加灵力: 10 (周期数: 1)
```

## 关键改进

1. **时序控制**：确保修炼计算只在登录完成后进行
2. **状态管理**：通过 `loginManager.isLoggedIn` 控制修炼启动
3. **状态重置**：登录完成后重置修炼状态，确保从登录时开始计算
4. **多点检查**：在游戏循环、静态方法、UI绘制等多个地方都添加了登录状态检查

## 测试建议

1. **清除本地存储测试**：
   ```javascript
   wx.clearStorageSync();
   // 刷新游戏，应该不会在登录前看到修炼日志
   ```

2. **检查日志顺序**：
   - 确保 "登录流程完成" 出现在修炼日志之前
   - 确保 "修炼状态已重置" 出现在修炼开始之前

3. **功能验证**：
   - 新用户：授权完成后才开始修炼
   - 老用户：自动登录完成后才开始修炼

现在后台修炼系统已经完全按照您的要求进行了修复，只有在用户完成登录流程后才会开始后台修炼计算！
