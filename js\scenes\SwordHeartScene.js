/**
 * 剑心场景类
 * 显示所有剑心，并允许玩家选择剑心进行详细操作
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import game from '../../game';

class SwordHeartScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 剑心列表
    this.swordHearts = [];

    // 当前选中的剑心
    this.selectedSwordHeart = null;

    // 滚动相关
    this.scrollY = 0;
    this.maxScrollY = 0;
    this.isDragging = false;
    this.lastTouchY = 0;

    // 初始化UI
    this.initUI();

    // 添加触摸事件监听
    this.addTouchEventListeners();
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 加载剑心数据
    this.loadSwordHearts();

    // 创建返回按钮
    this.createBackButton();

    // 创建剑心卡片
    this.createSwordHeartCards();
  }

  // 加载剑心数据
  loadSwordHearts() {
    if (game.gameStateManager) {
      this.swordHearts = game.gameStateManager.getAllSwordHearts();
    }
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('main');
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建剑心卡片
  createSwordHeartCards() {
    const headerHeight = 80;
    const cardWidth = 150;
    const cardHeight = 180;
    const margin = 20;
    const cardsPerRow = 2;
    const startX = (this.screenWidth - (cardWidth * cardsPerRow + margin * (cardsPerRow - 1))) / 2;
    const startY = headerHeight + 60;

    this.swordHeartButtons = [];

    this.swordHearts.forEach((swordHeart, index) => {
      const row = Math.floor(index / cardsPerRow);
      const col = index % cardsPerRow;
      const x = startX + col * (cardWidth + margin);
      const y = startY + row * (cardHeight + margin);

      const button = new Button(
        this.ctx,
        x,
        y - this.scrollY, // 应用滚动偏移
        cardWidth,
        cardHeight,
        swordHeart.name,
        null,
        swordHeart.color || '#4299e1',
        () => {
          if (swordHeart.unlocked) {
            this.onSwordHeartSelected(swordHeart);
          } else {
            this.showUnlockDialog(swordHeart);
          }
        }
      );

      this.swordHeartButtons.push(button);
      this.addUIElement(button);
    });

    // 计算最大滚动距离
    const totalRows = Math.ceil(this.swordHearts.length / cardsPerRow);
    const contentHeight = startY + totalRows * (cardHeight + margin);
    this.maxScrollY = Math.max(0, contentHeight - this.screenHeight + 100); // 100是底部额外空间
  }

  // 添加触摸事件监听
  addTouchEventListeners() {
    // 获取canvas元素
    let canvas = null;

    // 尝试从场景管理器获取canvas
    if (this.sceneManager && this.sceneManager.canvas) {
      canvas = this.sceneManager.canvas;
    }
    // 尝试从ctx获取canvas
    else if (this.ctx && this.ctx.canvas) {
      canvas = this.ctx.canvas;
    }
    // 尝试从全局game对象获取canvas
    else if (typeof game !== 'undefined' && game.canvas) {
      canvas = game.canvas;
    }

    if (!canvas) {
      console.error('无法获取canvas元素，滚动功能将不可用');
      return;
    }

    // 保存canvas引用
    this.canvas = canvas;

    // 触摸开始
    this.handleTouchStart = (x, y) => {
      if (this.isActive) {
        this.isDragging = true;
        this.lastTouchY = y;
        return true;
      }
      return false;
    };

    // 触摸移动
    this.handleTouchMove = (x, y) => {
      if (this.isActive && this.isDragging) {
        const deltaY = y - this.lastTouchY;

        // 更新滚动位置
        this.scrollY -= deltaY;

        // 限制滚动范围
        this.scrollY = Math.max(0, Math.min(this.scrollY, this.maxScrollY));

        // 更新最后触摸位置
        this.lastTouchY = y;

        // 重新创建UI元素以应用新的滚动位置
        this.clearUIElements();
        this.createBackButton();
        this.createSwordHeartCards();

        return true;
      }
      return false;
    };

    // 触摸结束
    this.handleTouchEnd = (x, y) => {
      if (this.isActive) {
        this.isDragging = false;
        return true;
      }
      return false;
    };

    // 如果在浏览器环境中，添加鼠标滚轮事件（用于桌面测试）
    if (typeof window !== 'undefined' && canvas) {
      canvas.addEventListener('wheel', (e) => {
        if (this.isActive) {
          // 更新滚动位置
          this.scrollY += e.deltaY * 0.5;

          // 限制滚动范围
          this.scrollY = Math.max(0, Math.min(this.scrollY, this.maxScrollY));

          // 重新创建UI元素以应用新的滚动位置
          this.clearUIElements();
          this.createBackButton();
          this.createSwordHeartCards();

          // 阻止默认行为
          e.preventDefault();
        }
      });
    }
  }

  // 剑心选择回调
  onSwordHeartSelected(swordHeart) {
    this.selectedSwordHeart = swordHeart;

    // 跳转到剑心详情场景
    this.sceneManager.showScene('swordHeartDetail', {
      swordHeartId: swordHeart.id
    });
  }

  // 显示解锁对话框
  showUnlockDialog(swordHeart) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];

    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '剑心未解锁',
      `${swordHeart.name}尚未解锁，请通过剑心试炼获取更多剑心。`,
      dialogButtons
    );

    this.addUIElement(dialog);
    dialog.show();
  }

  // 场景显示时的回调
  onShow(params) {
    // 重置滚动位置
    this.scrollY = 0;

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 0;

    // 设置场景为活动状态，以便触摸事件生效
    this.isActive = true;
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 重置选中状态
    this.selectedSwordHeart = null;

    // 设置场景为非活动状态，以便触摸事件不生效
    this.isActive = false;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制剑心信息
    this.drawSwordHeartInfo();
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a202c');
    gradient.addColorStop(1, '#2d3748');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    const headerHeight = 80;

    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('剑心系统', this.screenWidth / 2, headerHeight / 2);

    // 绘制剑意值
    const swordIntent = game.gameStateManager ? game.gameStateManager.getSwordIntent() : 0;
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#4299e1';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`剑意: ${swordIntent}`, this.screenWidth - 20, headerHeight / 2);
  }

  // 绘制剑心信息
  drawSwordHeartInfo() {
    const headerHeight = 80;
    const cardWidth = 150;
    const cardHeight = 180;
    const margin = 20;
    const cardsPerRow = 2;
    const startX = (this.screenWidth - (cardWidth * cardsPerRow + margin * (cardsPerRow - 1))) / 2;
    const startY = headerHeight + 60;

    this.swordHearts.forEach((swordHeart, index) => {
      const row = Math.floor(index / cardsPerRow);
      const col = index % cardsPerRow;
      const x = startX + col * (cardWidth + margin);
      const y = startY + row * (cardHeight + margin) - this.scrollY; // 应用滚动偏移

      // 如果卡片在可视区域外，跳过绘制
      if (y + cardHeight < 0 || y > this.screenHeight) {
        return;
      }

      // 绘制剑心卡片背景
      this.ctx.fillStyle = swordHeart.unlocked ? (swordHeart.color || '#4299e1') : '#718096';
      this.ctx.fillRect(x, y, cardWidth, cardHeight);

      // 绘制剑心名称
      this.ctx.font = 'bold 18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(swordHeart.name, x + cardWidth / 2, y + 20);

      // 绘制剑心等级
      this.ctx.font = '16px Arial';
      this.ctx.fillText(`等级: ${swordHeart.level}/${swordHeart.maxLevel}`, x + cardWidth / 2, y + 50);

      // 绘制剑心进阶等级
      this.ctx.fillText(`进阶: ${swordHeart.advancementLevel}/${swordHeart.maxAdvancementLevel}`, x + cardWidth / 2, y + 80);

      // 绘制剑心描述
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#f7fafc';

      // 裁剪描述文本以适应卡片
      const description = this.truncateText(swordHeart.description, 14, cardWidth - 20);
      this.ctx.fillText(description, x + cardWidth / 2, y + 110);

      // 如果未解锁，绘制锁定图标
      if (!swordHeart.unlocked) {
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillText('🔒', x + cardWidth / 2, y + 140);
      }
    });

    // 绘制滚动指示器（如果有滚动）
    if (this.maxScrollY > 0) {
      // 绘制滚动条背景
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      this.ctx.fillRect(this.screenWidth - 10, headerHeight, 5, this.screenHeight - headerHeight);

      // 计算滚动条高度和位置
      const scrollBarHeight = Math.max(30, (this.screenHeight - headerHeight) * (this.screenHeight / (this.screenHeight + this.maxScrollY)));
      const scrollBarY = headerHeight + (this.scrollY / this.maxScrollY) * (this.screenHeight - headerHeight - scrollBarHeight);

      // 绘制滚动条
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      this.ctx.fillRect(this.screenWidth - 10, scrollBarY, 5, scrollBarHeight);
    }
  }

  // 裁剪文本以适应指定宽度
  truncateText(text, fontSize, maxWidth) {
    if (!text) return '';

    this.ctx.font = `${fontSize}px Arial`;

    if (this.ctx.measureText(text).width <= maxWidth) {
      return text;
    }

    let truncated = text;
    while (this.ctx.measureText(truncated + '...').width > maxWidth && truncated.length > 0) {
      truncated = truncated.substring(0, truncated.length - 1);
    }

    return truncated + '...';
  }
}

export default SwordHeartScene;
