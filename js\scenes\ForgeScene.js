/**
 * 装备锻造场景
 * 用于消耗炼器石随机生成装备
 */
import BaseScene from './BaseScene.js';
import Button from '../ui/Button.js';
import AppContext from '../utils/AppContext.js';
import Equipment from '../models/Equipment.js';

// 直接在ForgeScene中定义所需常量，避免导入问题
// 物品类型
const ITEM_TYPES = {
    // 装备类型
    WEAPON: 'weapon',     // 武器
    ARMOR: 'armor',       // 护甲
    ACCESSORY: 'accessory', // 饰品
    ARTIFACT: 'artifact',   // 灵宝

    // 消耗品类型
    POTION: 'potion',     // 药水
    MATERIAL: 'material', // 材料
    FRAGMENT: 'fragment', // 角色碎片
    CURRENCY: 'currency', // 货币
};

// 物品品质
const ITEM_QUALITY = {
    NORMAL: 0,    // 普通
    GOOD: 1,      // 良好
    RARE: 2,      // 稀有
    EPIC: 3,      // 史诗
    LEGENDARY: 4, // 传说
};

// 物品品质名称
const ITEM_QUALITY_NAMES = [
    '普通', // 0
    '良好', // 1
    '稀有', // 2
    '史诗', // 3
    '传说'  // 4
];

// 物品品质颜色
const ITEM_QUALITY_COLORS = [
    '#CCCCCC', // 普通 (灰色)
    '#55AA55', // 良好 (绿色)
    '#5555AA', // 稀有 (蓝色)
    '#AA55AA', // 史诗 (紫色)
    '#AAAA55'  // 传说 (金色)
];

// 将ForgeScene作为默认导出
class ForgeScene extends BaseScene {
    constructor(ctx, screenWidth, screenHeight, sceneManager) {
        super(ctx, screenWidth, screenHeight, sceneManager);
        this.name = 'forge';
        this.title = '装备锻造';

        // 锻造消耗的炼器石数量
        this.forgeCost = 1; // 修改为1个
        this.tenForgeCost = 10; // 修改为10个

        // 锻造结果
        this.forgeResults = [];
        this.showingResults = false;

        // 锻造概率
        this.probabilities = {
            [ITEM_QUALITY.NORMAL]: 0.40,    // 普通 40%
            [ITEM_QUALITY.GOOD]: 0.30,      // 良好 30%
            [ITEM_QUALITY.RARE]: 0.15,      // 稀有 15%
            [ITEM_QUALITY.EPIC]: 0.10,      // 史诗 10%
            [ITEM_QUALITY.LEGENDARY]: 0.05   // 传说 5%
        };

        // 保底机制
        this.pityCounter = 0;
        this.pityThreshold = 50;  // 50次必出传说品质
    }

    // 场景显示时的回调
    onShow(params) {
        console.log('ForgeScene.onShow被调用，参数：', params);

        // 获取锻造保底计数
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        this.pityCounter = player.forgePity || 0;

        this.initUI();
        this.visible = true;
    }

    // 场景隐藏时的回调
    onHide() {
        // 清空UI元素
        this.clearUIElements();

        // 重置锻造结果
        this.forgeResults = [];
        this.showingResults = false;

        // 设置场景为不可见
        this.visible = false;

        console.log('ForgeScene隐藏');
    }

    // 初始化UI
    initUI() {
        // 清空UI元素
        this.clearUIElements();

        if (this.showingResults) {
            this.initResultUI();
        } else {
            this.initForgeUI();
        }
    }

    initForgeUI() {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();

        // 获取玩家的炼器石数量
        const forgeStoneCount = this.getForgeStoneCount(player);

        // 返回按钮
        const backButton = new Button(
            this.ctx,
            10,
            10,
            80,
            40,
            '返回',
            null,
            null,
            () => {
                this.sceneManager.showScene('main', { from: 'forge' });
            }
        );
        this.addUIElement(backButton);

        // 单次锻造按钮
        const singleForgeButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 150,
            this.screenHeight / 2 - 50,
            140,
            50,
            `锻造 (${this.forgeCost}炼器石)`,
            null,
            null,
            () => this.performForge(1),
            false,
            forgeStoneCount < this.forgeCost
        );
        this.addUIElement(singleForgeButton);

        // 十连锻造按钮
        const tenForgeButton = new Button(
            this.ctx,
            this.screenWidth / 2 + 10,
            this.screenHeight / 2 - 50,
            140,
            50,
            `十连锻造 (${this.tenForgeCost}炼器石)`,
            null,
            null,
            () => this.performForge(10),
            false,
            forgeStoneCount < this.tenForgeCost
        );
        this.addUIElement(tenForgeButton);

        // 查看概率按钮
        const probabilityButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 70,
            this.screenHeight / 2 + 30,
            140,
            40,
            '锻造概率',
            null,
            null,
            () => this.showProbabilities()
        );
        this.addUIElement(probabilityButton);
    }

    initResultUI() {
        // 返回按钮
        const backButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 50,
            this.screenHeight - 60,
            100,
            40,
            '返回',
            null,
            null,
            () => {
                this.showingResults = false;
                this.forgeResults = [];
                this.initUI();
            }
        );
        this.addUIElement(backButton);
    }

    // 获取炼器石数量
    getForgeStoneCount(player) {
        if (!player || !player.items) return 0;

        // 查找炼器石物品
        const forgeStone = player.items.find(item => item.name === '炼器石');
        return forgeStone ? forgeStone.count : 0;
    }

    // 执行锻造
    performForge(count) {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        const cost = count === 10 ? this.tenForgeCost : this.forgeCost * count;

        // 获取玩家境界决定装备阶级
        const mainCharacter = AppContext.game.gameStateManager.getCharacterById(1);
        const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

        // 决定使用的兽材类型
        const useTier2Material = playerRealm.includes('筑基期');
        const materialName = useTier2Material ? '二阶兽材' : '一阶兽材';

        // 获取兽材数量
        const beastMaterialCount = this.getBeastMaterialCount(player, materialName);

        // 检查是否有足够的兽材
        if (beastMaterialCount < cost) {
            // 使用console错误替代uiManager调用
            console.error(`${materialName}不足！`);
            // 在画布上显示错误消息
            this.showErrorMessage(`${materialName}不足！`);
            return;
        }

        // 扣除兽材
        this.consumeBeastMaterial(player, materialName, cost);

        // 执行锻造
        this.forgeResults = [];

        // 十连锻造保底一个史诗以上品质
        let hasGuaranteedEpic = false;

        for (let i = 0; i < count; i++) {
            // 十连锻造的最后一次，检查是否需要保底
            const needGuarantee = count === 10 && i === 9 && !hasGuaranteedEpic;

            const result = this.forgeEquipment(needGuarantee);
            this.forgeResults.push(result);

            // 添加装备到玩家装备列表
            const equipment = new Equipment(result);
            AppContext.game.gameStateManager.addEquipment(equipment);

            // 判断是否锻造了史诗以上品质
            if (result.quality >= ITEM_QUALITY.EPIC) {
                hasGuaranteedEpic = true;
            }
        }

        // 增加锻造次数，用于保底
        this.pityCounter += count;
        // 如果锻造出了传说品质，重置保底计数
        if (this.forgeResults.some(result => result.quality === ITEM_QUALITY.LEGENDARY)) {
            this.pityCounter = 0;
        }
        // 保存保底计数
        const updatedPlayer = { ...player, forgePity: this.pityCounter };
        gameStateManager.setPlayer(updatedPlayer);

        // 显示结果
        this.showingResults = true;
        this.initUI();
    }

    // 消耗炼器石
    consumeForgeStone(player, amount) {
        if (!player || !player.items) return;

        // 查找炼器石物品
        const forgeStoneIndex = player.items.findIndex(item => item.name === '炼器石');
        if (forgeStoneIndex === -1) return;

        // 减少数量
        player.items[forgeStoneIndex].count -= amount;

        // 如果数量为0，移除物品
        if (player.items[forgeStoneIndex].count <= 0) {
            player.items.splice(forgeStoneIndex, 1);
        }
    }

    // 生成一件随机装备
    forgeEquipment(guaranteeEpicOrAbove = false) {
        let quality;

        // 检查是否触发保底
        if (this.pityCounter >= this.pityThreshold - 1) {
            quality = ITEM_QUALITY.LEGENDARY;
        } else if (guaranteeEpicOrAbove) {
            // 保底史诗及以上品质
            const epicPlusProb = {
                [ITEM_QUALITY.EPIC]: this.probabilities[ITEM_QUALITY.EPIC] / (this.probabilities[ITEM_QUALITY.EPIC] + this.probabilities[ITEM_QUALITY.LEGENDARY]),
                [ITEM_QUALITY.LEGENDARY]: this.probabilities[ITEM_QUALITY.LEGENDARY] / (this.probabilities[ITEM_QUALITY.EPIC] + this.probabilities[ITEM_QUALITY.LEGENDARY])
            };

            const rand = Math.random();
            let cumulativeProbability = 0;

            for (const [q, prob] of Object.entries(epicPlusProb)) {
                cumulativeProbability += prob;
                if (rand <= cumulativeProbability) {
                    quality = parseInt(q);
                    break;
                }
            }
        } else {
            // 随机抽取品质
            const rand = Math.random();
            let cumulativeProbability = 0;

            for (const [q, prob] of Object.entries(this.probabilities)) {
                cumulativeProbability += prob;
                if (rand <= cumulativeProbability) {
                    quality = parseInt(q);
                    break;
                }
            }
        }

        // 随机选择装备类型
        const equipmentTypes = [
            ITEM_TYPES.WEAPON,
            ITEM_TYPES.ARMOR,
            ITEM_TYPES.ACCESSORY,
            ITEM_TYPES.ARTIFACT
        ];
        const randomType = equipmentTypes[Math.floor(Math.random() * equipmentTypes.length)];

        // 生成唯一ID
        const uniqueId = `equip_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

        // 根据品质和类型随机生成装备名称
        const typeNames = {
            [ITEM_TYPES.WEAPON]: ['剑', '刀', '枪', '斧', '锤', '弓'],
            [ITEM_TYPES.ARMOR]: ['铠甲', '护甲', '战衣', '法袍', '甲胄'],
            [ITEM_TYPES.ACCESSORY]: ['戒指', '项链', '手镯', '腰带', '玉佩'],
            [ITEM_TYPES.ARTIFACT]: ['宝珠', '法器', '宝盒', '玉简', '宝鼎']
        };

        const qualityPrefixes = {
            [ITEM_QUALITY.NORMAL]: ['普通', '简易', '基础', '粗糙'],
            [ITEM_QUALITY.GOOD]: ['精良', '坚固', '优质', '精致'],
            [ITEM_QUALITY.RARE]: ['稀有', '珍贵', '神秘', '奇特'],
            [ITEM_QUALITY.EPIC]: ['史诗', '传承', '远古', '神圣'],
            [ITEM_QUALITY.LEGENDARY]: ['传说', '神话', '至尊', '不朽']
        };

        const randomTypeName = typeNames[randomType][Math.floor(Math.random() * typeNames[randomType].length)];
        const randomPrefix = qualityPrefixes[quality][Math.floor(Math.random() * qualityPrefixes[quality].length)];
        const name = `${randomPrefix}${randomTypeName}`;

        // 根据品质和类型生成属性
        const attributes = this.generateAttributes(quality, randomType);

        // 生成随机属性词条
        const randomAttributes = this.generateRandomAttributes(quality, randomType);

        // 获取玩家境界决定装备阶级
        const player = AppContext.game.gameStateManager.getPlayer();
        const mainCharacter = AppContext.game.gameStateManager.getCharacterById(1);
        const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

        // 决定装备阶级和境界要求
        let tier = 1;
        let requiredRealm = '';

        // 根据玩家境界决定装备阶级
        // 如果玩家境界达到筑基期，有机会锻造出二阶装备
        if (playerRealm.includes('筑基期') && Math.random() < 0.3) {
            tier = 2;
            requiredRealm = '筑基期';
            // 二阶装备属性加成
            Object.keys(attributes).forEach(attr => {
                attributes[attr] = Math.floor(attributes[attr] * 1.5);
            });
        }

        // 创建装备对象
        return {
            id: uniqueId,
            name: tier > 1 ? `二阶${name}` : name,
            type: randomType,
            quality: quality,
            level: 1,
            attributes: attributes,
            randomAttributes: randomAttributes,
            tier: tier,
            requiredRealm: requiredRealm,
            description: `通过锻造获得的${tier}阶${ITEM_QUALITY_NAMES[quality]}品质${randomTypeName}。`
        };
    }

    // 根据品质和类型生成装备属性
    generateAttributes(quality, type) {
        const attributes = {};
        const qualityMultiplier = {
            [ITEM_QUALITY.NORMAL]: 1,
            [ITEM_QUALITY.GOOD]: 1.5,
            [ITEM_QUALITY.RARE]: 2,
            [ITEM_QUALITY.EPIC]: 3,
            [ITEM_QUALITY.LEGENDARY]: 5
        };

        const multiplier = qualityMultiplier[quality] || 1;

        // 基础属性
        switch (type) {
            case ITEM_TYPES.WEAPON:
                attributes.attack = Math.floor(20 * multiplier);

                // 高品质武器有暴击属性
                if (quality >= ITEM_QUALITY.RARE) {
                    attributes.critRate = parseFloat((0.05 * (quality - ITEM_QUALITY.RARE + 1)).toFixed(2));
                }

                if (quality >= ITEM_QUALITY.EPIC) {
                    attributes.critDamage = parseFloat((0.15 * (quality - ITEM_QUALITY.EPIC + 1)).toFixed(2));
                }
                break;

            case ITEM_TYPES.ARMOR:
                attributes.hp = Math.floor(50 * multiplier);
                attributes.defense = Math.floor(15 * multiplier);

                // 高品质护甲有额外属性
                if (quality >= ITEM_QUALITY.RARE) {
                    attributes.speed = Math.floor(5 * (quality - ITEM_QUALITY.RARE + 1));
                }
                break;

            case ITEM_TYPES.ACCESSORY:
                attributes.hp = Math.floor(20 * multiplier);
                attributes.speed = Math.floor(10 * multiplier);

                if (quality >= ITEM_QUALITY.GOOD) {
                    attributes.critRate = parseFloat((0.02 * (quality - ITEM_QUALITY.GOOD + 1)).toFixed(2));
                }
                break;

            case ITEM_TYPES.ARTIFACT:
                attributes.attack = Math.floor(10 * multiplier);
                attributes.defense = Math.floor(10 * multiplier);

                if (quality >= ITEM_QUALITY.RARE) {
                    attributes.penetration = Math.floor(5 * (quality - ITEM_QUALITY.RARE + 1));
                }

                if (quality >= ITEM_QUALITY.EPIC) {
                    attributes.critRate = parseFloat((0.03 * (quality - ITEM_QUALITY.EPIC + 1)).toFixed(2));
                    attributes.critDamage = parseFloat((0.1 * (quality - ITEM_QUALITY.EPIC + 1)).toFixed(2));
                }
                break;
        }

        // 传说品质装备都有道韵属性
        if (quality === ITEM_QUALITY.LEGENDARY) {
            attributes.daoRule = Math.floor(5 + Math.random() * 10);
        }

        return attributes;
    }

    // 生成随机属性词条
    generateRandomAttributes(quality, type) {
        // 不同品质装备的随机属性数量
        const attributeCountByQuality = {
            [ITEM_QUALITY.NORMAL]: 0,  // 普通装备没有随机属性
            [ITEM_QUALITY.GOOD]: 1,    // 绿色装备有1个随机属性
            [ITEM_QUALITY.RARE]: 2,    // 蓝色装备有2个随机属性
            [ITEM_QUALITY.EPIC]: 3,    // 紫色装备有3个随机属性
            [ITEM_QUALITY.LEGENDARY]: 4 // 橙色装备有4个随机属性
        };

        const attributeCount = attributeCountByQuality[quality] || 0;
        if (attributeCount === 0) {
            return []; // 普通装备没有随机属性
        }

        // 可能的随机属性类型
        const possibleAttributes = [
            'hp', 'attack', 'defense', 'speed', 'critRate', 'critDamage',
            'hpBonus', 'attackBonus', 'defenseBonus', 'damageBonus',
            'healEffect', 'damageReduction', 'dodgeRate', 'penetration', 'daoRule'
        ];

        // 根据装备类型调整属性权重
        const typeWeights = {
            [ITEM_TYPES.WEAPON]: {
                'attack': 3, 'critRate': 2, 'critDamage': 2, 'attackBonus': 2, 'damageBonus': 2,
                'penetration': 1.5, 'speed': 1, 'daoRule': 1
            },
            [ITEM_TYPES.ARMOR]: {
                'hp': 3, 'defense': 3, 'hpBonus': 2, 'defenseBonus': 2, 'damageReduction': 2,
                'healEffect': 1.5, 'dodgeRate': 1, 'daoRule': 1
            },
            [ITEM_TYPES.ACCESSORY]: {
                'speed': 3, 'critRate': 2, 'dodgeRate': 2, 'hpBonus': 1.5, 'attackBonus': 1.5,
                'defenseBonus': 1.5, 'healEffect': 1, 'daoRule': 1
            },
            [ITEM_TYPES.ARTIFACT]: {
                'daoRule': 3, 'penetration': 2, 'damageBonus': 2, 'critDamage': 1.5,
                'attackBonus': 1.5, 'defenseBonus': 1.5, 'healEffect': 1, 'damageReduction': 1
            }
        };

        const weights = typeWeights[type] || {};

        // 生成随机属性
        const randomAttributes = [];
        const selectedTypes = new Set(); // 记录已选属性类型，避免重复

        for (let i = 0; i < attributeCount; i++) {
            // 根据权重随机选择属性类型
            let selectedType = null;
            let attempts = 0;

            // 尝试最多10次选择不重复的属性类型
            while (attempts < 10) {
                const weightedSelection = [];

                possibleAttributes.forEach(attrType => {
                    if (!selectedTypes.has(attrType)) {
                        const weight = weights[attrType] || 1;
                        for (let j = 0; j < weight * 10; j++) {
                            weightedSelection.push(attrType);
                        }
                    }
                });

                if (weightedSelection.length > 0) {
                    selectedType = weightedSelection[Math.floor(Math.random() * weightedSelection.length)];
                    break;
                }

                attempts++;
            }

            // 如果没有选到属性，随机选一个
            if (!selectedType) {
                const availableTypes = possibleAttributes.filter(t => !selectedTypes.has(t));
                if (availableTypes.length > 0) {
                    selectedType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
                } else {
                    // 如果所有属性都被选过，则跳过
                    continue;
                }
            }

            // 标记该属性类型已被选择
            selectedTypes.add(selectedType);

            // 生成属性值
            let value = 0;
            const qualityMultiplier = {
                [ITEM_QUALITY.GOOD]: 1,
                [ITEM_QUALITY.RARE]: 1.5,
                [ITEM_QUALITY.EPIC]: 2,
                [ITEM_QUALITY.LEGENDARY]: 3
            }[quality] || 1;

            // 根据属性类型生成不同范围的属性值
            switch (selectedType) {
                case 'hp':
                    value = Math.floor((10 + Math.random() * 20) * qualityMultiplier);
                    break;
                case 'attack':
                    value = Math.floor((3 + Math.random() * 7) * qualityMultiplier);
                    break;
                case 'defense':
                    value = Math.floor((2 + Math.random() * 6) * qualityMultiplier);
                    break;
                case 'speed':
                    value = Math.floor((1 + Math.random() * 3) * qualityMultiplier);
                    break;
                case 'critRate':
                case 'dodgeRate':
                    value = parseFloat((0.01 + Math.random() * 0.04) * qualityMultiplier).toFixed(3);
                    value = parseFloat(value); // 转回数字
                    break;
                case 'critDamage':
                case 'hpBonus':
                case 'attackBonus':
                case 'defenseBonus':
                case 'damageBonus':
                case 'healEffect':
                case 'damageReduction':
                    value = parseFloat((0.03 + Math.random() * 0.07) * qualityMultiplier).toFixed(3);
                    value = parseFloat(value); // 转回数字
                    break;
                case 'penetration':
                    value = Math.floor((2 + Math.random() * 5) * qualityMultiplier);
                    break;
                case 'daoRule':
                    value = Math.floor((1 + Math.random() * 4) * qualityMultiplier);
                    break;
                default:
                    value = Math.floor((1 + Math.random() * 5) * qualityMultiplier);
            }

            // 添加到随机属性列表
            randomAttributes.push({
                type: selectedType,
                value: value
            });
        }

        return randomAttributes;
    }

    // 显示锻造概率
    showProbabilities() {
        let message = "装备锻造概率：\n";

        for (const [quality, probability] of Object.entries(this.probabilities)) {
            message += `${ITEM_QUALITY_NAMES[quality]}: ${probability * 100}%\n`;
        }

        message += `\n保底机制：每${this.pityThreshold}次必出传说品质装备`;
        message += `\n当前已锻造${this.pityCounter}次，距离保底还剩${this.pityThreshold - this.pityCounter}次`;

        console.log(message);
        // 直接在当前场景显示对话框
        this.showInfoDialog("锻造概率", message);
    }

    // 新增方法：在画布上显示错误消息
    showErrorMessage(message) {
        // 保存当前场景，在下一帧绘制错误消息
        this.errorMessage = message;
        this.errorMessageTime = Date.now();

        // 3秒后自动清除消息
        setTimeout(() => {
            this.errorMessage = null;
        }, 3000);
    }

    // 新增方法：显示信息对话框
    showInfoDialog(title, message) {
        // 设置对话框状态
        this.dialogTitle = title;
        this.dialogMessage = message;
        this.showingDialog = true;

        // 创建确定按钮
        const okButton = new Button(
            this.ctx,
            this.screenWidth / 2 - 50,
            this.screenHeight / 2 + 150,
            100,
            40,
            '确定',
            null,
            null,
            () => {
                this.showingDialog = false;
                this.dialogTitle = null;
                this.dialogMessage = null;
                this.initUI(); // 重新初始化UI
            }
        );

        // 清除当前UI并添加确定按钮
        this.clearUIElements();
        this.addUIElement(okButton);
    }

    // 绘制场景
    drawScene() {
        // 绘制背景
        this.drawBackground();

        // 标题
        this.ctx.font = '28px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.title, this.screenWidth / 2, 30);

        // 如果显示对话框，绘制对话框
        if (this.showingDialog && this.dialogTitle && this.dialogMessage) {
            this.drawDialog();
            return; // 对话框显示时不绘制其他内容
        }

        // 绘制锻造结果或锻造界面
        if (this.showingResults) {
            this.displayForgeResults();
        } else {
            // 显示当前兽材数量
            const gameStateManager = AppContext.game.gameStateManager;
            const player = gameStateManager.getPlayer();

            // 获取玩家境界决定装备阶级
            const mainCharacter = AppContext.game.gameStateManager.getCharacterById(1);
            const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

            // 决定显示的兽材类型
            const useTier2Material = playerRealm.includes('筑基期');
            const materialName = useTier2Material ? '二阶兽材' : '一阶兽材';

            // 获取兽材数量
            const beastMaterialCount = this.getBeastMaterialCount(player, materialName);

            this.ctx.font = '24px Arial';
            this.ctx.fillStyle = '#fff';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`${materialName}: ${beastMaterialCount}`, this.screenWidth / 2, 50);

            // 保底计数
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`当前已锻造${this.pityCounter}次，距离保底还剩${this.pityThreshold - this.pityCounter}次`,
                             this.screenWidth / 2, this.screenHeight / 2 + 100);
        }

        // 如果有错误消息，显示错误消息
        if (this.errorMessage) {
            this.drawErrorMessage();
        }
    }

    // 绘制对话框
    drawDialog() {
        // 半透明背景
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

        // 对话框背景
        const dialogWidth = 300;
        const dialogHeight = 400;
        const dialogX = (this.screenWidth - dialogWidth) / 2;
        const dialogY = (this.screenHeight - dialogHeight) / 2;

        this.ctx.fillStyle = '#222';
        this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);

        // 对话框边框
        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);

        // 标题
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.dialogTitle, this.screenWidth / 2, dialogY + 30);

        // 分隔线
        this.ctx.beginPath();
        this.ctx.moveTo(dialogX + 20, dialogY + 45);
        this.ctx.lineTo(dialogX + dialogWidth - 20, dialogY + 45);
        this.ctx.stroke();

        // 消息内容
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';

        const lines = this.dialogMessage.split('\n');
        let y = dialogY + 80;

        lines.forEach(line => {
            this.ctx.fillText(line, dialogX + 20, y);
            y += 25;
        });
    }

    // 绘制错误消息
    drawErrorMessage() {
        // 计算消息框位置
        const msgWidth = 200;
        const msgHeight = 40;
        const msgX = (this.screenWidth - msgWidth) / 2;
        const msgY = this.screenHeight - msgHeight - 100;

        // 绘制半透明背景
        this.ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
        this.ctx.fillRect(msgX, msgY, msgWidth, msgHeight);

        // 绘制边框
        this.ctx.strokeStyle = 'white';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(msgX, msgY, msgWidth, msgHeight);

        // 绘制文本
        this.ctx.font = '16px Arial';
        this.ctx.fillStyle = 'white';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(this.errorMessage, msgX + msgWidth / 2, msgY + msgHeight / 2);
    }

    // 显示锻造结果
    displayForgeResults() {
        // 标题
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`锻造结果`, this.screenWidth / 2, 50);

        const startY = 100;
        const itemHeight = 40;

        this.ctx.textAlign = 'center';

        this.forgeResults.forEach((result, index) => {
            const y = startY + index * itemHeight;

            // 设置不同品质的颜色
            this.ctx.fillStyle = ITEM_QUALITY_COLORS[result.quality] || '#FFFFFF';
            this.ctx.font = '18px Arial';

            // 获取主要属性文本
            let attributeText = '';
            if (result.type === ITEM_TYPES.WEAPON) {
                attributeText = `攻击力 +${result.attributes.attack}`;
            } else if (result.type === ITEM_TYPES.ARMOR) {
                attributeText = `生命值 +${result.attributes.hp}, 防御 +${result.attributes.defense}`;
            } else if (result.type === ITEM_TYPES.ACCESSORY) {
                attributeText = `生命值 +${result.attributes.hp || 0}, 速度 +${result.attributes.speed || 0}`;
            } else if (result.type === ITEM_TYPES.ARTIFACT) {
                attributeText = `攻击 +${result.attributes.attack || 0}, 防御 +${result.attributes.defense || 0}`;
            }

            // 显示装备名称、阶级和主要属性
            const tierText = result.tier > 1 ? `[阶级${result.tier}]` : '';
            this.ctx.fillText(`${result.name} ${tierText} [${ITEM_QUALITY_NAMES[result.quality]}] (${attributeText})`,
                           this.screenWidth / 2, y);

            // 如果有随机属性，显示随机属性数量
            if (result.randomAttributes && result.randomAttributes.length > 0) {
                this.ctx.font = '14px Arial';
                this.ctx.fillText(`随机属性: ${result.randomAttributes.length}条`,
                               this.screenWidth / 2, y + 20);
            }
        });
    }

    // 绘制背景
    drawBackground() {
        // 绘制一个渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
        gradient.addColorStop(0, '#1a2a6c');  // 顶部颜色
        gradient.addColorStop(0.5, '#b21f1f'); // 中间颜色
        gradient.addColorStop(1, '#fdbb2d');   // 底部颜色

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

        // 绘制简单装饰元素
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';

        // 顶部装饰条
        this.ctx.fillRect(0, 40, this.screenWidth, 2);

        // 底部装饰条
        this.ctx.fillRect(0, this.screenHeight - 70, this.screenWidth, 2);
    }

    // 获取兽材数量
    getBeastMaterialCount(player, materialName) {
        // 使用 gameStateManager 获取最新的物品列表
        const items = AppContext.game.gameStateManager.getItems();
        if (!items || items.length === 0) return 0;

        // 查找兽材物品
        const material = items.find(item => item.name === materialName);
        return material ? material.count : 0;
    }

    // 消耗兽材
    consumeBeastMaterial(player, materialName, amount) {
        // 使用 gameStateManager 获取最新的物品列表
        const items = AppContext.game.gameStateManager.getItems();
        if (!items || items.length === 0) return;

        // 查找兽材物品
        const material = items.find(item => item.name === materialName);
        if (!material) return;

        // 减少数量
        material.count -= amount;

        // 如果数量为0，移除物品
        if (material.count <= 0) {
            AppContext.game.gameStateManager.removeItem(material.id);
        } else {
            // 更新物品
            AppContext.game.gameStateManager.updateItem(material.id, material);
        }
    }
}

// 将ForgeScene导出为默认导出
export default ForgeScene;