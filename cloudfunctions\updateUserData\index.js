// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

const db = cloud.database()
const xiuxianCollection = db.collection('xiuxian')

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      error: 'No openid found'
    }
  }
  
  // 获取数据更新内容
  const { gameState } = event
  
  if (!gameState) {
    return {
      success: false,
      error: 'No game state data provided'
    }
  }
  
  try {
    // 查询用户记录是否存在
    const userRecord = await xiuxianCollection.where({
      _openid: openid
    }).get()
    
    if (userRecord.data.length === 0) {
      // 如果用户记录不存在，创建新记录
      await xiuxianCollection.add({
        data: {
          _openid: openid,
          gameState: gameState,
          createdAt: db.serverDate(),
          updatedAt: db.serverDate()
        }
      })
      
      return {
        success: true,
        message: 'Created new user data record'
      }
    } else {
      // 如果用户记录存在，更新记录
      const userId = userRecord.data[0]._id
      
      await xiuxianCollection.doc(userId).update({
        data: {
          gameState: gameState,
          updatedAt: db.serverDate()
        }
      })
      
      return {
        success: true,
        message: 'Updated user data'
      }
    }
  } catch (error) {
    console.error('Error updating user data:', error)
    return {
      success: false,
      error: error.message
    }
  }
} 