/**
 * 装备选择场景
 * 用于选择角色的装备
 */
import BaseScene from './BaseScene.js';
import Button from '../ui/Button.js';
import AppContext from '../utils/AppContext.js';

export class EquipmentSelectScene extends BaseScene {
    constructor(ctx, screenWidth, screenHeight, sceneManager) {
        super(ctx, screenWidth, screenHeight, sceneManager);
        this.name = 'equipmentSelect';
        this.title = '选择装备';

        // 当前角色和装备槽
        this.character = null;
        this.slotType = null;

        // 分页控制
        this.itemsPerPage = 5;
        this.currentPage = 0;

        // 过滤条件
        this.filter = 'all'; // 'all', 'weapon', 'armor', 'accessory', 'artifact'
    }

    // 场景显示时的回调
    onShow(params) {
        console.log('EquipmentSelectScene.onShow被调用，参数：', params);

        // 从参数中获取角色和装备槽类型
        if (params && params.character && params.slotType) {
            this.character = params.character;
            this.slotType = params.slotType.toLowerCase();

            // 设置初始过滤器为当前槽类型
            switch (this.slotType) {
                case '武器':
                    this.filter = 'weapon';
                    break;
                case '护甲':
                    this.filter = 'armor';
                    break;
                case '饰品':
                    this.filter = 'accessory';
                    break;
                case '灵宝':
                    this.filter = 'artifact';
                    break;
                default:
                    this.filter = 'all';
            }
        } else {
            console.error('未能获取角色或装备槽信息!');
            this.sceneManager.showScene('characterInfo');
            return;
        }

        // 初始化UI
        this.initUI();
        this.visible = true;
    }

    // 场景隐藏时的回调
    onHide() {
        // 清空UI元素
        this.clearUIElements();

        // 设置场景为不可见
        this.visible = false;

        console.log('EquipmentSelectScene隐藏');
    }

    // 初始化UI
    initUI() {
        // 清空之前的UI元素
        this.clearUIElements();

        // 返回按钮
        this.initBackButton();

        // 过滤按钮
        this.initFilterButtons();

        // 装备列表
        this.initEquipmentList();

        // 分页按钮
        this.initPaginationButtons();
    }

    // 初始化返回按钮
    initBackButton() {
        const backButton = new Button(
            this.ctx,
            10,
            10,
            80,
            30,
            '返回',
            null,
            null,
            () => {
                if (this.character && this.character.id) {
                    this.sceneManager.showScene('characterDetail', { characterId: this.character.id });
                } else {
                    this.sceneManager.showScene('character');
                }
            }
        );
        this.addUIElement(backButton);
    }

    // 初始化过滤按钮
    initFilterButtons() {
        const buttonWidth = 80;
        const buttonHeight = 30;
        const buttonSpacing = 10;
        const startX = 10;
        const startY = 50;

        // 全部按钮
        const allButton = new Button(
            this.ctx,
            startX,
            startY,
            buttonWidth,
            buttonHeight,
            '全部',
            null,
            this.filter === 'all' ? 'rgba(85, 85, 85, 1)' : 'rgba(51, 51, 51, 1)',
            () => {
                this.filter = 'all';
                this.currentPage = 0;
                this.initUI();
            }
        );
        this.addUIElement(allButton);

        // 武器按钮
        const weaponButton = new Button(
            this.ctx,
            startX + buttonWidth + buttonSpacing,
            startY,
            buttonWidth,
            buttonHeight,
            '武器',
            null,
            this.filter === 'weapon' ? 'rgba(85, 85, 85, 1)' : 'rgba(51, 51, 51, 1)',
            () => {
                this.filter = 'weapon';
                this.currentPage = 0;
                this.initUI();
            }
        );
        this.addUIElement(weaponButton);

        // 护甲按钮
        const armorButton = new Button(
            this.ctx,
            startX + (buttonWidth + buttonSpacing) * 2,
            startY,
            buttonWidth,
            buttonHeight,
            '护甲',
            null,
            this.filter === 'armor' ? 'rgba(85, 85, 85, 1)' : 'rgba(51, 51, 51, 1)',
            () => {
                this.filter = 'armor';
                this.currentPage = 0;
                this.initUI();
            }
        );
        this.addUIElement(armorButton);

        // 饰品按钮
        const accessoryButton = new Button(
            this.ctx,
            startX + (buttonWidth + buttonSpacing) * 3,
            startY,
            buttonWidth,
            buttonHeight,
            '饰品',
            null,
            this.filter === 'accessory' ? 'rgba(85, 85, 85, 1)' : 'rgba(51, 51, 51, 1)',
            () => {
                this.filter = 'accessory';
                this.currentPage = 0;
                this.initUI();
            }
        );
        this.addUIElement(accessoryButton);

        // 灵宝按钮
        const artifactButton = new Button(
            this.ctx,
            startX + (buttonWidth + buttonSpacing) * 4,
            startY,
            buttonWidth,
            buttonHeight,
            '灵宝',
            null,
            this.filter === 'artifact' ? 'rgba(85, 85, 85, 1)' : 'rgba(51, 51, 51, 1)',
            () => {
                this.filter = 'artifact';
                this.currentPage = 0;
                this.initUI();
            }
        );
        this.addUIElement(artifactButton);
    }

    // 初始化装备列表
    initEquipmentList() {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();
        let items = player.items || [];

        // 根据过滤条件过滤装备
        if (this.filter !== 'all') {
            items = items.filter(item => item.type === this.filter);
        }

        // 根据槽类型筛选可装备的物品
        let slotType = this.slotType.toLowerCase();
        const slotTypeMap = {
            '武器': 'weapon',
            '护甲': 'armor',
            '饰品': 'accessory',
            '灵宝': 'artifact'
        };

        const targetType = slotTypeMap[this.slotType];
        if (targetType) {
            items = items.filter(item => item.type === targetType);
        }

        this.filteredItems = items;

        // 计算当前页的物品
        const startIndex = this.currentPage * this.itemsPerPage;
        const pageItems = items.slice(startIndex, startIndex + this.itemsPerPage);

        // 创建装备列表项按钮
        const startY = 100;
        const itemHeight = 80;
        const itemSpacing = 10;

        // 添加卸下装备选项
        const unequipButton = new Button(
            this.ctx,
            20,
            startY,
            this.screenWidth - 40,
            itemHeight,
            '卸下装备',
            null,
            'rgba(51, 51, 51, 1)',
            () => this.unequipItem()
        );
        this.addUIElement(unequipButton);

        // 添加物品列表
        pageItems.forEach((item, index) => {
            const y = startY + (index + 1) * (itemHeight + itemSpacing);

            const itemButton = new EquipmentItemButton(
                this.ctx,
                20,
                y,
                this.screenWidth - 40,
                itemHeight,
                item,
                () => this.equipItem(item)
            );

            this.addUIElement(itemButton);
        });
    }

    // 初始化分页按钮
    initPaginationButtons() {
        const totalPages = Math.ceil(this.filteredItems.length / this.itemsPerPage);

        // 如果没有物品或只有一页，不显示分页按钮
        if (totalPages <= 1) return;

        const buttonWidth = 50;
        const buttonHeight = 40;
        const buttonSpacing = 10;
        const startX = this.screenWidth / 2 - buttonWidth - buttonSpacing / 2;
        const y = this.screenHeight - 60;

        // 上一页按钮
        if (this.currentPage > 0) {
            const prevButton = new Button(
                this.ctx,
                startX - buttonWidth - buttonSpacing,
                y,
                buttonWidth,
                buttonHeight,
                '上一页',
                null,
                null,
                () => {
                    this.currentPage--;
                    this.initUI();
                }
            );
            this.addUIElement(prevButton);
        }

        // 页码显示
        const pageInfoButton = new Button(
            this.ctx,
            startX,
            y,
            buttonWidth * 2 + buttonSpacing,
            buttonHeight,
            `${this.currentPage + 1}/${totalPages}`,
            null,
            null,
            () => {}  // 无操作
        );
        this.addUIElement(pageInfoButton);

        // 下一页按钮
        if (this.currentPage < totalPages - 1) {
            const nextButton = new Button(
                this.ctx,
                startX + buttonWidth * 2 + buttonSpacing * 2,
                y,
                buttonWidth,
                buttonHeight,
                '下一页',
                null,
                null,
                () => {
                    this.currentPage++;
                    this.initUI();
                }
            );
            this.addUIElement(nextButton);
        }
    }

    // 装备物品
    equipItem(item) {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();

        // 确保角色装备对象存在
        if (!this.character.equipment) {
            this.character.equipment = {};
        }

        // 确定装备槽
        const slotTypeMap = {
            '武器': 'weapon',
            '护甲': 'armor',
            '饰品': 'accessory',
            '灵宝': 'artifact'
        };
        const equipSlot = slotTypeMap[this.slotType];

        // 如果装备类型与槽类型不匹配，显示错误
        if (item.type !== equipSlot) {
            if (typeof wx !== 'undefined' && wx.showToast) {
                wx.showToast({
                    title: '装备类型不匹配！',
                    icon: 'none',
                    duration: 2000
                });
            }
            return;
        }

        // 检查装备境界要求
        if (item.tier > 1 && item.requiredRealm) {
            // 获取角色境界
            const characterRealm = this.character.cultivation || '';

            // 境界比较函数
            const compareRealms = (realm1, realm2) => {
                // 境界等级排序
                const realmOrder = [
                    '练气期', '筑基期', '金丹期', '元婴期',
                    '化神期', '返虚期', '合道期', '渡劫期', '大乘期'
                ];

                // 提取基础境界
                const getBaseRealm = (realm) => {
                    for (const baseRealm of realmOrder) {
                        if (realm.includes(baseRealm)) {
                            return baseRealm;
                        }
                    }
                    return realm;
                };

                // 获取境界索引
                const realm1Index = realmOrder.indexOf(getBaseRealm(realm1));
                const realm2Index = realmOrder.indexOf(getBaseRealm(realm2));

                // 如果找不到境界，返回-1
                if (realm1Index === -1 || realm2Index === -1) return false;

                // 比较境界等级
                return realm1Index >= realm2Index;
            };

            // 检查角色境界是否达到装备要求
            if (!compareRealms(characterRealm, item.requiredRealm)) {
                if (typeof wx !== 'undefined' && wx.showToast) {
                    wx.showToast({
                        title: `需要${item.requiredRealm}境界才能装备！`,
                        icon: 'none',
                        duration: 2000
                    });
                } else {
                    console.error(`需要${item.requiredRealm}境界才能装备！`);
                }
                return;
            }
        }

        // 如果已经装备了物品，先卸下旧装备
        const oldEquipment = this.character.equipment[equipSlot];
        if (oldEquipment) {
            // 将旧装备放回背包
            player.items.push(oldEquipment);
        }

        // 从背包中移除物品
        const itemIndex = player.items.findIndex(i => i.id === item.id);
        if (itemIndex !== -1) {
            player.items.splice(itemIndex, 1);
        }

        // 装备物品
        this.character.equipment[equipSlot] = item;

        // 更新角色属性和战力
        this.updateCharacterAttributes();

        // 保存玩家数据
        gameStateManager.setPlayer(player);

        // 返回角色信息页面
        if (this.character && this.character.id) {
            this.sceneManager.showScene('characterDetail', { characterId: this.character.id });
        } else {
            console.error('装备完成后无法返回角色页面：缺少角色ID');
            this.sceneManager.showScene('character');
        }
    }

    // 卸下装备
    unequipItem() {
        const gameStateManager = AppContext.game.gameStateManager;
        const player = gameStateManager.getPlayer();

        // 确定装备槽
        const slotTypeMap = {
            '武器': 'weapon',
            '护甲': 'armor',
            '饰品': 'accessory',
            '灵宝': 'artifact'
        };
        const equipSlot = slotTypeMap[this.slotType];

        // 如果没有装备，直接返回
        if (!this.character.equipment || !this.character.equipment[equipSlot]) {
            if (typeof wx !== 'undefined' && wx.showToast) {
                wx.showToast({
                    title: '没有已装备的物品！',
                    icon: 'none',
                    duration: 2000
                });
            }
            return;
        }

        // 获取装备
        const equipment = this.character.equipment[equipSlot];

        // 将装备放回背包
        player.items.push(equipment);

        // 卸下装备
        this.character.equipment[equipSlot] = null;

        // 更新角色属性和战力
        this.updateCharacterAttributes();

        // 保存玩家数据
        gameStateManager.setPlayer(player);

        // 返回角色信息页面
        if (this.character && this.character.id) {
            this.sceneManager.showScene('characterDetail', { characterId: this.character.id });
        } else {
            console.error('卸下装备后无法返回角色页面：缺少角色ID');
            this.sceneManager.showScene('character');
        }
    }

    // 更新角色属性
    updateCharacterAttributes() {
        // 更新角色数据
        const gameStateManager = AppContext.game.gameStateManager;
        const characters = gameStateManager.getCharacters();
        const charIndex = characters.findIndex(c => c.id === this.character.id);
        if (charIndex !== -1) {
            // 使用updateCharacter方法而不是setCharacters
            gameStateManager.updateCharacter(this.character.id, this.character);

            // 计算角色战力
            if (this.character.calculatePower && typeof this.character.calculatePower === 'function') {
                this.character.power = this.character.calculatePower();
            } else if (this.character.getAttributes && typeof this.character.getAttributes === 'function') {
                // 简单战力计算
                const attrs = this.character.getAttributes();
                this.character.power = Math.floor(
                    (attrs.hp || 0) * 0.1 +
                    (attrs.attack || 0) * 1.5 +
                    (attrs.defense || 0) * 1.0 +
                    (attrs.speed || 0) * 0.8
                );
            }

            console.log(`角色 ${this.character.name} 的装备已更新，战力: ${this.character.power}`);
        } else {
            console.error(`找不到ID为 ${this.character.id} 的角色！`);
        }
    }

    // 绘制场景
    drawScene() {
        // 绘制背景
        this.drawBackground();

        // 标题
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`选择${this.slotType}`, this.screenWidth / 2, 30);

        // 显示过滤和总数
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`总计: ${this.filteredItems ? this.filteredItems.length : 0} 装备`, this.screenWidth - 20, 70);

        // 如果没有装备，显示提示
        if (!this.filteredItems || this.filteredItems.length === 0) {
            this.ctx.font = '20px Arial';
            this.ctx.fillStyle = '#aaa';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`暂无可装备的${this.slotType}`, this.screenWidth / 2, 200);
        }
    }

    // 绘制背景
    drawBackground() {
        // 创建渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
        gradient.addColorStop(0, '#1a2a3d');
        gradient.addColorStop(1, '#0d1824');

        // 填充背景
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    }
}

// 自定义装备列表项按钮类
class EquipmentItemButton extends Button {
    constructor(ctx, x, y, width, height, item, onClick) {
        super(ctx, x, y, width, height, '', null, null, onClick);
        this.item = item;
        this.isEquipmentItem = true;
    }

    render() {
        const ctx = this.ctx;
        const item = this.item;
        const qualityColors = {
            0: '#CCCCCC', // 普通 (灰色)
            1: '#55AA55', // 良好 (绿色)
            2: '#5555AA', // 稀有 (蓝色)
            3: '#AA55AA', // 史诗 (紫色)
            4: '#AAAA55'  // 传说 (金色)
        };
        const qualityColor = qualityColors[item.quality] || '#FFFFFF';

        // 绘制背景
        ctx.fillStyle = this.isHovered ? '#444455' : '#333344';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 绘制边框
        ctx.strokeStyle = qualityColor;
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);

        // 绘制物品名称（左侧）
        ctx.font = '18px Arial';
        ctx.fillStyle = qualityColor;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${item.name} (+${item.level})`, this.x + 20, this.y + 20);

        // 绘制物品描述（左侧，第二行）
        ctx.font = '14px Arial';
        ctx.fillStyle = '#DDDDDD';
        ctx.fillText(item.description || '无描述', this.x + 20, this.y + 45);

        // 绘制物品属性（右侧）
        ctx.textAlign = 'right';
        ctx.fillStyle = '#88FF88';

        // 显示主要属性加成
        let attributesText = '';

        if (item.attributes) {
            if (item.attributes.hp) attributesText += `生命+${item.attributes.hp} `;
            if (item.attributes.attack) attributesText += `攻击+${item.attributes.attack} `;
            if (item.attributes.defense) attributesText += `防御+${item.attributes.defense} `;
            if (item.attributes.speed) attributesText += `速度+${item.attributes.speed} `;
        }

        ctx.fillText(attributesText, this.x + this.width - 20, this.y + 30);

        // 显示特殊属性（如果有）
        let specialText = '';

        if (item.attributes) {
            if (item.attributes.critRate) specialText += `暴击率+${(item.attributes.critRate * 100).toFixed(1)}% `;
            if (item.attributes.critDamage) specialText += `暴伤+${(item.attributes.critDamage * 100).toFixed(1)}% `;
            if (item.attributes.penetration) specialText += `破防+${item.attributes.penetration} `;
        }

        if (specialText) {
            ctx.fillStyle = '#FFAA55';
            ctx.fillText(specialText, this.x + this.width - 20, this.y + 55);
        }
    }
}

export default EquipmentSelectScene;