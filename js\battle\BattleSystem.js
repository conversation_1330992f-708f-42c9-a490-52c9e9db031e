/**
 * 战斗系统核心类
 * 负责管理整个战斗流程，包括布阵、战斗和结算
 */
import BattleFormationScene from './scenes/BattleFormationScene';
import BattleScene from './scenes/BattleScene';
import BattleResultScene from './scenes/BattleResultScene';
import AppContext from '../utils/AppContext';

class BattleSystem {
  constructor() {
    this.ctx = null;
    this.screenWidth = 0;
    this.screenHeight = 0;
    this.sceneManager = null;
    
    // 战斗相关场景
    this.formationScene = null;
    this.battleScene = null;
    this.resultScene = null;
    
    // 战斗相关数据
    this.stageData = null;     // 关卡数据
    this.playerFormation = []; // 玩家阵容
    this.enemyFormation = [];  // 敌方阵容
    this.battleResult = null;  // 战斗结果
    this.rewards = null;       // 战斗奖励
  }
  
  /**
   * 初始化战斗系统
   * @param {Object} ctx 画布上下文
   * @param {number} screenWidth 屏幕宽度
   * @param {number} screenHeight 屏幕高度
   * @param {Object} sceneManager 场景管理器
   */
  init(ctx, screenWidth, screenHeight, sceneManager) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.sceneManager = sceneManager;
    
    // 初始化战斗相关场景
    this.formationScene = new BattleFormationScene(ctx, screenWidth, screenHeight, sceneManager);
    this.battleScene = new BattleScene(ctx, screenWidth, screenHeight, sceneManager);
    this.resultScene = new BattleResultScene(ctx, screenWidth, screenHeight, sceneManager);
    
    // 注册场景到场景管理器
    sceneManager.registerScene('battleFormation', this.formationScene);
    sceneManager.registerScene('battle', this.battleScene);
    sceneManager.registerScene('battleResult', this.resultScene);
    
    console.log('战斗系统初始化完成');
  }
  
  /**
   * 开始战斗流程
   * @param {Object} stageData 关卡数据，包含敌方阵容和奖励信息
   * @param {Function} onComplete 战斗完成后的回调
   */
  startBattle(stageData, onComplete) {
    console.log('开始战斗流程，关卡数据:', stageData);
    
    // 保存关卡数据
    this.stageData = stageData;
    this.enemyFormation = stageData.enemies || [];
    this.rewards = stageData.rewards || {};
    this.onBattleComplete = onComplete || null;
    
    // 切换到布阵场景
    this.sceneManager.showScene('battleFormation', {
      enemyFormation: this.enemyFormation,
      stageData: this.stageData,
      onFormationComplete: this.onFormationComplete.bind(this)
    });
  }
  
  /**
   * 布阵完成回调
   * @param {Array} formation 玩家选择的阵容
   */
  onFormationComplete(formation) {
    console.log('布阵完成，玩家阵容:', formation);
    
    // 保存玩家阵容
    this.playerFormation = formation;
    
    // 切换到战斗场景
    this.sceneManager.showScene('battle', {
      playerFormation: this.playerFormation,
      enemyFormation: this.enemyFormation,
      stageData: this.stageData,
      onBattleEnd: this.onBattleEnd.bind(this)
    });
  }
  
  /**
   * 战斗结束回调
   * @param {Object} result 战斗结果
   */
  onBattleEnd(result) {
    console.log('战斗结束，结果:', result);
    
    // 保存战斗结果
    this.battleResult = result;
    
    // 如果战斗胜利，显示结算场景
    if (result === 'victory') {
      console.log('战斗胜利，显示结算场景');
      
      // 确保奖励数据存在
      if (!this.rewards || Object.keys(this.rewards).length === 0) {
        console.log('未找到预设奖励，设置默认奖励');
        this.rewards = {
          exp: Math.floor(50 + Math.random() * 20),
          lingshi: Math.floor(100 + Math.random() * 50)
        };
        
        // 根据关卡类型添加额外奖励
        if (this.stageData && this.stageData.type) {
          console.log(`根据关卡类型 ${this.stageData.type} 添加特定奖励`);
          
          switch (this.stageData.type) {
            case 'trial':
              // 试炼关卡额外奖励
              this.rewards.exp = Math.floor(this.rewards.exp * 1.2);
              this.rewards.lingshi = Math.floor(this.rewards.lingshi * 1.5);
              break;
            case 'boss':
              // Boss关卡额外奖励
              this.rewards.exp = Math.floor(this.rewards.exp * 2);
              this.rewards.lingshi = Math.floor(this.rewards.lingshi * 2);
              this.rewards.xianyu = Math.floor(5 + Math.random() * 10); // 额外添加仙玉
              break;
            case 'story':
              // 剧情关卡额外奖励
              this.rewards.exp = Math.floor(this.rewards.exp * 1.5);
              break;
          }
        }
      }
      
      console.log('最终战斗奖励:', this.rewards);
      
      // 根据关卡ID和类型, 尝试使用云函数发放奖励
      this.sendRewardsToPlayer(this.rewards);
      
      // 显示结算场景
      this.sceneManager.showScene('battleResult', {
        result: { victory: true },
        rewards: this.rewards,
        onComplete: this.onResultComplete.bind(this)
      });
    } else {
      // 战斗失败，直接返回
      console.log('战斗失败，直接执行回调');
      if (this.onBattleComplete) {
        this.onBattleComplete({
          victory: false,
          rewards: {}
        });
      }
    }
  }
  
  /**
   * 发送奖励到玩家账号
   * @param {Object} rewards 奖励数据
   */
  sendRewardsToPlayer(rewards) {
    if (!rewards) {
      console.warn('奖励数据为空');
      return;
    }
    
    // 检查云环境是否初始化
    if (!window.game || !window.game.cloudInited) {
      console.error('云环境未初始化，无法通过云函数发放奖励');
      return;
    }
    
    // 获取玩家OPENID
    const openid = window.game.user?.openid;
    if (!openid) {
      console.error('未找到玩家OPENID，无法通过云函数发放奖励');
      return;
    }
    
    console.log(`尝试使用云函数给玩家 ${openid} 发放奖励:`, rewards);
    
    // 生成奖励发放原因
    let reason = '战斗胜利';
    if (this.stageData) {
      reason = `${this.stageData.type || ''}关卡胜利: ${this.stageData.name || '未知关卡'}`;
    }
    
    // 调用云函数发放奖励
    window.game.cloud.callFunction({
      name: 'giveReward',
      data: {
        targetOpenid: openid,
        rewards: rewards,
        reason: reason
      },
      success: res => {
        console.log('云函数奖励发放成功:', res);
      },
      fail: err => {
        console.error('调用云函数发放奖励失败:', err);
      }
    });
  }
  
  /**
   * 结算完成回调
   */
  onResultComplete() {
    console.log('结算完成');
    
    // 调用战斗完成回调
    if (this.onBattleComplete) {
      this.onBattleComplete({
        victory: true,
        rewards: this.rewards
      });
    }
  }
  
  /**
   * 清理战斗数据
   */
  clear() {
    this.stageData = null;
    this.playerFormation = [];
    this.enemyFormation = [];
    this.battleResult = null;
    this.rewards = null;
    this.onBattleComplete = null;
  }
}

// 导出战斗系统单例
const battleSystem = new BattleSystem();
export default battleSystem; 