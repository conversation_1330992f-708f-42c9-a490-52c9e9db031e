/**
 * 加载组件
 */
class Loading {
  constructor(ctx, screenWidth, screenHeight) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    
    // 加载状态
    this.visible = false;
    this.progress = 0;
    this.text = '加载中...';
    
    // 动画相关
    this.rotation = 0;
  }
  
  // 显示加载界面
  show(text) {
    this.visible = true;
    this.text = text || '加载中...';
    this.progress = 0;
    this.rotation = 0;
  }
  
  // 隐藏加载界面
  hide() {
    this.visible = false;
  }
  
  // 更新加载进度
  updateProgress(progress) {
    this.progress = progress;
  }
  
  // 更新动画
  update() {
    // 更新旋转角度
    this.rotation += 0.1;
    if (this.rotation >= Math.PI * 2) {
      this.rotation = 0;
    }
  }
  
  // 绘制加载界面
  render() {
    if (!this.visible) {
      return;
    }
    
    // 更新动画
    this.update();
    
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制加载文本
    this.ctx.font = '24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.text, this.screenWidth / 2, this.screenHeight / 2 - 50);
    
    // 绘制进度条
    const progressBarWidth = 300;
    const progressBarHeight = 20;
    const progressBarX = (this.screenWidth - progressBarWidth) / 2;
    const progressBarY = this.screenHeight / 2;
    
    // 绘制进度条背景
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);
    
    // 绘制进度条进度
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth * this.progress, progressBarHeight);
    
    // 绘制进度文本
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${Math.floor(this.progress * 100)}%`, this.screenWidth / 2, progressBarY + 40);
    
    // 绘制加载动画
    this.drawLoadingSpinner(this.screenWidth / 2, progressBarY + 80, 30);
  }
  
  // 绘制加载旋转动画
  drawLoadingSpinner(x, y, radius) {
    const segments = 12;
    const segmentAngle = Math.PI * 2 / segments;
    
    for (let i = 0; i < segments; i++) {
      const angle = this.rotation + i * segmentAngle;
      const alpha = 1 - (i / segments);
      
      this.ctx.beginPath();
      this.ctx.arc(
        x + Math.cos(angle) * radius,
        y + Math.sin(angle) * radius,
        5,
        0,
        Math.PI * 2
      );
      this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
      this.ctx.fill();
    }
  }
}

export default Loading; 