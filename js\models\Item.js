/**
 * 物品模型类
 */
class Item {
  constructor({
    id,
    name,
    type,
    count = 1,
    effects = {},
    description = ''
  }) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.count = count;
    this.effects = effects;
    this.description = description;
  }

  // 获取物品类型名称
  getTypeName() {
    const typeNames = {
      'consumable': '消耗品',
      'material': '材料',
      'quest': '任务物品',
      'currency': '货币'
    };

    return typeNames[this.type] || '其他';
  }

  // 使用物品
  use(target) {
    // 检查是否有库存
    if (this.count <= 0) {
      return { success: false, message: '物品数量不足' };
    }

    // 根据物品类型执行不同的使用效果
    if (this.type === 'consumable') {
      // 消耗类物品，应用效果到目标
      if (target) {
        // 应用物品效果
        const effectResult = this.applyEffects(target);

        // 减少物品数量
        this.count--;

        return { success: true, ...effectResult };
      } else {
        return { success: false, message: '需要指定使用目标' };
      }
    } else if (this.type === 'material') {
      return { success: false, message: '材料无法直接使用' };
    } else if (this.type === 'quest') {
      return { success: false, message: '任务物品无法直接使用' };
    } else {
      return { success: false, message: '该物品无法使用' };
    }
  }

  // 应用物品效果
  applyEffects(target) {
    const effectsApplied = {};

    // 遍历所有效果
    Object.keys(this.effects).forEach(effect => {
      const value = this.effects[effect];

      // 根据效果类型应用到目标
      switch (effect) {
        case 'hp':
          if (target.attributes && target.attributes.hp !== undefined) {
            target.attributes.hp += value;
            effectsApplied.hp = value;
          }
          break;
        case 'exp':
          if (typeof target.addExp === 'function') {
            const leveledUp = target.addExp(value);
            effectsApplied.exp = value;
            effectsApplied.leveledUp = leveledUp;
          }
          break;
        case 'lingli':
          if (typeof target.addLingli === 'function') {
            const cultivationChanged = target.addLingli(value);
            effectsApplied.lingli = value;
            effectsApplied.cultivationChanged = cultivationChanged;
          }
          break;
        default:
          if (target.attributes && target.attributes[effect] !== undefined) {
            target.attributes[effect] += value;
            effectsApplied[effect] = value;
          }
      }
    });

    return { effectsApplied };
  }

  // 增加物品数量
  addCount(count = 1) {
    this.count += count;
    return this.count;
  }

  // 减少物品数量
  reduceCount(count = 1) {
    this.count = Math.max(0, this.count - count);
    return this.count;
  }

  // 获取物品效果描述
  getEffectsDescription() {
    let descriptions = [];

    // 遍历效果
    Object.keys(this.effects).forEach(effect => {
      const value = this.effects[effect];

      // 根据效果类型生成描述
      switch (effect) {
        case 'hp':
          descriptions.push(`恢复生命值 ${value}`);
          break;
        case 'exp':
          descriptions.push(`增加经验值 ${value}`);
          break;
        case 'lingli':
          descriptions.push(`增加灵力 ${value}`);
          break;
        case 'attack':
          descriptions.push(`增加攻击力 ${value}`);
          break;
        case 'defense':
          descriptions.push(`增加防御力 ${value}`);
          break;
        case 'speed':
          descriptions.push(`增加速度 ${value}`);
          break;
        default:
          descriptions.push(`增加${effect} ${value}`);
      }
    });

    return descriptions.join('\n');
  }

  // 获取完整物品描述
  getFullDescription() {
    let fullDescription = this.description ? this.description + '\n\n' : '';

    // 添加物品效果描述
    const effectsDescription = this.getEffectsDescription();
    if (effectsDescription) {
      fullDescription += '效果：\n' + effectsDescription;
    }

    return fullDescription;
  }
}

export default Item;