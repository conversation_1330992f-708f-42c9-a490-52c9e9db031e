# 修仙六道 - 微信小游戏

## 项目概述
修仙六道是一款修仙角色卡牌战斗类微信小游戏，包含离线收益、角色养成、装备锻造、丹药炼制和背包等功能模块。

## 功能模块
1. **角色养成系统**：收集不同修仙角色，通过修炼提升等级和属性。
2. **装备锻造系统**：打造、强化和进阶不同部位的装备，提升角色战力。
3. **丹药炼制系统**：收集材料炼制丹药，用于提升角色属性和特殊效果。
4. **洞府系统**：建设和升级洞府，提供不同的修炼加成和离线收益。
5. **试炼系统**：挑战不同难度的试炼关卡，获取资源和奖励。
6. **背包系统**：管理收集到的物品、装备和材料。

## 主要页面
1. **首页(index)**：游戏入口页面，提供服务器选择和进入游戏功能。
2. **主页面**：展示角色和基础信息，提供各个功能模块的入口。
3. **角色页面**：展示所有拥有的角色，可查看角色详情和进行角色相关操作。
4. **角色详情页面**：查看角色属性、装备、功法和进阶等信息，进行相关操作。
5. **洞府页面**：展示洞府场景，提供洞府升级和管理功能。
6. **试炼页面**：提供各类试炼挑战，获取资源奖励。
7. **背包页面**：管理所有物品，进行使用、销售等操作。

## 资源系统
- **仙玉**：高级货币，用于高级操作和稀有资源获取。
- **灵石**：基础货币，用于日常操作和普通资源获取。
- **材料**：用于炼制丹药、锻造装备等。

## 技术实现
- 基于微信小游戏原生框架开发
- 采用模块化、组件化设计
- 实现响应式UI布局，适配不同设备
- 使用本地存储保存游戏进度 