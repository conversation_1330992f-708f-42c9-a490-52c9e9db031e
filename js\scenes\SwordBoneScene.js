/**
 * 剑骨养成场景
 * 显示剑骨等级、品阶和属性，提供升级和升阶功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import game from '../../game';
import { SWORD_BONE_LEVEL_CONFIG, SWORD_BONE_RANK_CONFIG, SWORD_BONE_EFFECTS } from '../config/SwordBoneConfig.js';

class SwordBoneScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 初始化UI
    this.initUI();
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建返回按钮
    this.createBackButton();
    
    // 创建升级按钮
    this.createUpgradeButton();
    
    // 创建升阶按钮
    this.createRankUpButton();
  }
  
  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  // 创建升级按钮
  createUpgradeButton() {
    const buttonWidth = 160;
    const buttonHeight = 50;
    const margin = 20;
    
    // 获取升级所需灵石
    let upgradeCost = 0;
    if (game.gameStateManager && game.gameStateManager.swordBoneManager) {
      upgradeCost = game.gameStateManager.swordBoneManager.getUpgradeCost() || 0;
    }
    
    // 获取玩家灵石数量
    let playerLingshi = 0;
    if (game.gameStateManager && game.gameStateManager.getPlayer()) {
      playerLingshi = game.gameStateManager.getPlayer().resources.lingshi || 0;
    }
    
    // 判断是否可以升级
    const canUpgrade = upgradeCost > 0 && playerLingshi >= upgradeCost;
    
    this.upgradeButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - buttonHeight - margin - 120,
      buttonWidth,
      buttonHeight,
      `升级 (${upgradeCost}灵石)`,
      null,
      canUpgrade ? '#4CAF50' : '#9E9E9E',
      () => {
        if (canUpgrade) {
          this.upgradeSwordBone();
        } else {
          this.showMessage(upgradeCost > 0 ? '灵石不足' : '已达到最高等级');
        }
      }
    );
    
    this.addUIElement(this.upgradeButton);
  }
  
  // 创建升阶按钮
  createRankUpButton() {
    const buttonWidth = 160;
    const buttonHeight = 50;
    const margin = 20;
    
    // 获取升阶所需仙玉
    let rankUpCost = 0;
    if (game.gameStateManager && game.gameStateManager.swordBoneManager) {
      rankUpCost = game.gameStateManager.swordBoneManager.getRankUpCost() || 0;
    }
    
    // 获取玩家仙玉数量
    let playerXianyu = 0;
    if (game.gameStateManager && game.gameStateManager.getPlayer()) {
      playerXianyu = game.gameStateManager.getPlayer().resources.xianyu || 0;
    }
    
    // 判断是否可以升阶
    const canRankUp = rankUpCost > 0 && playerXianyu >= rankUpCost;
    
    this.rankUpButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - buttonHeight - margin - 60,
      buttonWidth,
      buttonHeight,
      `升阶 (${rankUpCost}仙玉)`,
      null,
      canRankUp ? '#FF9800' : '#9E9E9E',
      () => {
        if (canRankUp) {
          this.rankUpSwordBone();
        } else {
          this.showMessage(rankUpCost > 0 ? '仙玉不足' : '已达到最高品阶');
        }
      }
    );
    
    this.addUIElement(this.rankUpButton);
  }
  
  // 升级剑骨
  upgradeSwordBone() {
    if (!game.gameStateManager || !game.gameStateManager.swordBoneManager) {
      this.showMessage('剑骨系统未初始化');
      return;
    }
    
    // 获取升级所需灵石
    const upgradeCost = game.gameStateManager.swordBoneManager.getUpgradeCost() || 0;
    if (upgradeCost <= 0) {
      this.showMessage('已达到最高等级');
      return;
    }
    
    // 获取玩家灵石数量
    const player = game.gameStateManager.getPlayer();
    const playerLingshi = player.resources.lingshi || 0;
    
    // 检查灵石是否足够
    if (playerLingshi < upgradeCost) {
      this.showMessage('灵石不足');
      return;
    }
    
    // 扣除灵石
    player.resources.lingshi -= upgradeCost;
    
    // 升级剑骨
    const success = game.gameStateManager.swordBoneManager.levelUp(upgradeCost);
    
    // 保存玩家数据
    game.gameStateManager.setPlayer(player);
    
    // 保存剑骨数据
    game.gameStateManager.saveSwordBoneData();
    
    // 显示结果
    if (success) {
      this.showMessage('剑骨升级成功！');
    } else {
      // 如果升级失败，返还灵石
      player.resources.lingshi += upgradeCost;
      game.gameStateManager.setPlayer(player);
      this.showMessage('剑骨升级失败');
    }
    
    // 刷新UI
    this.initUI();
  }
  
  // 升阶剑骨
  rankUpSwordBone() {
    if (!game.gameStateManager || !game.gameStateManager.swordBoneManager) {
      this.showMessage('剑骨系统未初始化');
      return;
    }
    
    // 获取升阶所需仙玉
    const rankUpCost = game.gameStateManager.swordBoneManager.getRankUpCost() || 0;
    if (rankUpCost <= 0) {
      this.showMessage('已达到最高品阶');
      return;
    }
    
    // 获取玩家仙玉数量
    const player = game.gameStateManager.getPlayer();
    const playerXianyu = player.resources.xianyu || 0;
    
    // 检查仙玉是否足够
    if (playerXianyu < rankUpCost) {
      this.showMessage('仙玉不足');
      return;
    }
    
    // 扣除仙玉
    player.resources.xianyu -= rankUpCost;
    
    // 升阶剑骨
    const success = game.gameStateManager.swordBoneManager.rankUp(rankUpCost);
    
    // 保存玩家数据
    game.gameStateManager.setPlayer(player);
    
    // 保存剑骨数据
    game.gameStateManager.saveSwordBoneData();
    
    // 显示结果
    if (success) {
      this.showMessage('剑骨升阶成功！');
    } else {
      // 如果升阶失败，返还仙玉
      player.resources.xianyu += rankUpCost;
      game.gameStateManager.setPlayer(player);
      this.showMessage('剑骨升阶失败');
    }
    
    // 刷新UI
    this.initUI();
  }
  
  // 显示消息对话框
  showMessage(message) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];
    
    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '提示',
      message,
      dialogButtons
    );
    
    this.addUIElement(dialog);
    dialog.show();
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();
    
    // 初始化UI
    this.initUI();
    
    // 如果剑骨未解锁，尝试解锁
    if (game.gameStateManager && game.gameStateManager.swordBoneManager && !game.gameStateManager.swordBoneManager.unlocked) {
      game.gameStateManager.swordBoneManager.unlock();
      game.gameStateManager.saveSwordBoneData();
    }
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制剑骨信息
    this.drawSwordBoneInfo();
    
    // 绘制剑骨属性
    this.drawSwordBoneAttributes();
    
    // 绘制剑骨特效
    this.drawSwordBoneEffects();
  }
  
  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a202c');
    gradient.addColorStop(1, '#2d3748');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  // 绘制标题
  drawTitle() {
    const headerHeight = 80;
    
    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('剑骨养成', this.screenWidth / 2, headerHeight / 2);
  }
  
  // 绘制剑骨信息
  drawSwordBoneInfo() {
    if (!game.gameStateManager || !game.gameStateManager.swordBoneManager) {
      return;
    }
    
    const swordBoneManager = game.gameStateManager.swordBoneManager;
    const level = swordBoneManager.level;
    const rank = swordBoneManager.rank;
    const name = swordBoneManager.getName();
    const description = swordBoneManager.getDescription();
    
    const headerHeight = 80;
    const margin = 20;
    const infoBoxHeight = 100;
    
    // 绘制信息背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, headerHeight + margin, this.screenWidth - margin * 2, infoBoxHeight);
    
    // 绘制剑骨名称和等级
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${name} (等级${level})`, this.screenWidth / 2, headerHeight + margin + 30);
    
    // 绘制剑骨描述
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.fillText(description, this.screenWidth / 2, headerHeight + margin + 60);
  }
  
  // 绘制剑骨属性
  drawSwordBoneAttributes() {
    if (!game.gameStateManager || !game.gameStateManager.swordBoneManager) {
      return;
    }
    
    const swordBoneManager = game.gameStateManager.swordBoneManager;
    const attributes = swordBoneManager.getFinalAttributes();
    
    const headerHeight = 80;
    const margin = 20;
    const infoBoxHeight = 100;
    const attributesBoxY = headerHeight + margin + infoBoxHeight + margin;
    const attributesBoxHeight = 120;
    
    // 绘制属性背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, attributesBoxY, this.screenWidth - margin * 2, attributesBoxHeight);
    
    // 绘制属性标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('剑骨属性', this.screenWidth / 2, attributesBoxY + 25);
    
    // 绘制属性值
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'left';
    
    // 左侧属性
    this.ctx.fillText(`攻击力: +${attributes.attack}`, margin + 30, attributesBoxY + 60);
    this.ctx.fillText(`暴击率: +${(attributes.critRate * 100).toFixed(1)}%`, margin + 30, attributesBoxY + 90);
    
    // 右侧属性
    this.ctx.fillText(`暴击伤害: +${(attributes.critDamage * 100).toFixed(1)}%`, this.screenWidth / 2 + 30, attributesBoxY + 60);
    this.ctx.fillText(`穿透: +${attributes.penetration}`, this.screenWidth / 2 + 30, attributesBoxY + 90);
  }
  
  // 绘制剑骨特效
  drawSwordBoneEffects() {
    if (!game.gameStateManager || !game.gameStateManager.swordBoneManager) {
      return;
    }
    
    const swordBoneManager = game.gameStateManager.swordBoneManager;
    const effects = swordBoneManager.getUnlockedEffects();
    
    const headerHeight = 80;
    const margin = 20;
    const infoBoxHeight = 100;
    const attributesBoxHeight = 120;
    const effectsBoxY = headerHeight + margin + infoBoxHeight + margin + attributesBoxHeight + margin;
    const effectItemHeight = 60;
    
    // 绘制特效标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('剑骨特效', this.screenWidth / 2, effectsBoxY + 25);
    
    // 如果没有特效，显示提示
    if (effects.length === 0) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText('提升剑骨品阶解锁特效', this.screenWidth / 2, effectsBoxY + 60);
      return;
    }
    
    // 绘制特效列表
    effects.forEach((effect, index) => {
      const effectY = effectsBoxY + 60 + index * effectItemHeight;
      
      // 绘制特效背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      this.ctx.fillRect(margin, effectY, this.screenWidth - margin * 2, effectItemHeight - 10);
      
      // 绘制特效名称
      this.ctx.font = 'bold 16px Arial';
      this.ctx.fillStyle = '#ffcc00';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(effect.name, margin + 20, effectY + 25);
      
      // 绘制特效描述
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.fillText(effect.description, margin + 20, effectY + 45);
    });
  }
}

export default SwordBoneScene;
