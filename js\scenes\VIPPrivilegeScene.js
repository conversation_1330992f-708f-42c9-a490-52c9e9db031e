/**
 * VIP特权详情场景
 * 用于展示VIP等级和特权信息
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class VIPPrivilegeScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景名称
    this.name = 'vipPrivilege';
    this.pageTitle = 'VIP特权详情';
    
    // 当前选中的VIP等级
    this.selectedVIPLevel = 0;
    
    // 按钮区域
    this.buttons = {
      back: { x: 20, y: 50, width: 80, height: 40, text: '返回' }
    };
    
    // 初始化UI
    this.initUI();
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建返回按钮
    this.backButton = new Button(
      this.ctx,
      this.buttons.back.x,
      this.buttons.back.y,
      this.buttons.back.width,
      this.buttons.back.height,
      this.buttons.back.text,
      null,
      null,
      () => {
        this.goBack();
      }
    );
    
    this.addUIElement(this.backButton);
    
    // 创建VIP等级选择按钮
    this.createVIPLevelButtons();
  }
  
  // 创建VIP等级选择按钮
  createVIPLevelButtons() {
    // 获取所有VIP等级
    const vipLevels = game.vipSystem.getAllVIPLevels();
    if (!vipLevels || vipLevels.length === 0) return;
    
    const buttonWidth = 60;
    const buttonHeight = 40;
    const startX = (this.screenWidth - (buttonWidth * vipLevels.length)) / 2;
    const y = 100;
    
    // 创建每个VIP等级的按钮
    vipLevels.forEach((vip, index) => {
      const button = new Button(
        this.ctx,
        startX + buttonWidth * index,
        y,
        buttonWidth,
        buttonHeight,
        vip.name,
        null,
        null,
        () => {
          this.selectedVIPLevel = vip.level;
        }
      );
      
      this.addUIElement(button);
    });
  }
  
  // 返回上一个场景
  goBack() {
    if (this.params && this.params.returnScene) {
      this.sceneManager.showScene(this.params.returnScene, this.params.returnParams || {});
    } else {
      this.sceneManager.showScene('recharge');
    }
  }
  
  // 场景显示时的回调
  onShow(params) {
    this.visible = true;
    
    // 保存传入的参数
    this.params = params || {};
    
    // 获取当前玩家VIP等级
    const player = game.gameStateManager.getPlayer();
    this.selectedVIPLevel = player.vipLevel || 0;
    
    // 初始化UI
    this.initUI();
  }
  
  // 处理点击事件
  handleClick(x, y) {
    // 检查是否点击了返回按钮
    if (this.isPointInRect(x, y, this.buttons.back)) {
      this.goBack();
      return;
    }
    
    // 调用父类的点击处理方法
    super.handleClick(x, y);
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制顶部导航栏
    this.drawHeader();
    
    // 绘制VIP等级选择器
    this.drawVIPLevelSelector();
    
    // 绘制VIP特权详情
    this.drawVIPPrivilegeDetails();
  }
  
  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    
    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.pageTitle, this.screenWidth / 2, headerHeight / 2 + 8);
    
    // 绘制返回按钮
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
    this.ctx.fillRect(this.buttons.back.x, this.buttons.back.y, this.buttons.back.width, this.buttons.back.height);
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      this.buttons.back.text,
      this.buttons.back.x + this.buttons.back.width / 2,
      this.buttons.back.y + this.buttons.back.height / 2 + 5
    );
  }
  
  // 绘制VIP等级选择器
  drawVIPLevelSelector() {
    // 获取所有VIP等级
    const vipLevels = game.vipSystem.getAllVIPLevels();
    if (!vipLevels || vipLevels.length === 0) return;
    
    const buttonWidth = 60;
    const buttonHeight = 40;
    const startX = (this.screenWidth - (buttonWidth * vipLevels.length)) / 2;
    const y = 100;
    
    // 绘制每个VIP等级按钮
    vipLevels.forEach((vip, index) => {
      // 绘制按钮背景
      this.ctx.fillStyle = this.selectedVIPLevel === vip.level ? 'rgba(100, 100, 200, 0.8)' : 'rgba(50, 50, 50, 0.8)';
      this.ctx.fillRect(startX + buttonWidth * index, y, buttonWidth, buttonHeight);
      
      // 绘制按钮文本
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        vip.name,
        startX + buttonWidth * index + buttonWidth / 2,
        y + buttonHeight / 2 + 5
      );
    });
    
    // 获取当前玩家VIP等级
    const player = game.gameStateManager.getPlayer();
    const currentVIPLevel = player.vipLevel || 0;
    
    // 绘制当前VIP等级提示
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#FFCC00';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `您当前的VIP等级: ${game.vipSystem.getVIPLevelInfo(currentVIPLevel).name}`,
      this.screenWidth / 2,
      y + buttonHeight + 30
    );
  }
  
  // 绘制VIP特权详情
  drawVIPPrivilegeDetails() {
    // 获取选中的VIP等级信息
    const vipInfo = game.vipSystem.getVIPLevelInfo(this.selectedVIPLevel);
    if (!vipInfo) return;
    
    const startY = 180;
    const padding = 25;
    
    // 绘制VIP等级名称
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(vipInfo.name, this.screenWidth / 2, startY);
    
    // 绘制所需充值金额
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `所需充值金额: ${vipInfo.requiredRecharge}元`,
      this.screenWidth / 2,
      startY + padding
    );
    
    // 绘制特权标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('特权内容', this.screenWidth / 2, startY + padding * 2);
    
    // 绘制特权列表
    const benefits = vipInfo.benefits;
    let y = startY + padding * 3;
    
    // 洞府修炼速度加成
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#CCCCCC';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(
      `洞府修炼速度: +${benefits.dongfuSpeedBonus * 100}%`,
      this.screenWidth / 4,
      y
    );
    
    // 游历收益加成
    this.ctx.fillText(
      `游历收益: +${benefits.idleRewardBonus * 100}%`,
      this.screenWidth * 3 / 4,
      y
    );
    
    y += padding;
    
    // 每日灵石
    this.ctx.fillText(
      `每日灵石: ${benefits.dailyLingshi}`,
      this.screenWidth / 4,
      y
    );
    
    // 每日仙玉
    this.ctx.fillText(
      `每日仙玉: ${benefits.dailyXianyu}`,
      this.screenWidth * 3 / 4,
      y
    );
    
    y += padding;
    
    // 最大体力
    this.ctx.fillText(
      `最大体力: ${benefits.maxEnergy}`,
      this.screenWidth / 4,
      y
    );
    
    // 体力恢复速率
    this.ctx.fillText(
      `体力恢复速率: ${benefits.energyRecoveryRate}倍`,
      this.screenWidth * 3 / 4,
      y
    );
    
    y += padding;
    
    // 额外每日试炼次数
    this.ctx.fillText(
      `额外每日试炼次数: ${benefits.extraDailyTrials}`,
      this.screenWidth / 2,
      y
    );
    
    // 绘制充值提示
    if (this.selectedVIPLevel > 0) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFCC00';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        `充值${vipInfo.requiredRecharge}元即可享受以上特权`,
        this.screenWidth / 2,
        this.screenHeight - 100
      );
      
      this.ctx.fillText(
        '点击返回按钮前往充值',
        this.screenWidth / 2,
        this.screenHeight - 70
      );
    }
  }
}

export default VIPPrivilegeScene;
