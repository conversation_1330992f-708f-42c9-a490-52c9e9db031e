/**
 * 功法升级场景类
 * 用于升级功法等级和星级
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import AppContext from '../utils/AppContext';

class SkillUpgradeScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = null;

    // 当前功法
    this.skill = null;
    this.skillId = null;

    // 页面状态
    this.pageTitle = '功法升级';

    // 选择的功能标签（0: 等级提升, 1: 星级提升, 2: 功法进阶）
    this.selectedTabIndex = 0;
  }

  // 初始化UI
  initUI() {
    // 尝试获取资源
    if (AppContext && AppContext.game && AppContext.game.resourceLoader) {
      this.resources = AppContext.game.resourceLoader.resources;
    }

    // 创建返回按钮
    const backButtonSize = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回功法页面
        this.sceneManager.showScene('skill');
      }
    );

    this.addUIElement(this.backButton);

    // 创建标签按钮
    const tabButtonWidth = 120;
    const tabButtonHeight = 40;
    const tabButtonY = 100;

    // 等级提升按钮
    this.levelUpButton = new Button(
      this.ctx,
      this.screenWidth / 2 - tabButtonWidth * 1.5 - margin,
      tabButtonY,
      tabButtonWidth,
      tabButtonHeight,
      '等级提升',
      null,
      null,
      () => {
        this.selectedTabIndex = 0;
      }
    );

    this.addUIElement(this.levelUpButton);

    // 星级提升按钮
    this.starUpButton = new Button(
      this.ctx,
      this.screenWidth / 2 - tabButtonWidth / 2,
      tabButtonY,
      tabButtonWidth,
      tabButtonHeight,
      '星级提升',
      null,
      null,
      () => {
        this.selectedTabIndex = 1;
      }
    );

    this.addUIElement(this.starUpButton);

    // 功法进阶按钮
    this.advancementButton = new Button(
      this.ctx,
      this.screenWidth / 2 + tabButtonWidth / 2 + margin,
      tabButtonY,
      tabButtonWidth,
      tabButtonHeight,
      '功法进阶',
      null,
      null,
      () => {
        this.selectedTabIndex = 2;
      }
    );

    this.addUIElement(this.advancementButton);

    // 创建确认按钮
    const confirmButtonWidth = 150;
    const confirmButtonHeight = 50;

    this.confirmButton = new Button(
      this.ctx,
      this.screenWidth / 2 - confirmButtonWidth / 2,
      this.screenHeight - confirmButtonHeight - margin * 3,
      confirmButtonWidth,
      confirmButtonHeight,
      '确认升级',
      null,
      null,
      () => {
        this.confirmUpgrade();
      }
    );

    this.addUIElement(this.confirmButton);
  }

  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("SkillUpgradeScene onShow", params);
      this.visible = true;

      // 获取参数
      if (!params) {
        console.error('升级功法场景缺少必要参数');
        return;
      }

      // 处理两种情况：直接传递了skill对象或者只传递了skillId
      if (params.skill) {
        this.skill = params.skill;
        this.skillId = params.skill.id;
        console.log(`接收到功法对象，ID: ${this.skillId}`);
      } else if (params.skillId) {
        this.skillId = params.skillId;

        // 尝试从游戏上下文中获取skillManager实例
        if (AppContext && AppContext.game && AppContext.game.skillManager) {
          // 获取当前要升级的功法
          this.skill = AppContext.game.skillManager.getSkillById(this.skillId);

          if (!this.skill) {
            console.error(`找不到ID为${this.skillId}的功法`);
            return;
          }
        } else {
          console.warn("skillManager未初始化或不可用");
          // 设置为空避免报错
          this.skill = null;
          return;
        }
      } else {
        console.error('升级功法场景缺少必要参数：skill或skillId');
        return;
      }

      console.log(`成功加载功法升级界面，功法: ${this.skill.name}, ID: ${this.skillId}`);

      // 重置标签选择
      this.selectedTabIndex = 0;

      // 初始化UI
      this.initUI();
    } catch (error) {
      console.error("SkillUpgradeScene.onShow 出错:", error);
      console.error("错误详情:", error.stack);
      // 设置为空避免报错
      this.skill = null;
    }
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 设置场景为不可见
    this.visible = false;
  }

  // 处理触摸开始事件
  handleTouchStart(x, y) {
    return false;
  }

  // 处理触摸移动事件
  handleTouchMove(x, y) {
    return false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    return false;
  }

  // 确认升级
  confirmUpgrade() {
    if (!this.skill) {
      console.error('没有选择功法或功法无效');
      return;
    }

    if (this.selectedTabIndex === 0) {
      // 等级提升
      this.upgradeLevelConfirm();
    } else if (this.selectedTabIndex === 1) {
      // 星级提升
      this.upgradeStarConfirm();
    } else if (this.selectedTabIndex === 2) {
      // 功法进阶
      this.goToAdvancementScene();
    }
  }

  // 跳转到功法进阶场景
  goToAdvancementScene() {
    if (!this.skill) {
      console.error('没有选择功法或功法无效');
      return;
    }

    // 跳转到功法进阶场景
    this.sceneManager.showScene('skillAdvancement', {
      skill: this.skill,
      returnScene: 'skillUpgrade',
      returnParams: { skill: this.skill }
    });
  }

  // 确认等级提升
  upgradeLevelConfirm() {
    try {
      // 检查功法是否有效
      if (!this.skill) {
        console.error('没有选择功法或功法无效');
        return;
      }

      // 获取skillManager
      if (!AppContext || !AppContext.game || !AppContext.game.skillManager) {
        console.error('skillManager未初始化');
        return;
      }

      const skillManager = AppContext.game.skillManager;

      // 获取升级所需材料
      const materials = skillManager.getUpgradeMaterials(this.skill);
      if (!materials || !materials.skill_essence) {
        console.error('无法获取升级所需材料');
        return;
      }

      // 检查材料是否足够
      if (!skillManager.consumeMaterial('skill_essence', materials.skill_essence)) {
        console.log('心法要义不足，无法升级');
        return;
      }

      // 升级功法
      this.skill.upgrade(1);

      // 保存游戏状态
      skillManager.saveToGameState(AppContext.game.gameStateManager.gameState);
      AppContext.game.gameStateManager.saveGameState();

      console.log(`${this.skill.name} 升级成功，当前等级: ${this.skill.level}`);
    } catch (error) {
      console.error('升级功法时出错:', error);
    }
  }

  // 确认星级提升
  upgradeStarConfirm() {
    try {
      // 检查功法是否有效
      if (!this.skill) {
        console.error('没有选择功法或功法无效');
        return;
      }

      // 获取skillManager
      if (!AppContext || !AppContext.game || !AppContext.game.skillManager) {
        console.error('skillManager未初始化');
        return;
      }

      const skillManager = AppContext.game.skillManager;

      // 获取升星所需碎片
      const materials = skillManager.getStarUpgradeMaterials(this.skill);
      if (!materials) {
        console.error('无法获取升星所需材料');
        return;
      }

      // 获取碎片ID和数量
      const entries = Object.entries(materials);
      if (!entries.length) {
        console.error('升星材料列表为空');
        return;
      }

      const [fragmentId, count] = entries[0];

      // 检查碎片是否足够
      if (!skillManager.consumeFragment(fragmentId, count)) {
        console.log('功法碎片不足，无法升星');
        return;
      }

      // 升级功法星级
      this.skill.upgradeStar();

      // 保存游戏状态
      skillManager.saveToGameState(AppContext.game.gameStateManager.gameState);
      AppContext.game.gameStateManager.saveGameState();

      console.log(`${this.skill.name} 升星成功，当前星级: ${this.skill.stars}星`);
    } catch (error) {
      console.error('升星功法时出错:', error);
    }
  }

  // 子类实现的绘制逻辑
  drawScene() {
    if (!this.skill) {
      return;
    }

    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制功法信息
    this.drawSkillInfo();

    // 绘制标签内容
    if (this.selectedTabIndex === 0) {
      this.drawLevelUpContent();
    } else if (this.selectedTabIndex === 1) {
      this.drawStarUpContent();
    } else if (this.selectedTabIndex === 2) {
      this.drawAdvancementContent();
    }
  }

  // 绘制功法进阶内容
  drawAdvancementContent() {
    const contentY = 350;
    const margin = 20;

    if (!this.skill) return;

    // 检查是否已达到最高进阶等级
    if (this.skill.advancementLevel >= (this.skill.maxAdvancementLevel || 3)) {
      this.ctx.font = 'bold 18px Arial';
      this.ctx.fillStyle = '#ff6666';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('已达到最高进阶等级', this.screenWidth / 2, contentY + 50);
      return;
    }

    // 绘制当前进阶等级
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffcc00';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `当前进阶等级: ${this.skill.advancementLevel}/${this.skill.maxAdvancementLevel}`,
      this.screenWidth / 2,
      contentY
    );

    // 绘制进阶效果预览
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('进阶效果预览', this.screenWidth / 2, contentY + 40);

    // 获取下一级进阶效果
    let nextEffect = null;
    if (this.skill.advancementEffects && this.skill.advancementEffects.length > this.skill.advancementLevel) {
      nextEffect = this.skill.advancementEffects[this.skill.advancementLevel];
    } else if (AppContext && AppContext.game && AppContext.game.skillManager) {
      // 如果没有预定义的进阶效果，获取默认效果
      const defaultEffects = this.skill.getDefaultAdvancementEffects();
      if (defaultEffects && defaultEffects.length > this.skill.advancementLevel) {
        nextEffect = defaultEffects[this.skill.advancementLevel];
      }
    }

    if (nextEffect) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#00ff00';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        nextEffect.description,
        this.screenWidth / 2,
        contentY + 70
      );

      // 显示属性加成
      if (nextEffect.attributes) {
        let y = contentY + 100;
        for (const [attr, value] of Object.entries(nextEffect.attributes)) {
          let attrName = attr;
          switch (attr) {
            case 'hp': attrName = '生命值'; break;
            case 'hpPercent': attrName = '生命值百分比'; break;
            case 'attack': attrName = '攻击力'; break;
            case 'attackPercent': attrName = '攻击力百分比'; break;
            case 'defense': attrName = '防御力'; break;
            case 'defensePercent': attrName = '防御力百分比'; break;
            case 'speed': attrName = '速度'; break;
            case 'critical': attrName = '暴击率'; break;
            case 'critDamage': attrName = '暴击伤害'; break;
          }

          this.ctx.font = '14px Arial';
          this.ctx.fillStyle = '#cccccc';
          this.ctx.textAlign = 'center';

          const valueText = attr.includes('Percent') ? `${value}%` : value;
          this.ctx.fillText(
            `${attrName}: +${valueText}`,
            this.screenWidth / 2,
            y
          );

          y += 20;
        }
      }

      // 显示特殊效果
      if (nextEffect.specialEffect) {
        this.ctx.font = '16px Arial';
        this.ctx.fillStyle = '#00ccff';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          `特殊效果: ${nextEffect.specialEffect.name}`,
          this.screenWidth / 2,
          contentY + 180
        );

        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = '#cccccc';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          nextEffect.specialEffect.description,
          this.screenWidth / 2,
          contentY + 200
        );
      }
    } else {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('暂无进阶效果信息', this.screenWidth / 2, contentY + 70);
    }

    // 显示进阶所需材料
    try {
      if (AppContext && AppContext.game && AppContext.game.skillManager) {
        const skillManager = AppContext.game.skillManager;
        const materials = skillManager.getAdvancementMaterials(this.skill);

        if (materials) {
          this.ctx.font = 'bold 18px Arial';
          this.ctx.fillStyle = '#ffffff';
          this.ctx.textAlign = 'center';
          this.ctx.fillText('进阶所需材料', this.screenWidth / 2, contentY + 240);

          let y = contentY + 270;
          for (const [materialId, count] of Object.entries(materials)) {
            const material = skillManager.materials[materialId] || skillManager.fragments[materialId];
            if (!material) continue;

            const currentAmount = material.count || 0;
            const isEnough = currentAmount >= count;

            this.ctx.font = '16px Arial';
            this.ctx.fillStyle = isEnough ? '#00ff00' : '#ff6666';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
              `${material.name}: ${currentAmount}/${count}`,
              this.screenWidth / 2,
              y
            );

            y += 25;
          }
        }
      }
    } catch (error) {
      console.error('绘制进阶材料信息时出错:', error);
    }
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 8);
  }

  // 绘制功法信息
  drawSkillInfo() {
    if (!this.skill) return;

    const headerHeight = 80;
    const margin = 10;
    const skillInfoY = headerHeight + 50;

    // 绘制功法信息背景
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
    this.ctx.fillRect(margin, skillInfoY, this.screenWidth - margin * 2, 100);

    try {
      // 绘制功法边框
      if (this.skill.getQualityColor) {
        this.ctx.strokeStyle = this.skill.getQualityColor();
      } else {
        this.ctx.strokeStyle = '#ffffff';
      }
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(margin, skillInfoY, this.screenWidth - margin * 2, 100);

      // 绘制功法名称
      this.ctx.font = 'bold 20px Arial';
      if (this.skill.getQualityColor) {
        this.ctx.fillStyle = this.skill.getQualityColor();
      } else {
        this.ctx.fillStyle = '#ffffff';
      }
      this.ctx.textAlign = 'center';
      this.ctx.fillText(this.skill.name, this.screenWidth / 2, skillInfoY + 30);

      // 绘制功法品质和等级
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#ffffff';

      let qualityName = '未知';
      if (this.skill.getQualityName) {
        qualityName = this.skill.getQualityName();
      }

      this.ctx.fillText(
        `品质: ${qualityName} | 等级: ${this.skill.level} | 星级: ${this.skill.stars}★`,
        this.screenWidth / 2,
        skillInfoY + 60
      );

      // 绘制功法战力
      this.ctx.font = 'bold 18px Arial';
      this.ctx.fillStyle = '#ffcc00';
      this.ctx.fillText(`战力: ${this.skill.power || 0}`, this.screenWidth / 2, skillInfoY + 90);
    } catch (error) {
      console.error('绘制功法信息时出错:', error);
    }
  }

  // 绘制等级提升内容
  drawLevelUpContent() {
    const contentY = 350;
    const margin = 20;

    if (!this.skill) return;

    // 绘制提升后的属性变化预览
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('提升效果预览', this.screenWidth / 2, contentY);

    // 当前属性
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`当前(${this.skill.level}级)`, this.screenWidth / 2 - 10, contentY + 40);

    // 提升后属性
    this.ctx.fillStyle = '#00ff00';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`升级后(${this.skill.level + 1}级)`, this.screenWidth / 2 + 10, contentY + 40);

    // 显示升级所需材料
    try {
      if (AppContext && AppContext.game && AppContext.game.skillManager) {
        const skillManager = AppContext.game.skillManager;
        const materials = skillManager.getUpgradeMaterials(this.skill);
        const availableMaterials = skillManager.getMaterials();

        if (materials && materials.skill_essence && availableMaterials && availableMaterials.skill_essence) {
          const currentAmount = availableMaterials.skill_essence.count || 0;
          const requiredAmount = materials.skill_essence;
          const isEnough = currentAmount >= requiredAmount;

          this.ctx.font = 'bold 18px Arial';
          this.ctx.fillStyle = '#ffffff';
          this.ctx.textAlign = 'center';
          this.ctx.fillText('升级所需材料', this.screenWidth / 2, contentY + 100);

          this.ctx.font = '16px Arial';
          this.ctx.fillStyle = isEnough ? '#00ff00' : '#ff6666';
          this.ctx.fillText(
            `心法要义: ${currentAmount}/${requiredAmount}`,
            this.screenWidth / 2,
            contentY + 130
          );
        }
      }
    } catch (error) {
      console.error('绘制升级材料信息时出错:', error);
    }
  }

  // 绘制星级提升内容
  drawStarUpContent() {
    const contentY = 350;
    const margin = 20;

    if (!this.skill) return;

    // 检查是否已达到最高星级
    if (this.skill.stars >= (this.skill.maxStars || 5)) {
      this.ctx.font = 'bold 18px Arial';
      this.ctx.fillStyle = '#ff6666';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('已达到最高星级', this.screenWidth / 2, contentY + 50);
      return;
    }

    // 绘制提升后的属性变化预览
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('提升效果预览', this.screenWidth / 2, contentY);

    // 当前属性
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`当前(${this.skill.stars}★)`, this.screenWidth / 2 - 10, contentY + 40);

    // 提升后属性
    this.ctx.fillStyle = '#00ff00';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`升星后(${this.skill.stars + 1}★)`, this.screenWidth / 2 + 10, contentY + 40);

    // 显示升星所需碎片
    try {
      if (AppContext && AppContext.game && AppContext.game.skillManager) {
        const skillManager = AppContext.game.skillManager;
        const materials = skillManager.getStarUpgradeMaterials(this.skill);
        const fragments = skillManager.getFragments();

        if (materials && fragments) {
          const entries = Object.entries(materials);
          if (entries.length > 0) {
            const [fragmentId, requiredAmount] = entries[0];
            const fragment = fragments[fragmentId];

            if (fragment) {
              const currentAmount = fragment.count || 0;
              const isEnough = currentAmount >= requiredAmount;

              this.ctx.font = 'bold 18px Arial';
              this.ctx.fillStyle = '#ffffff';
              this.ctx.textAlign = 'center';
              this.ctx.fillText('升星所需材料', this.screenWidth / 2, contentY + 100);

              this.ctx.font = '16px Arial';
              this.ctx.fillStyle = isEnough ? '#00ff00' : '#ff6666';
              this.ctx.fillText(
                `${fragment.name}: ${currentAmount}/${requiredAmount}`,
                this.screenWidth / 2,
                contentY + 130
              );
            }
          }
        }
      }
    } catch (error) {
      console.error('绘制升星材料信息时出错:', error);
    }
  }
}

export default SkillUpgradeScene;