/**
 * 剑心详情场景类
 * 显示剑心详细信息，并提供升级和进阶功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import game from '../../game';

class SwordHeartDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前剑心ID
    this.swordHeartId = null;

    // 当前剑心
    this.swordHeart = null;

    // 当前选择的操作类型（提升或进阶）
    this.selectedOperation = 'level'; // 'level' 或 'advancement'

    // 要消耗的剑意值
    this.intentToUse = 100;

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 加载剑心数据
    this.loadSwordHeart();

    // 创建返回按钮
    this.createBackButton();

    // 创建操作按钮
    this.createOperationButtons();

    // 创建提升和进阶按钮
    this.createActionButtons();
  }

  // 加载剑心数据
  loadSwordHeart() {
    if (game.gameStateManager && this.swordHeartId) {
      this.swordHeart = game.gameStateManager.getSwordHeart(this.swordHeartId);
    }
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('swordHeart');
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建操作按钮（提升/进阶切换）
  createOperationButtons() {
    const headerHeight = 80;
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 20;

    // 提升按钮
    this.levelButton = new Button(
      this.ctx,
      this.screenWidth / 2 - buttonWidth - margin / 2,
      headerHeight + 20,
      buttonWidth,
      buttonHeight,
      '剑心提升',
      null,
      this.selectedOperation === 'level' ? '#4299e1' : null,
      () => {
        this.selectedOperation = 'level';
        this.updateOperationButtons();
      }
    );

    this.addUIElement(this.levelButton);

    // 进阶按钮
    this.advancementButton = new Button(
      this.ctx,
      this.screenWidth / 2 + margin / 2,
      headerHeight + 20,
      buttonWidth,
      buttonHeight,
      '剑心进阶',
      null,
      this.selectedOperation === 'advancement' ? '#4299e1' : null,
      () => {
        this.selectedOperation = 'advancement';
        this.updateOperationButtons();
      }
    );

    this.addUIElement(this.advancementButton);
  }

  // 更新操作按钮状态
  updateOperationButtons() {
    this.levelButton.pressedImg = this.selectedOperation === 'level' ? '#4299e1' : null;
    this.advancementButton.pressedImg = this.selectedOperation === 'advancement' ? '#4299e1' : null;

    // 重新创建操作按钮
    this.createActionButtons();
  }

  // 创建提升和进阶按钮
  createActionButtons() {
    const headerHeight = 80;
    const buttonWidth = 150;
    const buttonHeight = 50;
    const margin = 20;
    const y = this.screenHeight - buttonHeight - margin;

    // 移除之前的按钮
    if (this.actionButton) {
      this.removeUIElement(this.actionButton);
    }

    if (this.intentSlider) {
      this.removeUIElement(this.intentSlider);
    }

    if (this.selectedOperation === 'level') {
      // 创建提升按钮
      this.actionButton = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        y,
        buttonWidth,
        buttonHeight,
        '提升剑心',
        null,
        '#4299e1',
        () => {
          this.levelUpSwordHeart();
        }
      );

      this.addUIElement(this.actionButton);

      // 创建剑意滑块
      // 这里简化处理，实际上应该创建一个滑块组件
      this.intentToUse = 100;
    } else {
      // 创建进阶按钮
      this.actionButton = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        y,
        buttonWidth,
        buttonHeight,
        '进阶剑心',
        null,
        '#4299e1',
        () => {
          this.advanceSwordHeart();
        }
      );

      this.addUIElement(this.actionButton);
    }
  }

  // 提升剑心等级
  levelUpSwordHeart() {
    if (!this.swordHeart) return;

    // 检查是否已达到最大等级
    if (this.swordHeart.level >= this.swordHeart.maxLevel) {
      this.showMessage('剑心已达到最大等级');
      return;
    }

    // 检查剑意是否足够
    const currentIntent = game.gameStateManager.getSwordIntent();
    if (currentIntent < this.intentToUse) {
      this.showMessage('剑意不足，请通过剑心试炼获取更多剑意');
      return;
    }

    // 提升剑心等级
    const success = game.gameStateManager.levelUpSwordHeart(this.swordHeart.id, this.intentToUse);

    if (success) {
      // 重新加载剑心数据
      this.loadSwordHeart();

      // 显示成功消息
      this.showMessage('剑心等级提升成功');
    } else {
      // 显示失败消息
      this.showMessage('剑心等级提升失败');
    }
  }

  // 进阶剑心
  advanceSwordHeart() {
    if (!this.swordHeart) return;

    // 检查是否已达到最大进阶等级
    if (this.swordHeart.advancementLevel >= this.swordHeart.maxAdvancementLevel) {
      this.showMessage('剑心已达到最大进阶等级');
      return;
    }

    // 获取进阶所需材料
    const requirements = this.swordHeart.advancementRequirements[this.swordHeart.advancementLevel];
    if (!requirements || requirements.length === 0) {
      this.showMessage('进阶所需材料未定义');
      return;
    }

    // 检查材料是否足够
    const materials = [];
    let insufficientMaterials = false;

    for (const req of requirements) {
      // 查找同名剑心物品
      const itemId = req.id;
      const item = game.gameStateManager.getItem(itemId);

      if (!item || item.count < req.count) {
        insufficientMaterials = true;
        break;
      }

      materials.push({
        id: itemId,
        count: req.count
      });
    }

    if (insufficientMaterials) {
      // 显示需要的材料信息
      let materialInfo = '需要以下材料进行进阶：\n';
      for (const req of requirements) {
        const item = game.gameStateManager.getItem(req.id);
        const itemName = item ? item.name : req.id;
        const itemCount = item ? item.count : 0;
        materialInfo += `${itemName}: ${itemCount}/${req.count}\n`;
      }

      this.showMessage(`材料不足，无法进阶\n${materialInfo}`);
      return;
    }

    // 进阶剑心
    const success = game.gameStateManager.advanceSwordHeart(this.swordHeart.id, materials);

    if (success) {
      // 重新加载剑心数据
      this.loadSwordHeart();

      // 显示成功消息
      const nextLevel = this.swordHeart.advancementLevel + 1;
      const advancementBonus = this.swordHeart.getAdvancementDescription();
      this.showMessage(`剑心进阶成功！\n当前进阶等级：${nextLevel}\n${advancementBonus}`);
    } else {
      // 显示失败消息
      this.showMessage('剑心进阶失败');
    }
  }

  // 显示消息对话框
  showMessage(message) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];

    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '提示',
      message,
      dialogButtons
    );

    this.addUIElement(dialog);
    dialog.show();
  }

  // 场景显示时的回调
  onShow(params) {
    if (params && params.swordHeartId) {
      this.swordHeartId = params.swordHeartId;
    }

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 重置状态
    this.swordHeartId = null;
    this.swordHeart = null;
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制剑心详情
    this.drawSwordHeartDetail();

    // 根据当前操作类型绘制不同内容
    if (this.selectedOperation === 'level') {
      this.drawLevelUpInfo();
    } else {
      this.drawAdvancementInfo();
    }
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a202c');
    gradient.addColorStop(1, '#2d3748');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    const headerHeight = 80;

    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(this.swordHeart ? this.swordHeart.name : '剑心详情', this.screenWidth / 2, headerHeight / 2);

    // 绘制剑意值
    const swordIntent = game.gameStateManager ? game.gameStateManager.getSwordIntent() : 0;
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#4299e1';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`剑意: ${swordIntent}`, this.screenWidth - 20, headerHeight / 2);
  }

  // 绘制剑心详情
  drawSwordHeartDetail() {
    if (!this.swordHeart) return;

    const headerHeight = 80;
    const margin = 20;
    const y = headerHeight + 80;

    // 绘制剑心信息背景
    this.ctx.fillStyle = this.swordHeart.color || '#4299e1';
    this.ctx.fillRect(margin, y, this.screenWidth - margin * 2, 120);

    // 绘制剑心描述
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(this.swordHeart.description, margin + 20, y + 20);

    // 绘制剑心等级
    this.ctx.fillText(`等级: ${this.swordHeart.level}/${this.swordHeart.maxLevel}`, margin + 20, y + 50);

    // 绘制剑心进阶等级
    const advancementDescription = this.swordHeart.getAdvancementDescription();
    this.ctx.fillText(`进阶: ${this.swordHeart.advancementLevel}/${this.swordHeart.maxAdvancementLevel} (${advancementDescription})`, margin + 20, y + 80);

    // 绘制剑心属性加成
    const attributeBonus = this.swordHeart.getAttributeBonus();
    let attributeText = '属性加成: ';

    for (const [key, value] of Object.entries(attributeBonus)) {
      attributeText += `${this.getAttributeName(key)}: ${value.toFixed(2)} `;
    }

    this.ctx.font = '14px Arial';
    this.ctx.fillText(attributeText, margin + 20, y + 110);
  }

  // 绘制提升信息
  drawLevelUpInfo() {
    if (!this.swordHeart) return;

    const headerHeight = 80;
    const margin = 20;
    const y = headerHeight + 220;

    // 绘制提升信息背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, y, this.screenWidth - margin * 2, 150);

    // 绘制提升信息标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('剑心提升', this.screenWidth / 2, y + 20);

    // 绘制当前等级和下一级
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`当前等级: ${this.swordHeart.level}`, margin + 20, y + 50);
    this.ctx.fillText(`下一等级: ${this.swordHeart.level + 1}`, margin + 20, y + 80);

    // 绘制所需剑意
    const requiredIntent = this.swordHeart.getNextLevelRequirement();
    this.ctx.fillText(`所需剑意: ${requiredIntent}`, margin + 20, y + 110);

    // 绘制剑意输入框
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(this.screenWidth / 2, y + 110, 100, 30);

    this.ctx.textAlign = 'center';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillText(`${this.intentToUse}`, this.screenWidth / 2 + 50, y + 115);
  }

  // 绘制进阶信息
  drawAdvancementInfo() {
    if (!this.swordHeart) return;

    const headerHeight = 80;
    const margin = 20;
    const y = headerHeight + 220;

    // 绘制进阶信息背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, y, this.screenWidth - margin * 2, 150);

    // 绘制进阶信息标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('剑心进阶', this.screenWidth / 2, y + 20);

    // 绘制当前进阶等级和下一级
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`当前进阶等级: ${this.swordHeart.advancementLevel}`, margin + 20, y + 50);
    this.ctx.fillText(`下一进阶等级: ${this.swordHeart.advancementLevel + 1}`, margin + 20, y + 80);

    // 绘制所需材料
    this.ctx.fillText('所需材料:', margin + 20, y + 110);

    // 获取进阶所需材料
    const requirements = this.swordHeart.advancementRequirements[this.swordHeart.advancementLevel];
    if (requirements && requirements.length > 0) {
      let materialsText = '';

      requirements.forEach((req, index) => {
        const item = game.gameStateManager.getItem(req.id);
        const itemName = item ? item.name : req.id;
        const itemCount = item ? item.count : 0;

        materialsText += `${itemName}: ${itemCount}/${req.count}`;

        if (index < requirements.length - 1) {
          materialsText += ', ';
        }
      });

      this.ctx.fillText(materialsText, margin + 120, y + 110);
    } else {
      this.ctx.fillText('无需材料', margin + 120, y + 110);
    }
  }

  // 获取属性名称
  getAttributeName(key) {
    const attributeNames = {
      hp: '生命',
      attack: '攻击',
      defense: '防御',
      speed: '速度',
      critRate: '暴击率',
      critDamage: '暴击伤害',
      daoRule: '大道法则',
      penetration: '破防',
      hpBonus: '生命加成',
      defenseBonus: '防御加成',
      attackBonus: '攻击加成',
      hitRate: '命中率',
      dodgeRate: '闪避率',
      healEffect: '治疗效果',
      damageBonus: '伤害加成',
      damageReduction: '伤害减免',
      skillDamageBonus: '技能伤害加成',
      skillDamageReduction: '技能伤害减免'
    };

    return attributeNames[key] || key;
  }
}

export default SwordHeartDetailScene;
