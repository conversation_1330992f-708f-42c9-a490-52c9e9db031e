/**
 * 剑心管理器类
 * 负责管理所有剑心数据和相关操作
 */
import SwordHeart from '../models/SwordHeart.js';
import EventEmitter from '../utils/EventEmitter.js';
import game from '../../game.js';
import { SWORD_HEART_CONFIG } from '../config/SwordHeartConfig.js';

class SwordHeartManager extends EventEmitter {
  constructor() {
    super();

    // 剑心列表
    this.swordHearts = [];

    // 剑意值（通用资源）
    this.swordIntent = 0;

    // 初始化剑心数据
    this.initSwordHearts();
  }

  /**
   * 初始化剑心数据
   */
  initSwordHearts() {
    // 从配置文件创建剑心实例
    this.swordHearts = SWORD_HEART_CONFIG.map(data => new SwordHeart(data));
  }

  /**
   * 获取所有剑心
   * @returns {Array} 剑心列表
   */
  getAllSwordHearts() {
    return this.swordHearts;
  }

  /**
   * 获取已解锁的剑心
   * @returns {Array} 已解锁的剑心列表
   */
  getUnlockedSwordHearts() {
    return this.swordHearts.filter(heart => heart.unlocked);
  }

  /**
   * 获取指定ID的剑心
   * @param {string} id 剑心ID
   * @returns {SwordHeart|null} 剑心实例或null
   */
  getSwordHeart(id) {
    return this.swordHearts.find(heart => heart.id === id) || null;
  }

  /**
   * 解锁剑心
   * @param {string} id 剑心ID
   * @returns {boolean} 是否成功解锁
   */
  unlockSwordHeart(id) {
    const swordHeart = this.getSwordHeart(id);
    if (swordHeart && !swordHeart.unlocked) {
      swordHeart.unlocked = true;
      this.emit('swordHeartUnlocked', swordHeart);
      return true;
    }
    return false;
  }

  /**
   * 提升剑心等级
   * @param {string} id 剑心ID
   * @param {number} swordIntent 要消耗的剑意值
   * @returns {boolean} 是否成功提升等级
   */
  levelUpSwordHeart(id, swordIntent) {
    const swordHeart = this.getSwordHeart(id);
    if (swordHeart && swordHeart.unlocked) {
      const result = swordHeart.levelUp(swordIntent);
      if (result) {
        this.emit('swordHeartLevelUp', swordHeart);
      }
      return result;
    }
    return false;
  }

  /**
   * 进阶剑心
   * @param {string} id 剑心ID
   * @param {Array} materials 材料列表
   * @returns {boolean} 是否成功进阶
   */
  advanceSwordHeart(id, materials) {
    const swordHeart = this.getSwordHeart(id);
    if (swordHeart && swordHeart.unlocked) {
      const result = swordHeart.advance(materials);
      if (result) {
        this.emit('swordHeartAdvanced', swordHeart);
      }
      return result;
    }
    return false;
  }

  /**
   * 增加剑意值
   * @param {number} amount 增加的数量
   */
  addSwordIntent(amount) {
    this.swordIntent += amount;
    this.emit('swordIntentChanged', this.swordIntent);
  }

  /**
   * 获取当前剑意值
   * @returns {number} 剑意值
   */
  getSwordIntent() {
    return this.swordIntent;
  }

  /**
   * 将剑心管理器数据转换为JSON格式
   * @returns {Object} JSON对象
   */
  toJSON() {
    return {
      swordHearts: this.swordHearts.map(heart => heart.toJSON()),
      swordIntent: this.swordIntent
    };
  }

  /**
   * 从JSON对象加载剑心管理器数据
   * @param {Object} json JSON对象
   */
  fromJSON(json) {
    if (json && json.swordHearts) {
      this.swordHearts = json.swordHearts.map(heartData => SwordHeart.fromJSON(heartData));
    }

    if (json && typeof json.swordIntent === 'number') {
      this.swordIntent = json.swordIntent;
    }
  }
}

// 导出剑心管理器类
export default SwordHeartManager;
