// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

const db = cloud.database()
const xiuxianPlayerCollection = db.collection('xiuxian-player')
const _ = db.command

/**
 * 给玩家发放奖励的云函数
 * 
 * 参数格式：
 * {
 *   targetOpenid: "玩家的openid",  // 必需，要发放奖励的玩家ID
 *   rewards: {  // 奖励内容
 *     exp: 100,  // 经验值
 *     lingshi: 200,  // 灵石
 *     xianyu: 50,  // 仙玉
 *     items: [  // 物品列表
 *       { id: "item1", name: "丹药", count: 1 },
 *       { id: "item2", name: "符箓", count: 2 }
 *     ],
 *     equipments: [  // 装备列表
 *       { id: "equip1", name: "飞剑", level: 1, quality: 2 }
 *     ]
 *   },
 *   reason: "战斗胜利"  // 可选，奖励发放原因
 * }
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const adminOpenid = wxContext.OPENID  // 记录谁调用了这个函数
  
  // 获取目标玩家ID和奖励内容
  const { targetOpenid, rewards, reason } = event
  
  // 参数验证
  if (!targetOpenid) {
    return {
      success: false,
      error: '缺少目标玩家ID'
    }
  }
  
  if (!rewards) {
    return {
      success: false,
      error: '缺少奖励内容'
    }
  }
  
  try {
    // 查询玩家记录
    const playerRecord = await xiuxianPlayerCollection.where({
      openid: targetOpenid  // 使用openid字段查询
    }).get()
    
    if (playerRecord.data.length === 0) {
      return {
        success: false,
        error: '找不到目标玩家'
      }
    }
    
    const playerData = playerRecord.data[0]
    const playerGameState = playerData.gameState || {}
    const playerId = playerData._id
    
    // 准备更新数据
    const updateData = {}
    const rewardLog = {
      time: db.serverDate(),
      reason: reason || '系统奖励',
      rewards: { ...rewards },
      adminOpenid: adminOpenid
    }
    
    // 处理经验值
    if (rewards.exp) {
      if (!playerGameState.player) playerGameState.player = {}
      updateData['gameState.player.exp'] = _.inc(rewards.exp)
    }
    
    // 处理灵石
    if (rewards.lingshi) {
      if (!playerGameState.player) playerGameState.player = {}
      if (!playerGameState.player.resources) playerGameState.player.resources = {}
      updateData['gameState.player.resources.lingshi'] = _.inc(rewards.lingshi)
    }
    
    // 处理仙玉
    if (rewards.xianyu) {
      if (!playerGameState.player) playerGameState.player = {}
      if (!playerGameState.player.resources) playerGameState.player.resources = {}
      updateData['gameState.player.resources.xianyu'] = _.inc(rewards.xianyu)
    }
    
    // 处理物品
    if (rewards.items && rewards.items.length > 0) {
      // 获取当前背包
      if (!playerGameState.inventory) playerGameState.inventory = {}
      if (!playerGameState.inventory.items) playerGameState.inventory.items = []
      
      // 复制当前背包
      const currentItems = [...playerGameState.inventory.items]
      
      // 添加新物品
      rewards.items.forEach(item => {
        const existingItemIndex = currentItems.findIndex(i => i.id === item.id)
        
        if (existingItemIndex >= 0) {
          // 如果物品已存在，增加数量
          currentItems[existingItemIndex].count = (currentItems[existingItemIndex].count || 1) + (item.count || 1)
        } else {
          // 否则添加新物品
          currentItems.push({
            id: item.id,
            name: item.name,
            count: item.count || 1,
            ...item
          })
        }
      })
      
      // 更新背包
      updateData['gameState.inventory.items'] = currentItems
    }
    
    // 处理装备
    if (rewards.equipments && rewards.equipments.length > 0) {
      // 获取当前装备背包
      if (!playerGameState.inventory) playerGameState.inventory = {}
      if (!playerGameState.inventory.equipments) playerGameState.inventory.equipments = []
      
      // 复制当前装备背包
      const currentEquipments = [...playerGameState.inventory.equipments]
      
      // 添加新装备，每件装备都是独立的，所以直接添加
      rewards.equipments.forEach(equipment => {
        currentEquipments.push({
          id: equipment.id,
          name: equipment.name,
          level: equipment.level || 1,
          quality: equipment.quality || 0,
          ...equipment,
          acquireTime: db.serverDate()
        })
      })
      
      // 更新装备背包
      updateData['gameState.inventory.equipments'] = currentEquipments
    }
    
    // 记录奖励日志
    updateData['rewardLogs'] = _.push([rewardLog])
    
    // 更新玩家数据
    await xiuxianPlayerCollection.doc(playerId).update({
      data: updateData
    })
    
    return {
      success: true,
      message: '奖励发放成功',
      rewardLog: rewardLog
    }
  } catch (error) {
    console.error('奖励发放失败:', error)
    return {
      success: false,
      error: error.message || '发放奖励时发生错误'
    }
  }
} 