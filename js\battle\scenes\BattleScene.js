/**
 * 战斗场景
 * 显示战斗过程，并模拟战斗动画
 */
import BaseScene from '../../scenes/BaseScene';
import Button from '../../ui/Button';
import game from '../../../game';
import BattleUnit from '../models/BattleUnit';

class BattleScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 战斗数据
    this.playerUnits = [];
    this.enemyUnits = [];
    this.battleActions = []; // 战斗动作序列
    this.currentActionIndex = 0;
    
    // 战斗配置
    this.battleConfig = null;
    this.onBattleComplete = null;
    
    // 战斗状态
    this.isBattleActive = false;
    this.battleResult = null;
    
    // 战斗单元UI位置
    this.playerPositions = [];
    this.enemyPositions = [];
    
    // 动画相关
    this.actionDelay = 1000; // 动作之间的延迟(毫秒)
    this.lastActionTime = 0;
    this.currentAnimation = null;
    
    // 跳过战斗按钮
    this.skipButton = null;
    
    // 初始化布局
    this.initLayout();
  }
  
  /**
   * 初始化布局
   */
  initLayout() {
    // 计算战斗区域尺寸
    this.battleAreaWidth = this.screenWidth * 0.8;
    this.battleAreaHeight = this.screenHeight * 0.6;
    
    // 计算战斗区域位置
    this.battleAreaX = (this.screenWidth - this.battleAreaWidth) / 2;
    this.battleAreaY = (this.screenHeight - this.battleAreaHeight) / 2;
    
    // 计算单位尺寸
    this.unitWidth = this.battleAreaWidth * 0.15;
    this.unitHeight = this.battleAreaHeight * 0.2;
    this.unitSize = Math.min(this.unitWidth, this.unitHeight);
    
    // 计算单位间距
    this.unitSpacing = this.battleAreaWidth * 0.1;
    
    // 计算玩家单位起始位置
    this.playerStartX = this.battleAreaX + this.battleAreaWidth * 0.1;
    this.playerStartY = this.battleAreaY + this.battleAreaHeight * 0.6;
    
    // 计算敌人单位起始位置
    this.enemyStartX = this.battleAreaX + this.battleAreaWidth * 0.1;
    this.enemyStartY = this.battleAreaY + this.battleAreaHeight * 0.2;
    
    // 清空旧的位置数组
    this.unitPositions = {
      player: [],
      enemy: []
    };
    
    // 明确初始化新的位置数组
    this.playerPositions = [];
    this.enemyPositions = [];
    
    // 计算玩家单位位置 - 3个常规位置
    for (let i = 0; i < 3; i++) {
      const pos = {
        x: this.playerStartX + i * (this.unitWidth + this.unitSpacing),
        y: this.playerStartY,
        size: this.unitSize
      };
      this.unitPositions.player.push(pos);
      this.playerPositions.push(pos);
    }
    
    // 计算敌人单位位置 - 3个常规位置
    for (let i = 0; i < 3; i++) {
      const pos = {
        x: this.enemyStartX + i * (this.unitWidth + this.unitSpacing),
        y: this.enemyStartY,
        size: this.unitSize
      };
      this.unitPositions.enemy.push(pos);
      this.enemyPositions.push(pos);
    }
    
    console.log('战斗场景布局初始化完成:', {
      battleArea: {
        width: this.battleAreaWidth,
        height: this.battleAreaHeight,
        x: this.battleAreaX,
        y: this.battleAreaY
      },
      unit: {
        width: this.unitWidth,
        height: this.unitHeight,
        spacing: this.unitSpacing
      },
      positions: {
        player: this.playerPositions,
        enemy: this.enemyPositions
      }
    });
  }
  
  /**
   * 场景显示回调
   * @param {Object} params 场景参数
   */
  onShow(params) {
    console.log('战斗场景显示', params);
    
    // 重置战斗状态标志
    this.hasEndedBattle = false;
    this.battleEndingInProgress = false;
    this.resultShown = false;
    
    // 清空UI元素
    this.clearUIElements();
    
    // 获取资源
    this.resources = this.getResources();
    
    // 设置场景可见
    this.visible = true;
    
    // 初始化布局
    this.initLayout();
    
    // 初始化战斗
    this.setupBattle(params);
    
    // 初始化UI
    this.initUI();
    
    // 开始战斗
    this.startBattle();
  }
  
  /**
   * 设置战斗数据
   * @param {Object} params 战斗参数
   */
  setupBattle(params) {
    console.log('设置战斗数据', JSON.stringify(params, (key, value) => {
      // 避免循环引用问题
      if (key === 'unit' || key === 'source' || key === 'target') {
        return undefined;
      }
      return value;
    }, 2));
    
    // 设置参战双方
    this.playerUnits = params.playerUnits || [];
    this.enemyUnits = params.enemyUnits || [];
    
    console.log('玩家单位初始数据:', this.playerUnits.map(unit => ({
      name: unit.name,
      hp: unit.hp,
      maxHp: unit.maxHp,
      attributes: unit.attributes
    })));
    
    console.log('敌人单位初始数据:', this.enemyUnits.map(unit => ({
      name: unit.name,
      hp: unit.hp,
      maxHp: unit.maxHp,
      attributes: unit.attributes
    })));
    
    // 确保单位有必要的属性
    this.playerUnits.forEach((unit, index) => {
      this.initializeUnit(unit, 'player', index);
    });
    
    this.enemyUnits.forEach((unit, index) => {
      this.initializeUnit(unit, 'enemy', index);
    });
    
    console.log('单位初始化后玩家单位:', this.playerUnits.map(unit => ({
      name: unit.name,
      hp: unit.hp,
      maxHp: unit.maxHp
    })));
    
    console.log('单位初始化后敌人单位:', this.enemyUnits.map(unit => ({
      name: unit.name,
      hp: unit.hp,
      maxHp: unit.maxHp
    })));
    
    // 设置完成回调
    this.onBattleComplete = params.onComplete || null;
    
    // 添加返回按钮
    this.addBackButton();
    
    // 初始化战斗状态
    this.battleState = {
      turn: 0,
      phase: 'ready', // ready, action, end
      currentActionIndex: 0,
      actions: []
    };
    
    // 生成战斗动作序列
    this.battleState.actions = this.generateSimpleBattleActions();
    
    console.log('战斗初始化完成', {
      playerUnits: this.playerUnits.length,
      enemyUnits: this.enemyUnits.length,
      actions: this.battleState.actions.length
    });
  }
  
  /**
   * 添加返回按钮
   */
  addBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        console.log('点击返回按钮');
        
        // 清除任何可能的延迟调用
        if (this.processNextActionTimeout) {
          clearTimeout(this.processNextActionTimeout);
          this.processNextActionTimeout = null;
        }
        
        // 如果战斗正在进行，则强制结束战斗
        if (this.isBattleActive) {
          console.warn('战斗未结束，强制结束为失败');
          this.battleResult = 'defeat';
          this.endBattle(true);
        } else {
          // 如果战斗已结束，直接返回主场景
          if (this.sceneManager) {
            this.sceneManager.showScene('main');
          }
        }
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  /**
   * 初始化单元
   * @param {Object} unit 单元数据
   * @param {String} side 单元阵营 (player/enemy)
   * @param {Number} index 单元索引
   */
  initializeUnit(unit, side, index) {
    console.log(`初始化单位: ${unit.name}`);
    
    // 确保单位有必需的属性
    unit.id = unit.id || `${side}_${index}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    
    if (!unit.maxHp) {
      console.warn(`单位 ${unit.name} 缺少maxHp属性，设置为默认值100`);
      unit.maxHp = 100;
    }
    
    if (unit.hp === undefined) {
      console.warn(`单位 ${unit.name} 缺少hp属性，设置为maxHp值 ${unit.maxHp}`);
      unit.hp = unit.maxHp;
    }
    
    // 确保其他必需的属性
    unit.attack = unit.attack || 10;
    unit.defense = unit.defense || 5;
    unit.speed = unit.speed || 5;
    
    // 确保有阵营标识
    unit.side = side;
    
    // 确保有可读名称
    if (!unit.name) {
      unit.name = side === 'player' ? `玩家${index+1}` : `敌人${index+1}`;
    }
    
    // 默认位置索引
    if (unit.positionIndex === undefined) {
      unit.positionIndex = index;
    }
    
    // 默认生命值
    if (unit.maxHp === undefined) {
      unit.maxHp = 100;
      console.log(`初始化单位 ${unit.name} 设置默认maxHp: ${unit.maxHp}`);
    }
    
    if (unit.hp === undefined) {
      unit.hp = unit.maxHp;
      console.log(`初始化单位 ${unit.name} 设置默认hp: ${unit.hp}`);
    }
    
    // 默认基础属性
    if (!unit.attributes) {
      unit.attributes = {
        strength: 10,
        dexterity: 10,
        intelligence: 10,
        luck: 10
      };
    }
    
    // 添加判断单位是否存活的方法
    unit.isAlive = function() {
      console.log(`检查单位 ${this.name} 存活状态，HP: ${this.hp}`);
      return this.hp > 0;
    };
    
    // 测试isAlive方法
    console.log(`单位 ${unit.name} 初始存活状态: ${unit.isAlive()}`);
    
    console.log(`单位 ${unit.name} 初始化完成，HP: ${unit.hp}/${unit.maxHp}`);
    return unit;
  }
  
  /**
   * 生成简单的战斗动作序列
   * @returns {Array} 战斗动作数组
   */
  generateSimpleBattleActions() {
    console.log("开始生成战斗动作(模拟)...");
    const actions = [];

    // --- 修改开始: 克隆单位数据进行模拟 ---
    // 确保BattleUnit有clone方法，如果没有则需要实现
    const clonedPlayerUnits = this.playerUnits.map(unit => 
      (typeof unit.clone === 'function') ? unit.clone() : { ...unit }
    );
    const clonedEnemyUnits = this.enemyUnits.map(unit => 
      (typeof unit.clone === 'function') ? unit.clone() : { ...unit }
    );

    // 后续模拟使用克隆后的单位
    let alivePlayerUnits = clonedPlayerUnits.filter(unit => unit.isAlive());
    let aliveEnemyUnits = clonedEnemyUnits.filter(unit => unit.isAlive());
    // --- 修改结束 ---
    
    console.log(`模拟开始时：${alivePlayerUnits.length}个玩家单位存活，${aliveEnemyUnits.length}个敌方单位存活`);
    console.log('模拟玩家单位速度:', clonedPlayerUnits.map(u => ({ name: u.name, speed: u.speed })));
    console.log('模拟敌人单位速度:', clonedEnemyUnits.map(u => ({ name: u.name, speed: u.speed })));
    
    // 最多进行20个回合
    for (let turn = 0; turn < 20; turn++) {
      console.log(`--------------------------- 模拟回合${turn + 1} ---------------------------`);
      
      // 在每个回合开始时，重新检查存活单位 (使用克隆副本)
      alivePlayerUnits = clonedPlayerUnits.filter(unit => unit.isAlive());
      aliveEnemyUnits = clonedEnemyUnits.filter(unit => unit.isAlive());
      
      // 如果任一方全部阵亡，结束战斗模拟
      if (alivePlayerUnits.length === 0 || aliveEnemyUnits.length === 0) {
        console.log(`模拟战斗结束：${alivePlayerUnits.length === 0 ? '玩家全部阵亡' : '敌方全部阵亡'}`);
        break;
      }
      
      console.log(`模拟回合${turn + 1}开始：${alivePlayerUnits.length}个玩家单位存活，${aliveEnemyUnits.length}个敌方单位存活`);
      
      // 所有存活单位合并为一个数组，按速度排序决定行动顺序 (使用克隆副本)
      const allUnits = [...alivePlayerUnits, ...aliveEnemyUnits];
      // --- 增加日志: 排序前 --- 
      console.log(`模拟回合 ${turn + 1} 排序前:`, allUnits.map(u => ({ name: u.name, speed: u.speed, type: u.type })));
      // --- 日志结束 ---
      allUnits.sort((a, b) => {
          // 主要按速度降序，速度相同则玩家优先
          if (b.speed !== a.speed) {
              return b.speed - a.speed;
          } else {
              return a.type === 'player' ? -1 : (b.type === 'player' ? 1 : 0);
          }
      });
      // --- 增加日志: 排序后 --- 
      console.log(`模拟回合 ${turn + 1} 排序后:`, allUnits.map(u => ({ name: u.name, speed: u.speed, type: u.type })));
      // --- 日志结束 ---
      
      // 为该回合中的每个单位生成一个行动
      for (let i = 0; i < allUnits.length; i++) {
        const actorClone = allUnits[i]; // 这是克隆的行动者
        
        // 再次检查克隆单位是否存活
        if (!actorClone.isAlive()) {
          // console.log(`模拟：单位[${actorClone.name}]已阵亡，跳过其行动`); // 减少日志量
          continue;
        }
        
        // 确定攻击目标 (使用克隆副本)
        let potentialTargets = actorClone.type === 'player' ? aliveEnemyUnits : alivePlayerUnits;
        
        // 如果没有可攻击的目标，跳过此单位的行动
        if (potentialTargets.length === 0) {
          // console.log(`模拟：单位[${actorClone.name}]没有可攻击的目标，跳过其行动`); // 减少日志量
          continue;
        }
        
        // 随机选择一个目标 (克隆副本)
        const targetClone = potentialTargets[Math.floor(Math.random() * potentialTargets.length)];
        
        // --- 修改开始: 获取原始单位引用，用于记录action --- 
        // 需要根据克隆对象的id或索引找到原始对象
        const originalActor = actorClone.type === 'player' 
            ? this.playerUnits.find(u => u.id === actorClone.id)
            : this.enemyUnits.find(u => u.id === actorClone.id);
            
        const originalTarget = targetClone.type === 'player' 
            ? this.playerUnits.find(u => u.id === targetClone.id)
            : this.enemyUnits.find(u => u.id === targetClone.id);
            
        // --- 增加检查: 确保找到原始单位 --- 
        if (!originalActor) {
            console.error(`模拟错误：找不到原始攻击者！ID: ${actorClone.id}, Name: ${actorClone.name}`);
            continue; // 跳过这个动作
        }
        if (!originalTarget) {
            console.error(`模拟错误：找不到原始目标！ID: ${targetClone.id}, Name: ${targetClone.name}`);
            continue; // 跳过这个动作
        }
        // --- 检查结束 ---
        
        // 计算基础伤害 (使用克隆副本的属性)
        let damage = Math.max(1, actorClone.attack - targetClone.defense / 2);
        
        // 暴击判定
        let isCrit = Math.random() < actorClone.critRate;
        if (isCrit) {
          damage = Math.floor(damage * actorClone.critDamage);
        }
        
        // 记录动作 (使用原始单位引用)
        actions.push({
          turn: turn,
          actor: originalActor, // 使用原始引用
          target: originalTarget, // 使用原始引用
          type: 'attack',
          damage: damage,
          isCrit: isCrit
        });
        
        // console.log(`模拟回合${turn + 1}：[${actorClone.name}] 攻击 [${targetClone.name}]，造成${damage}点伤害${isCrit ? '（暴击）' : ''}`); // 减少日志量
        
        // --- 修改开始: 更新克隆目标的生命值 --- 
        // 更新克隆目标生命值，但不低于0
        targetClone.hp = Math.max(0, targetClone.hp - damage);
        // 如果BattleUnit类有takeDamage方法，推荐使用：
        // targetClone.takeDamage(damage);
        // --- 修改结束 ---
        // console.log(`模拟：[${targetClone.name}] 剩余生命值：${targetClone.hp}/${targetClone.maxHp}`); // 减少日志量
        
        // 检查克隆目标是否阵亡
        if (targetClone.hp <= 0) {
          // console.log(`模拟：[${targetClone.name}] 已阵亡！`); // 减少日志量
          
          // 更新存活单位列表 (克隆副本)
          if (targetClone.type === 'player') {
            alivePlayerUnits = alivePlayerUnits.filter(u => u !== targetClone);
          } else {
            aliveEnemyUnits = aliveEnemyUnits.filter(u => u !== targetClone);
          }
          
          // 检查是否一方全部阵亡
          if (alivePlayerUnits.length === 0 || aliveEnemyUnits.length === 0) {
            console.log(`模拟战斗即将结束：${alivePlayerUnits.length === 0 ? '玩家全部阵亡' : '敌方全部阵亡'}`);
          }
        }
      }
      
      // console.log(`模拟回合${turn + 1}结束：${alivePlayerUnits.length}个玩家单位存活，${aliveEnemyUnits.length}个敌方单位存活`); // 减少日志量
    }
    
    // --- 修改开始: 使用克隆单位判断模拟结果 --- 
    // 设置战斗结果 (基于克隆单位的最终状态)
    const playerWin = clonedPlayerUnits.some(unit => unit.isAlive()) && !clonedEnemyUnits.some(unit => unit.isAlive());
    const enemyWin = !clonedPlayerUnits.some(unit => unit.isAlive()) && clonedEnemyUnits.some(unit => unit.isAlive());
    const draw = !clonedPlayerUnits.some(unit => unit.isAlive()) && !clonedEnemyUnits.some(unit => unit.isAlive());
    // --- 修改结束 ---
    
    // --- 修改: 将结果暂存，并添加到结束动作中 --- 
    const finalResult = {
      playerWin: playerWin,
      enemyWin: enemyWin,
      draw: draw,
      reason: playerWin ? '敌方全部阵亡' : (enemyWin ? '玩家全部阵亡' : (draw ? '双方全部阵亡' : '回合数达到上限'))
    };
    
    // 将模拟结果保存在 this.battleResults 供 skipBattle 使用
    this.battleResults = finalResult; 

    // 添加结束动作到序列中
    actions.push({
        type: 'end',
        result: finalResult
    });
    // --- 修改结束 ---
    
    console.log(`模拟战斗结果：${JSON.stringify(finalResult)}`);
    console.log(`共生成${actions.length}个战斗动作 (包含结束动作)`);
    
    return actions;
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    // 初始化跳过按钮
    this.initSkipButton();
  }
  
  /**
   * 初始化跳过按钮
   */
  initSkipButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.skipButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      margin,
      buttonWidth,
      buttonHeight,
      '跳过',
      null,
      null,
      () => {
        this.skipBattle();
      }
    );
    
    this.addUIElement(this.skipButton);
  }
  
  /**
   * 开始战斗
   */
  startBattle() {
    console.log('开始战斗...');
    
    // 重置所有战斗状态标志
    this.hasEndedBattle = false;
    this.battleEndingInProgress = false;
    this.resultShown = false;
    
    // 确保战斗相关变量正确初始化
    this.isBattleActive = true;
    this.currentActionIndex = 0;
    this.lastActionTime = Date.now();
    this.battleResults = null; // 清空之前的战斗结果
    this.battleResult = null;  // 清空之前的战斗结果
    this.rewards = null;       // 清空之前的奖励
    
    // 清除可能的超时处理器
    if (this.processNextActionTimeout) {
      clearTimeout(this.processNextActionTimeout);
      this.processNextActionTimeout = null;
    }
    
    if (this.battleEndTimeout) {
      clearTimeout(this.battleEndTimeout);
      this.battleEndTimeout = null;
    }
    
    // 清除伤害文本效果
    this.damageTexts = [];
    
    // 检查战斗动作序列是否已设置
    if (!this.battleState || !this.battleState.actions || this.battleState.actions.length === 0) {
      console.warn('战斗动作序列为空，尝试生成简单动作序列');
      
      // 如果没有预设的动作序列，生成一个简单的
      this.battleState = this.battleState || {};
      this.battleState.actions = this.generateSimpleBattleActions();
    }
    
    // 保存战斗动作序列
    this.battleActions = this.battleState.actions;
    console.log('战斗动作序列:', this.battleActions);
    
    // 初始化回合为0，第一个回合动作会将其设为1
    this.battleState.turn = 0;
    
    // 设置战斗单位的显示位置
    this.setupUnitPositions();
    
    console.log('战斗初始化完成，玩家单位:', this.playerUnits);
    console.log('战斗初始化完成，敌人单位:', this.enemyUnits);
    
    // 确保所有单位都有isAlive方法
    this.playerUnits.forEach(unit => {
      if (typeof unit.isAlive !== 'function') {
        unit.isAlive = function() { return this.hp > 0; };
      }
    });
    
    this.enemyUnits.forEach(unit => {
      if (typeof unit.isAlive !== 'function') {
        unit.isAlive = function() { return this.hp > 0; };
      }
    });
    
    // 设置超时以开始处理第一个动作（给足够时间让场景完全渲染）
    setTimeout(() => {
      console.log('开始执行战斗动作序列...');
      if (this.isBattleActive && this.battleActions && this.battleActions.length > 0) {
        this.processNextAction();
      } else {
        console.warn('战斗状态异常，无法开始执行动作');
      }
    }, 500);
  }
  
  /**
   * 设置战斗单位的显示位置
   */
  setupUnitPositions() {
    // 确保每个单位都有正确的位置索引
    this.playerUnits.forEach((unit, index) => {
      // 首先检查 formationPosition，如果有使用 formationPosition
      if (unit.formationPosition !== undefined && unit.formationPosition < this.playerPositions.length) {
        unit.positionIndex = unit.formationPosition;
      } 
      // 如果没有 formationPosition 或超出范围，使用索引
      else if (unit.positionIndex === undefined || unit.positionIndex >= this.playerPositions.length) {
        unit.positionIndex = index % this.playerPositions.length;
      }
      
      // 只在未设置maxHp和hp时才设置默认值
      if (unit.maxHp === undefined) {
        unit.maxHp = 100;
        console.log(`玩家单位 ${unit.name} 在setupUnitPositions中设置默认maxHp: ${unit.maxHp}`);
      }
      
      if (unit.hp === undefined) {
        unit.hp = unit.maxHp;
        console.log(`玩家单位 ${unit.name} 在setupUnitPositions中设置默认hp: ${unit.hp}`);
      }
      
      console.log(`玩家单位 ${unit.name} 位置索引: ${unit.positionIndex}, 血量: ${unit.hp}/${unit.maxHp}`);
    });
    
    this.enemyUnits.forEach((unit, index) => {
      // 首先检查 formationPosition，如果有使用 formationPosition
      if (unit.formationPosition !== undefined && unit.formationPosition < this.enemyPositions.length) {
        unit.positionIndex = unit.formationPosition;
      } 
      // 如果没有 formationPosition 或超出范围，使用索引
      else if (this.enemyPositions && this.enemyPositions[index]) {
        unit.positionIndex = index % this.enemyPositions.length;
      }
      
      // 只在未设置maxHp和hp时才设置默认值
      if (unit.maxHp === undefined) {
        unit.maxHp = 100;
        console.log(`敌方单位 ${unit.name} 在setupUnitPositions中设置默认maxHp: ${unit.maxHp}`);
      }
      
      if (unit.hp === undefined) {
        unit.hp = unit.maxHp;
        console.log(`敌方单位 ${unit.name} 在setupUnitPositions中设置默认hp: ${unit.hp}`);
      }
      
      console.log(`敌方单位 ${unit.name} 位置索引: ${unit.positionIndex}, 血量: ${unit.hp}/${unit.maxHp}`);
    });
  }
  
  /**
   * 跳过战斗，直接显示结果
   */
  skipBattle() {
    console.log('玩家点击了跳过战斗按钮');
    
    // 清除任何可能的延迟调用
    if (this.processNextActionTimeout) {
      clearTimeout(this.processNextActionTimeout);
      this.processNextActionTimeout = null;
    }
    
    // 如果已有预先计算的战斗结果，直接使用
    if (this.battleResults) {
      console.log('使用预先计算的战斗结果:', this.battleResults);
      this.battleResult = this.battleResults.playerWin ? 'victory' : 
                          (this.battleResults.enemyWin ? 'defeat' : 'draw');
      this.rewards = this.battleResults.rewards || {};
      
      // 立即结束战斗，不显示动画
      this.endBattle(true);
    } else {
      // 如果没有预先计算的结果，检查当前战斗状态
      const anyPlayerAlive = this.playerUnits.some(unit => unit.isAlive());
      const anyEnemyAlive = this.enemyUnits.some(unit => unit.isAlive());
      
      if (!anyPlayerAlive || !anyEnemyAlive) {
        // 如果战斗已经可以判定结果
        this.battleResult = anyPlayerAlive && !anyEnemyAlive ? 'victory' : 'defeat';
        console.log(`根据当前战斗状态确定结果: ${this.battleResult}`);
      } else {
        // 否则强制结束为失败
        console.warn('没有预先计算的战斗结果且战斗未决出胜负，强制结束为失败');
        this.battleResult = 'defeat';
      }
      
      this.endBattle(true);
    }
  }
  
  /**
   * 检查战斗是否结束
   * @returns {boolean} 如果战斗结束返回true，否则返回false
   */
  isBattleOver() {
    // 如果战斗已经标记为结束，不再检查
    if (!this.isBattleActive) {
      return true;
    }

    try {
      // 检查是否有任何玩家单位存活
      const anyPlayerAlive = this.playerUnits.some(unit => {
        const isAlive = typeof unit.isAlive === 'function' ? unit.isAlive() : unit.hp > 0;
        return isAlive;
      });
      
      // 检查是否有任何敌人单位存活
      const anyEnemyAlive = this.enemyUnits.some(unit => {
        const isAlive = typeof unit.isAlive === 'function' ? unit.isAlive() : unit.hp > 0;
        return isAlive;
      });
      
      // 根据单位存活状态确定战斗结果
      if (!anyPlayerAlive) {
        console.log("所有玩家单位已阵亡，战斗结束");
        this.battleResult = 'defeat';
        this.isBattleActive = false;
        
        // 直接结束战斗，不再使用setTimeout
        this.endBattle(false);
        return true;
      }
      
      if (!anyEnemyAlive) {
        console.log("所有敌人单位已阵亡，战斗结束");
        this.battleResult = 'victory';
        this.isBattleActive = false;
        
        // 设置默认奖励
        if (!this.rewards) {
          this.rewards = {
            exp: 50,
            lingshi: 100
          };
        }
        
        // 直接结束战斗，不再使用setTimeout
        this.endBattle(false);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error("检查战斗状态时出错:", error);
      // 发生错误时，为安全起见标记战斗结束
      this.battleResult = 'error';
      this.isBattleActive = false;
      this.endBattle(true);
      return true;
    }
  }
  
  /**
   * 结束战斗
   * @param {boolean} skipped - 是否是跳过战斗
   */
  endBattle(skipped = false) {
    console.log(`结束战斗，结果: ${this.battleResult}, 跳过: ${skipped}`);
    
    // 检查全局标志，避免重复处理
    if (this.hasEndedBattle === true) {
      console.warn('战斗已经在结束流程中，不能再次结束');
      return;
    }
    
    // 立即设置结束标志，防止重复调用
    this.hasEndedBattle = true;
    
    // 如果战斗已经标记为不活跃，但显示结果失败
    if (!this.isBattleActive) {
      // 强制关闭战斗场景并显示结算
      console.warn('战斗已经结束，强制显示结算界面');
      this.forceBattleEnd();
      return;
    }
    
    // 标记战斗已结束和正在结束处理中
    this.isBattleActive = false;
    this.battleEndingInProgress = true;
    
    // 清除任何可能的延迟调用
    if (this.processNextActionTimeout) {
      clearTimeout(this.processNextActionTimeout);
      this.processNextActionTimeout = null;
    }
    
    // 清除可能的战斗结束超时
    if (this.battleEndTimeout) {
      clearTimeout(this.battleEndTimeout);
      this.battleEndTimeout = null;
    }
    
    // 如果跳过了战斗，直接显示结果
    const delay = skipped ? 0 : 500; // 缩短延迟时间
    
    // 显示战斗结果
    this.battleEndTimeout = setTimeout(() => {
      this.battleEndTimeout = null;
      this.showBattleResult();
    }, delay);
  }
  
  /**
   * 强制结束战斗（用于处理异常情况）
   */
  forceBattleEnd() {
    console.log('强制结束战斗并显示结算界面');
    
    // 确保有战斗结果
    if (!this.battleResult) {
      // 检查是否有敌人存活
      const anyEnemyAlive = this.enemyUnits.some(unit => unit.hp > 0);
      this.battleResult = anyEnemyAlive ? 'defeat' : 'victory';
      console.log(`强制设置战斗结果: ${this.battleResult}`);
    }
    
    // 标记战斗已结束
    this.isBattleActive = false;
    this.hasEndedBattle = true;
    
    // 确保所有超时都被清除
    if (this.battleEndTimeout) {
      clearTimeout(this.battleEndTimeout);
      this.battleEndTimeout = null;
    }
    
    if (this.processNextActionTimeout) {
      clearTimeout(this.processNextActionTimeout);
      this.processNextActionTimeout = null;
    }
    
    // 直接显示结果界面
    this.showBattleResult();
  }
  
  /**
   * 显示战斗结果界面
   */
  showBattleResult() {
    // 防止重复调用
    if (this.resultShown) {
      console.warn('战斗结果已经显示，不再重复显示');
      return;
    }
    
    this.resultShown = true;
    
    if (this.onBattleComplete) {
      console.log('执行战斗完成回调，传递结果:', this.battleResult, '奖励:', this.rewards);
      
      try {
        this.onBattleComplete(this.battleResult, this.rewards);
      } catch (error) {
        console.error('执行战斗完成回调出错:', error);
        // 如果回调执行出错，尝试直接显示结算界面
        this.showBattleResultScene();
      }
    } else {
      console.warn('未设置战斗完成回调函数，尝试显示结算界面');
      this.showBattleResultScene();
    }
  }
  
  /**
   * 直接显示战斗结算场景
   */
  showBattleResultScene() {
    if (this.sceneManager) {
      console.log('显示战斗结算界面');
      this.sceneManager.showScene('battleResult', {
        result: { 
          victory: this.battleResult === 'victory',
          defeat: this.battleResult === 'defeat'
        },
        rewards: this.rewards || {},
        onComplete: () => {
          // 结算界面关闭后返回主场景
          this.sceneManager.showScene('main');
        }
      });
    } else {
      console.error('无法显示结算界面，尝试直接返回主场景');
      if (this.sceneManager) {
        this.sceneManager.showScene('main');
      }
    }
  }
  
  /**
   * 更新场景
   */
  updateScene() {
    // --- 日志: 确认更新循环 --- 
    // console.log('BattleScene updateScene'); // 减少日志量，必要时取消注释
    // --- 日志结束 ---
    
    // 如果战斗未激活，不更新
    if (!this.isBattleActive) {
      return;
    }
    
    // 检查是否应该执行下一个动作
    const now = Date.now();
    if (now - this.lastActionTime >= this.actionDelay) {
      this.processNextAction();
    }
  }
  
  /**
   * 处理下一个战斗动作
   */
  processNextAction() {
    // 如果战斗已经标记为结束，不再处理新动作
    if (this.hasEndedBattle) {
      console.log('战斗已经进入结束流程，不再处理动作');
      return;
    }
    
    // 检查战斗是否结束
    if (this.isBattleOver()) {
      console.log('检测到战斗结束，不再处理动作');
      // isBattleOver中会自动调用endBattle
      return;
    }
    
    // 如果战斗已被标记为不活跃，不再处理
    if (!this.isBattleActive) {
      console.log('战斗已终止，不再处理动作');
      return;
    }
    
    // 如果超出动作序列范围，结束战斗
    if (!this.battleActions || this.currentActionIndex >= this.battleActions.length) {
      console.log('动作序列已执行完毕，结束战斗');
      this.endBattle();
      return;
    }
    
    // 获取当前动作
    const action = this.battleActions[this.currentActionIndex];
    
    // 检查action是否有效
    if (!action) {
      console.warn(`第${this.currentActionIndex}个战斗动作无效，尝试跳至下一个`);
      this.currentActionIndex++;
      this.processNextAction();
      return;
    }
    
    // 处理特殊动作类型
    if (action.type === 'end') {
      console.log('遇到结束动作，直接结束战斗');
      this.endBattle();
      return;
    }
    
    // 处理动作
    console.log(`处理战斗动作[${this.currentActionIndex}/${this.battleActions.length}]:`, action);
    this.processAction(action);
    
    // 更新最后动作时间
    this.lastActionTime = Date.now();
    
    // 增加索引
    this.currentActionIndex++;
    
    // 再次检查战斗是否结束
    if (this.isBattleOver() || !this.isBattleActive || this.hasEndedBattle) {
      console.log('动作处理后战斗已结束，不再安排下一个动作');
      return;
    }
    
    // 设置延迟处理下一个动作（根据动作类型决定延迟时间）
    const actionDelay = action.type === 'attack' ? 800 : 500;
    
    // 清除任何已存在的超时处理
    if (this.processNextActionTimeout) {
      clearTimeout(this.processNextActionTimeout);
      this.processNextActionTimeout = null;
    }
    
    // 存储超时处理的引用，以便可以在skipBattle中清除
    this.processNextActionTimeout = setTimeout(() => {
      this.processNextActionTimeout = null; // 清除引用
      if (this.isBattleActive && !this.hasEndedBattle) {
        this.processNextAction();
      }
    }, actionDelay);
  }
  
  /**
   * 处理战斗动作
   * @param {Object} action 动作数据
   */
  processAction(action) {
    if (!action) {
      console.warn('处理空动作，跳过');
      return;
    }

    // 如果战斗已经结束，不再处理新动作
    if (!this.isBattleActive || this.hasEndedBattle) {
      console.log('战斗已结束，不再处理动作');
      return;
    }

    // 根据动作类型处理
    switch (action.type) {
      case 'attack':
        this.handleAttackAction(action);
        // 设置攻击标记，用于控制日志输出
        this.justAttacked = true;
        break;
      case 'skill':
        this.handleSkillAction(action);
        break;
      case 'item':
        this.handleItemAction(action);
        break;
      case 'message':
        this.showMessage(action.text);
        break;
      case 'delay':
        // 仅延迟，不做特殊处理
        break;
      case 'end':
        console.log('接收到end动作，结束战斗');
        this.endBattle();
        return;
      default:
        console.warn(`未知动作类型: ${action.type}`);
    }

    // 每次处理完动作后检查战斗是否结束
    if (this.isBattleOver()) {
      console.log('动作处理完毕后发现战斗已结束');
      // isBattleOver中会自动调用endBattle
      return;
    }
  }
  
  /**
   * 处理攻击动作
   * @param {Object} action 攻击动作数据
   */
  handleAttackAction(action) {
    console.log('处理攻击动作:', action);
    
    // 检查action数据结构
    if (!action.actor && !action.source) {
      console.error('攻击动作缺少攻击者信息:', action);
      return;
    }
    
    if (!action.target) {
      console.error('攻击动作缺少目标信息:', action);
      return;
    }
    
    // 获取攻击者(兼容两种格式)
    const attacker = action.actor || this.getUnitByReference(action.source);
    // 获取目标
    const target = this.getUnitByReference(action.target);
    
    if (!attacker) {
      console.error('找不到攻击者:', action);
      return;
    }
    
    if (!target) {
      console.error('找不到目标:', action);
      return;
    }
    
    console.log(`${attacker.name} 攻击 ${target.name}, 预计伤害: ${action.damage}, 暴击: ${action.isCrit}`);
    
    // 拿到伤害值，优先使用action中的预设伤害
    const damage = action.damage || this.calculateDamage(attacker, target, action);
    const isCrit = !!action.isCrit;
    
    // 应用伤害
    const prevHp = target.hp;
    target.hp = Math.max(0, target.hp - damage);
    
    console.log(`${target.name} 受到 ${damage} 点伤害，HP: ${prevHp} -> ${target.hp}/${target.maxHp}`);
    
    // 创建伤害文本显示
    this.createDamageText(target, damage, isCrit);
    
    // 检查目标是否死亡
    if (target.hp <= 0 && prevHp > 0) {
      console.log(`${target.name} 已被击败!`);
      
      // 重新检查战斗是否结束
      if (this.isBattleOver()) {
        console.log('根据单位存活状态, 战斗应该结束');
        // 不在这里直接结束，而是在processAction中检查
      }
    }
  }
  
  /**
   * 播放攻击动画
   * @param {Object} action 攻击动作数据
   */
  playAttackAnimation(action) {
    if (!action.source || !action.target) {
      console.error('攻击动作缺少源或目标:', action);
      return;
    }
    
    const sourceUnit = action.source.unit;
    const targetUnit = action.target.unit;
    
    if (!sourceUnit || !targetUnit) {
      console.error('攻击单位不存在:', action);
      return;
    }
    
    console.log(`${sourceUnit.name} 攻击 ${targetUnit.name}, 造成 ${action.damage} 点伤害${action.isCrit ? '（暴击）' : ''}`);
    
    // 设置当前动画
    this.currentAnimation = {
      type: 'attack',
      sourceType: action.source.type,
      sourceIndex: action.source.index,
      targetType: action.target.type,
      targetIndex: action.target.index,
      damage: action.damage,
      isCrit: action.isCrit,
      startTime: Date.now(),
      duration: 500
    };
    
    // 应用伤害（动画完成后应用，但这里直接应用）
    targetUnit.hp = Math.max(0, targetUnit.hp - action.damage);
  }
  
  /**
   * 播放技能动画
   * @param {Object} action 技能动作数据
   */
  playSkillAnimation(action) {
    // 技能动画待实现
    console.log('技能动画待实现');
  }
  
  /**
   * 场景隐藏回调
   */
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制战斗场景
    this.drawBattleField();
    
    // 绘制单位
    this.drawUnits();
    
    // --- 增加: 绘制伤害文字 --- 
    this.drawDamageTexts();
    // --- 增加结束 ---
    
    // 绘制动画
    if (this.currentAnimation) {
      this.drawAnimation();
    }
    
    // 绘制战斗UI (例如回合数)
    this.drawBattleUI();

    // --- 移除: BaseScene 会自动处理 UI 元素绘制 ---
    // this.drawUIElements(); 
    // --- 移除结束 ---
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制背景图
    if (this.resources && this.resources.battleBackground) {
      this.ctx.drawImage(
        this.resources.battleBackground,
        0,
        0,
        this.screenWidth,
        this.screenHeight
      );
    } else {
      // 如果没有背景图，绘制简单渐变背景
      const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
      gradient.addColorStop(0, '#000033');
      gradient.addColorStop(1, '#000066');
      
      this.ctx.fillStyle = gradient;
      this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    }
  }
  
  /**
   * 绘制战场
   */
  drawBattleField() {
    // 删除中心分割线，改为上下分布的战场
    // 可以绘制一个分隔区域
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    const dividerY = this.battleAreaY + this.battleAreaHeight * 0.5;
    const dividerHeight = 20;
    this.ctx.fillRect(
      this.battleAreaX, 
      dividerY - dividerHeight / 2, 
      this.battleAreaWidth, 
      dividerHeight
    );
    
    // 可以加上装饰性的元素
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.setLineDash([5, 3]);
    this.ctx.beginPath();
    this.ctx.moveTo(this.battleAreaX, dividerY);
    this.ctx.lineTo(this.battleAreaX + this.battleAreaWidth, dividerY);
    this.ctx.stroke();
    this.ctx.setLineDash([]);
  }
  
  /**
   * 绘制单位
   */
  drawUnits() {
    // 依次绘制敌人单位
    this.enemyUnits.forEach((unit, index) => {
      // 获取该单位的位置信息
      const position = this.enemyPositions[index];
      
      if (!position || position.x === undefined || position.y === undefined ||
          isNaN(position.x) || isNaN(position.y)) {
        console.warn(`敌人单位 ${unit.name || index} 的位置无效:`, position);
        return;
      }
      
      // 确保血量信息存在
      const currentHP = typeof unit.hp === 'number' ? unit.hp : (unit.attributes ? unit.attributes.hp : 100);
      const maxHP = typeof unit.maxHp === 'number' ? unit.maxHp : (unit.attributes ? unit.attributes.hp : 100);
      
      // 只在攻击动作后输出日志
      if (this.justAttacked) {
        console.log(`绘制敌人单位 ${unit.name || index}, 坐标: (${position.x}, ${position.y}), HP: ${currentHP}/${maxHP}`);
      }
      
      // 绘制单位
      this.drawUnit(unit, position.x, position.y, this.unitSize, 'enemy');
      
      // 绘制血量条
      this.drawHealthBar(
        position.x,
        position.y + this.unitSize + 5, // 调整Y坐标，放在单位下方
        this.unitSize,
        10,
        currentHP,
        maxHP,
        'red'
      );
    });
    
    // 依次绘制玩家单位
    this.playerUnits.forEach((unit, index) => {
      // 获取该单位的位置信息
      const position = this.playerPositions[index];
      
      if (!position || position.x === undefined || position.y === undefined ||
          isNaN(position.x) || isNaN(position.y)) {
        console.warn(`玩家单位 ${unit.name || index} 的位置无效:`, position);
        return;
      }

      // 确保血量信息存在
      const currentHP = typeof unit.hp === 'number' ? unit.hp : (unit.attributes ? unit.attributes.hp : 100);
      const maxHP = typeof unit.maxHp === 'number' ? unit.maxHp : (unit.attributes ? unit.attributes.hp : 100);
      
      // 只在攻击动作后输出日志
      if (this.justAttacked) {
        console.log(`绘制玩家单位 ${unit.name || index}, 坐标: (${position.x}, ${position.y}), HP: ${currentHP}/${maxHP}`);
      }
      
      // 绘制单位
      this.drawUnit(unit, position.x, position.y, this.unitSize, 'player'); // 使用this.unitSize
      
      // 绘制血量条
      this.drawHealthBar(
        position.x,
        position.y + this.unitSize + 5, // 调整Y坐标，放在单位下方
        this.unitSize, // 使用this.unitSize
        10, 
        currentHP, 
        maxHP, 
        'green'
      );
    });
    
    // 重置攻击标记
    this.justAttacked = false;
  }
  
  /**
   * 绘制血量条
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} currentHP - 当前血量
   * @param {number} maxHP - 最大血量
   * @param {string} color - 血量条颜色
   */
  drawHealthBar(x, y, width, height, currentHP, maxHP, color) {
    // 血量百分比
    const healthPercent = Math.max(0, Math.min(1, currentHP / maxHP));
    
    // 绘制血量条背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制血量
    this.ctx.fillStyle = color;
    this.ctx.fillRect(x, y, width * healthPercent, height);
    
    // 绘制血量文字
    this.ctx.font = '10px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `${currentHP}/${maxHP}`,
      x + width / 2,
      y + height - 2
    );
  }
  
  /**
   * 绘制单位
   * @param {Object} unit 单位数据
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {number} size 单位大小
   * @param {string} type 单位类型（玩家/敌人）
   */
  drawUnit(unit, x, y, size, type) {
    if (!unit) {
      console.warn(`尝试绘制空单位: ${type} at ${x},${y}`);
      return;
    }
    
    // 检查单位是否存活
    if (unit.hp <= 0) {
      // 绘制击败状态
      this.drawDefeatedUnit(unit, x, y, size, type);
      return;
    }
    
    // 尝试获取单位图像资源
    let resourceKey = '';
    if (type === 'player') {
      resourceKey = `character${unit.id}`;
    } else {
      resourceKey = `enemy${unit.id}`;
    }
    
    const unitResource = this.resources[resourceKey];
    
    // 只在攻击动作后输出详细日志
    if (this.justAttacked) {
      console.log(`绘制单位: ${unit.name}, 类型: ${type}, 使用资源: ${resourceKey}, 资源存在: ${!!unitResource}`);
    }
    
    if (unitResource) {
      // 绘制单位图像
      try {
        this.ctx.drawImage(unitResource, x, y, size, size);
      } catch (error) {
        console.warn(`绘制单位图像失败: ${error.message}`, unit);
        // 如果图像绘制失败，使用占位符
        this.drawUnitPlaceholder(x, y, size, type, unit);
      }
    } else {
      // 如果没有资源，绘制占位图形
      if (this.justAttacked) {
        console.log(`未找到单位图像资源 ${resourceKey}，使用占位符`);
      }
      this.drawUnitPlaceholder(x, y, size, type, unit);
    }
    
    // 绘制单位名称
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = type === 'player' ? '#88ff88' : '#ff8888';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    
    // 如果名称太长，截断显示
    let name = unit.name || '未命名';
    if (name.length > 8) {
      name = name.substring(0, 7) + '...';
    }
    
    this.ctx.fillText(name, x + size / 2, y + size + 5);
    
    // 绘制单位状态条
    this.drawUnitStatusBar(unit, x, y, size);
    
    // 绘制单位等级
    this.ctx.font = 'bold 10px Arial';
    this.ctx.fillStyle = '#ffff00';
    this.ctx.textBaseline = 'top';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`Lv.${unit.level || 1}`, x, y);
  }
  
  /**
   * 绘制击败状态的单位
   */
  drawDefeatedUnit(unit, x, y, size, type) {
    // 绘制半透明的单位
    this.ctx.globalAlpha = 0.5;
    this.drawUnitPlaceholder(x, y, size, type, unit);
    this.ctx.globalAlpha = 1.0;
    
    // 绘制X标记
    this.ctx.strokeStyle = '#ff0000';
    this.ctx.lineWidth = 3;
    
    // 绘制X
    this.ctx.beginPath();
    this.ctx.moveTo(x + 10, y + 10);
    this.ctx.lineTo(x + size - 10, y + size - 10);
    this.ctx.stroke();
    
    this.ctx.beginPath();
    this.ctx.moveTo(x + size - 10, y + 10);
    this.ctx.lineTo(x + 10, y + size - 10);
    this.ctx.stroke();
    
    // 绘制"击败"文本
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillStyle = '#ff0000';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('击败', x + size / 2, y + size / 2);
  }
  
  /**
   * 绘制单位占位图形
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {number} size 大小
   * @param {string} type 单位类型
   * @param {Object} unit 单位数据
   */
  drawUnitPlaceholder(x, y, size, type, unit) {
    // 根据单位类型设置颜色
    const hue = type === 'player' ? 200 : 0; // 玩家蓝色系，敌人红色系
    const id = unit.id || 0;
    
    // 根据ID调整饱和度和亮度，确保每个单位颜色不同
    const saturation = 70 + (id % 3) * 10;
    const lightness = 50 + (Math.floor(id / 3) % 3) * 10;
    
    this.ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    this.ctx.fillRect(x, y, size, size);
    
    // 添加图案和文字，使单位更易区分
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.beginPath();
    this.ctx.arc(x + size/2, y + size/2, size/3, 0, Math.PI * 2);
    this.ctx.fill();
    
    // 绘制单位名称首字母或ID
    const text = unit.name ? unit.name.charAt(0) : unit.id.toString();
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = 'white';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, x + size/2, y + size/2);
  }
  
  /**
   * 绘制单位状态条
   * @param {Object} unit 单位数据
   * @param {number} x 单位X坐标
   * @param {number} y 单位Y坐标
   * @param {number} size 单位大小
   */
  drawUnitStatusBar(unit, x, y, size) {
    const barWidth = size;
    const barHeight = 6;
    const barY = y - barHeight - 5;
    
    // 绘制血量背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(x, barY, barWidth, barHeight);
    
    // 计算血量比例
    const hpRatio = Math.max(0, Math.min(1, unit.hp / unit.maxHp));
    
    // 根据血量比例选择颜色
    let barColor;
    if (hpRatio > 0.6) {
      barColor = '#00ff00'; // 绿色
    } else if (hpRatio > 0.3) {
      barColor = '#ffff00'; // 黄色
    } else {
      barColor = '#ff0000'; // 红色
    }
    
    // 绘制血量条
    this.ctx.fillStyle = barColor;
    this.ctx.fillRect(x, barY, barWidth * hpRatio, barHeight);
    
    // 绘制血量文本
    this.ctx.font = '10px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(`${Math.ceil(unit.hp)}/${unit.maxHp}`, x + barWidth / 2, barY + barHeight / 2);
  }
  
  /**
   * 绘制动画
   */
  drawAnimation() {
    if (!this.currentAnimation) return;
    
    const now = Date.now();
    const elapsed = now - this.currentAnimation.startTime;
    
    // 如果动画已经结束，清除动画
    if (elapsed >= this.currentAnimation.duration) {
      this.currentAnimation = null;
      return;
    }
    
    // 动画进度 (0-1)
    const progress = Math.min(1, elapsed / this.currentAnimation.duration);
    
    // 根据动画类型绘制
    switch (this.currentAnimation.type) {
      case 'attack':
        this.drawAttackAnimation(progress);
        break;
      case 'skill':
        this.drawSkillAnimation(progress);
        break;
    }
  }
  
  /**
   * 绘制攻击动画
   * @param {number} progress 动画进度 (0-1)
   */
  drawAttackAnimation(progress) {
    const anim = this.currentAnimation;
    
    // 获取源单位和目标单位的位置
    const sourcePos = anim.sourceType === 'player' 
      ? this.playerPositions[anim.sourceIndex]
      : this.enemyPositions[anim.sourceIndex];
      
    const targetPos = anim.targetType === 'player'
      ? this.playerPositions[anim.targetIndex]
      : this.enemyPositions[anim.targetIndex];
    
    if (!sourcePos || !targetPos) return;
    
    // 绘制攻击线
    this.ctx.strokeStyle = anim.isCrit ? '#ff0000' : '#ffffff';
    this.ctx.lineWidth = anim.isCrit ? 3 : 2;
    this.ctx.beginPath();
    this.ctx.moveTo(sourcePos.x + sourcePos.size / 2, sourcePos.y + sourcePos.size / 2);
    this.ctx.lineTo(targetPos.x + targetPos.size / 2, targetPos.y + targetPos.size / 2);
    this.ctx.stroke();
    
    // 绘制伤害数字
    if (progress > 0.5) {
      const damageAlpha = (progress - 0.5) * 2; // 0.5-1 => 0-1
      this.ctx.fillStyle = anim.isCrit ? `rgba(255, 0, 0, ${damageAlpha})` : `rgba(255, 255, 255, ${damageAlpha})`;
      this.ctx.font = anim.isCrit ? 'bold 24px Arial' : 'bold 20px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      
      // 受击目标上方显示伤害值
      this.ctx.fillText(
        `-${anim.damage}${anim.isCrit ? '!' : ''}`, 
        targetPos.x + targetPos.size / 2, 
        targetPos.y - 20
      );
    }
    
    // 目标震动效果
    if (progress > 0.5) {
      const shake = Math.sin(progress * 10) * 5 * (1 - progress); // 震动幅度随时间减小
      this.ctx.save();
      this.ctx.translate(targetPos.x + shake, targetPos.y);
      
      // 受击闪烁效果
      if (anim.isCrit) {
        this.ctx.fillStyle = `rgba(255, 0, 0, ${0.5 * (1 - progress)})`;
        this.ctx.fillRect(0, 0, targetPos.size, targetPos.size);
      }
      
      this.ctx.restore();
    }
  }
  
  /**
   * 绘制战斗UI
   */
  drawBattleUI() {
    // 绘制回合信息
    if (this.battleState && this.battleState.turn) {
      this.ctx.font = 'bold 16px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`回合: ${this.battleState.turn}`, this.screenWidth / 2, 30);
    }
  }
  
  /**
   * 获取资源
   */
  getResources() {
    // 检查 game 对象是否存在
    if (!window.game || !game.resourceLoader) {
      console.warn('未找到game对象或resourceLoader');
      return {};
    }
    
    // 从资源加载器中获取已加载的资源
    this.resources = game.resourceLoader.resources || {};
    
    // 添加通用敌人图像占位符
    // 如果敌人资源不存在，创建并添加默认图像
    ['enemy1001', 'enemy1002', 'enemy1003', 'enemy1004', 'enemy1005'].forEach(enemyKey => {
      if (!this.resources[enemyKey]) {
        console.log(`为 ${enemyKey} 创建默认图像资源`);
        try {
          // 创建一个画布作为敌人的默认图像
          const canvas = wx.createCanvas();
          canvas.width = 100;
          canvas.height = 100;
          const ctx = canvas.getContext('2d');
          
          // 设置一个随机颜色，但保持红色系来表示敌人
          const hue = 0; // 红色基调
          const saturation = 70 + Math.floor(Math.random() * 30);
          const lightness = 40 + Math.floor(Math.random() * 20);
          
          // 绘制基本形状
          ctx.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
          ctx.fillRect(0, 0, 100, 100);
          
          // 添加图案，使敌人更易区分
          ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
          ctx.beginPath();
          ctx.arc(50, 50, 30, 0, Math.PI * 2);
          ctx.fill();
          
          // 添加敌人ID作为标识
          const enemyId = enemyKey.replace('enemy', '');
          ctx.font = 'bold 24px Arial';
          ctx.fillStyle = 'white';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(enemyId, 50, 50);
          
          // 将创建的画布保存为资源
          this.resources[enemyKey] = canvas;
        } catch (error) {
          console.error(`创建敌人 ${enemyKey} 默认图像失败:`, error);
        }
      }
    });
    
    return this.resources;
  }

  /**
   * 创建伤害文字效果
   * @param {Object} target 受击单位
   * @param {number} damage 伤害值
   * @param {boolean} isCrit 是否暴击
   */
  createDamageText(target, damage, isCrit) {
    if (!target || damage === undefined) return;

    // 获取目标单位在屏幕上的位置
    const unitPositions = target.type === 'player' ? this.playerPositions : this.enemyPositions;
    const position = unitPositions[target.positionIndex];
    if (!position) {
        console.warn(`无法为单位 ${target.name} 创建伤害文字，找不到位置信息`);
        return;
    }

    const text = `-${damage}${isCrit ? '!' : ''}`;
    const color = isCrit ? '#ff0000' : '#ffffff';
    const fontSize = isCrit ? 24 : 20;
    const duration = 1000; // 持续时间 (毫秒)
    const startTime = Date.now();
    const startX = position.x + position.size / 2;
    const startY = position.y - 10; // 从单位上方开始
    const endY = startY - 30; // 向上漂浮

    // 将伤害文字添加到动画列表
    if (!this.damageTexts) {
        this.damageTexts = [];
    }
    this.damageTexts.push({
        text,
        color,
        fontSize,
        startTime,
        duration,
        startX,
        startY,
        endY
    });
  }

  /**
   * 绘制所有活动的伤害文字
   */
  drawDamageTexts() {
    if (!this.damageTexts || this.damageTexts.length === 0) {
        return;
    }

    const now = Date.now();
    const remainingTexts = [];

    this.damageTexts.forEach(dt => {
        const elapsed = now - dt.startTime;
        if (elapsed >= dt.duration) {
            return; // 动画结束，不再绘制
        }

        const progress = elapsed / dt.duration;
        const currentY = dt.startY + (dt.endY - dt.startY) * progress; // 线性插值计算Y坐标
        const alpha = 1 - progress; // 透明度随时间减淡

        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.font = `bold ${dt.fontSize}px Arial`;
        this.ctx.fillStyle = dt.color;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(dt.text, dt.startX, currentY);
        this.ctx.restore();

        remainingTexts.push(dt); // 保留未结束的动画
    });

    this.damageTexts = remainingTexts;
  }

  /**
   * 根据引用获取战斗单位
   * @param {Object} ref 单位引用
   * @returns {Object} 单位对象
   */
  getUnitByReference(ref) {
    if (!ref) {
      console.error('单位引用为空');
      return null;
    }
    
    // 如果ref直接是BattleUnit实例，直接返回
    if (typeof ref.isAlive === 'function') {
      return ref;
    }
    
    // 确定单位类型
    let type = ref.type;
    if (!type) {
      // 尝试通过其他属性判断类型
      if (this.playerUnits.includes(ref)) {
        type = 'player';
      } else if (this.enemyUnits.includes(ref)) {
        type = 'enemy';
      }
    }
    
    // 获取对应类型的单位列表
    const units = type === 'player' ? this.playerUnits : this.enemyUnits;
    if (!units || !Array.isArray(units)) {
      console.error(`无法获取${type || '未知'}类型的单位列表`);
      return null;
    }
    
    // 如果ref直接是单位数组中的一个元素
    if (units.includes(ref)) {
      return ref;
    }
    
    // 根据索引获取单位
    if (ref.index !== undefined && units[ref.index]) {
      return units[ref.index];
    }
    
    // 根据ID获取单位
    if (ref.id !== undefined) {
      const unit = units.find(unit => unit.id === ref.id);
      if (unit) return unit;
    }
    
    // 根据positionIndex获取单位
    if (ref.positionIndex !== undefined) {
      const unit = units.find(unit => unit.positionIndex === ref.positionIndex);
      if (unit) return unit;
    }
    
    // 根据formationPosition获取单位
    if (ref.formationPosition !== undefined) {
      const unit = units.find(unit => unit.formationPosition === ref.formationPosition);
      if (unit) return unit;
    }
    
    // 如果还是找不到，记录详细信息
    console.warn('无法通过引用找到单位:', {
      referenceData: ref,
      availablePlayerUnits: this.playerUnits.map(u => ({id: u.id, name: u.name})),
      availableEnemyUnits: this.enemyUnits.map(u => ({id: u.id, name: u.name}))
    });
    
    return null;
  }

  /**
   * 计算伤害
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   * @param {Object} action 动作数据
   * @returns {number} 伤害值
   */
  calculateDamage(attacker, target, action) {
    // 如果action中已经有预定义的伤害值，优先使用
    if (action && typeof action.damage === 'number') {
      return action.damage;
    }
    
    // 如果attacker具有计算伤害的方法，优先使用
    if (attacker && typeof attacker.calculateDamage === 'function') {
      const isCrit = action && action.isCrit !== undefined ? action.isCrit : undefined;
      return attacker.calculateDamage(target, isCrit);
    }
    
    // 基础伤害计算
    let damage = 0;
    
    try {
      // 确保attacker和target都有效
      if (!attacker || !target) {
        console.error('计算伤害时单位无效:', { attacker, target });
        return 1; // 返回最小伤害
      }
      
      // 获取攻击者和目标的属性
      const attack = attacker.attack || 10;
      const defense = target.defense || 5;
      
      // 计算基础伤害
      damage = Math.max(1, attack - defense / 2);
      
      // 暴击判定
      const critRate = attacker.critical || attacker.critRate || 0.05;
      const critDamage = attacker.criticalDamage || attacker.critDamage || 1.5;
      
      // 如果action指定了是否暴击，使用指定值，否则随机判定
      const isCrit = action && action.isCrit !== undefined ? 
                      action.isCrit : 
                      (Math.random() < critRate);
      
      if (isCrit) {
        damage = Math.floor(damage * critDamage);
        console.log(`${attacker.name}的攻击暴击! 伤害 ${damage}`);
      }
    } catch (error) {
      console.error('计算伤害时出错:', error);
      damage = 1; // 出错时返回最小伤害
    }
    
    return Math.max(1, Math.floor(damage)); // 确保至少造成1点伤害
  }
}

export default BattleScene; 