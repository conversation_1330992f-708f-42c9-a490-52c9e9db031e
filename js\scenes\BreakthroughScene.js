/**
 * 突破场景类
 * 用于角色境界突破
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import REALM_CONFIG from '../config/RealmConfig.js';

class BreakthroughScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 角色ID
    this.characterId = null;

    // 返回场景
    this.returnScene = 'jingshi';

    // 是否使用突破丹
    this.useBreakthroughPill = false;

    // 突破丹ID
    this.breakthroughPillId = null;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      '#f44336',
      '#FFFFFF',
      () => {
        // 返回到静室场景
        this.sceneManager.showScene(this.returnScene);
      }
    );

    this.addUIElement(this.backButton);

    // 创建突破按钮
    const breakthroughButtonWidth = 150;
    const breakthroughButtonHeight = 50;

    this.breakthroughButton = new Button(
      this.ctx,
      (this.screenWidth - breakthroughButtonWidth) / 2,
      this.screenHeight * 0.8,
      breakthroughButtonWidth,
      breakthroughButtonHeight,
      '开始突破',
      '#4CAF50',
      '#FFFFFF',
      () => {
        this.performBreakthrough();
      }
    );

    this.addUIElement(this.breakthroughButton);

    // 创建使用突破丹按钮
    this.createBreakthroughPillButtons();
  }

  // 创建突破丹按钮
  createBreakthroughPillButtons() {
    const character = this.getCharacter();
    if (!character) return;

    // 获取玩家背包中的突破丹
    const items = game.gameStateManager.getItems();

    // 筛选出所有突破丹物品
    const allBreakthroughPills = items.filter(item => {
      // 首先确保 item 存在
      if (!item) return false;

      // 检查 id 是否包含 breakthrough_
      const idMatch = item.id && typeof item.id === 'string' && item.id.includes('breakthrough_');

      // 检查名称是否包含突破丹
      const nameMatch = item.name && typeof item.name === 'string' && item.name.includes('突破丹');

      // 检查描述是否包含突破成功率
      const descMatch = item.description && typeof item.description === 'string' && item.description.includes('突破成功率');

      return idMatch || nameMatch || descMatch;
    });

    console.log('找到突破丹物品数量:', allBreakthroughPills.length);

    if (allBreakthroughPills.length === 0) {
      console.log('没有找到突破丹物品');

      // 创建一个提示按钮
      const tipButton = new Button(
        this.ctx,
        (this.screenWidth - 200) / 2,
        this.screenHeight * 0.4,
        200,
        40,
        '没有突破丹，请获取',
        '#cccccc',
        '#000000',
        () => {
          wx.showToast({
            title: '请通过游历或商店获取突破丹',
            icon: 'none',
            duration: 2000
          });
        }
      );

      this.addUIElement(tipButton);
      return;
    }

    // 分类突破丹：通用突破丹和境界特定突破丹
    const generalPills = allBreakthroughPills.filter(pill =>
      !pill.effects || !pill.effects.cultivation
    );

    // 当前境界的特定突破丹
    const currentCultivation = character.cultivation;
    const specificPills = allBreakthroughPills.filter(pill =>
      pill.effects && pill.effects.cultivation &&
      currentCultivation.includes(pill.effects.cultivation)
    );

    // 合并并排序突破丹，境界特定突破丹优先
    const breakthroughPills = [...specificPills, ...generalPills];

    // 打印所有突破丹信息便于调试
    breakthroughPills.forEach(pill => {
      console.log(`突破丹: ${pill.name}, ID: ${pill.id}, 数量: ${pill.count}`);
    });

    const buttonWidth = 180;
    const buttonHeight = 40;
    const startY = this.screenHeight * 0.4;
    const margin = 15;

    // 创建突破丹按钮
    breakthroughPills.forEach((pill, index) => {
      // 如果数量为0，不创建按钮
      if (pill.count <= 0) return;

      // 根据是否选中设置按钮颜色
      const isSelected = this.breakthroughPillId === pill.id;
      const buttonColor = isSelected ? '#4CAF50' : '#3498db'; // 选中时为绿色，未选中时为蓝色
      const textColor = '#FFFFFF'; // 白色文字

      // 显示特定境界突破丹的标记
      let buttonText = `使用${pill.name} (${pill.count})`;
      if (pill.effects && pill.effects.cultivation) {
        buttonText = `[境界] ${buttonText}`;
      }

      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        startY + (buttonHeight + margin) * index,
        buttonWidth,
        buttonHeight,
        buttonText,
        buttonColor,
        textColor,
        () => {
          this.toggleBreakthroughPill(pill.id);
        }
      );

      this.addUIElement(button);
      console.log(`创建突破丹按钮: ${pill.name}, 数量: ${pill.count}`);
    });
  }

  // 切换使用突破丹
  toggleBreakthroughPill(pillId) {
    if (this.breakthroughPillId === pillId) {
      // 取消选择
      this.useBreakthroughPill = false;
      this.breakthroughPillId = null;
    } else {
      // 选择新的突破丹
      this.useBreakthroughPill = true;
      this.breakthroughPillId = pillId;
    }

    // 重新初始化UI，显示选中状态
    this.initUI();
  }

  // 执行突破
  performBreakthrough() {
    const character = this.getCharacter();
    if (!character) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 获取当前灵力和突破所需灵力
    const currentLingLi = player.resources.lingli || 0;
    const requiredLingLi = this.getBreakthroughRequirement(character.cultivation);

    if (currentLingLi < requiredLingLi) {
      wx.showToast({
        title: '灵力不足',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算突破成功率
    const successRate = this.getBreakthroughSuccessRate(character.cultivation);

    // 检查是否使用突破丹
    let bonusRate = 0;

    if (this.useBreakthroughPill && this.breakthroughPillId) {
      const items = game.gameStateManager.getItems();
      const breakthroughPill = items.find(item => item && typeof item.id === 'string' && item.id === this.breakthroughPillId && item.count > 0);

      if (breakthroughPill) {
        console.log(`使用突破丹: ${breakthroughPill.name}, ID: ${breakthroughPill.id}`);
        console.log('突破丹数据:', JSON.stringify(breakthroughPill));

        // 检查是否是境界特定突破丹
        const isSpecificPill = breakthroughPill.effects &&
                              breakthroughPill.effects.cultivation &&
                              character.cultivation.includes(breakthroughPill.effects.cultivation);

        // 先检查effects属性
        if (breakthroughPill.effects) {
          // 如果是境界特定突破丹，使用效果值
          if (isSpecificPill && breakthroughPill.effects.breakthrough) {
            bonusRate = breakthroughPill.effects.breakthrough;
            console.log(`使用境界特定突破丹，加成${bonusRate * 100}%`);
          }
          // 否则使用通用突破丹效果
          else if (breakthroughPill.effects.breakthrough) {
            bonusRate = breakthroughPill.effects.breakthrough;
            console.log(`使用通用突破丹，加成${bonusRate * 100}%`);
          }
        }
        // 再检查effect属性
        else if (breakthroughPill.effect) {
          if (breakthroughPill.effect.type === 'breakthrough' && breakthroughPill.effect.value) {
            bonusRate = breakthroughPill.effect.value;
            console.log(`从 effect.value 获取突破加成: ${bonusRate}`);
          }
        }
        // 最后根据品质猜测
        else {
          // 兼容旧版突破丹
          if (breakthroughPill.quality === 'rare') {
            bonusRate = 0.3; // 稀有品质增加30%成功率
          } else if (breakthroughPill.quality === 'good' || breakthroughPill.quality === 'uncommon') {
            bonusRate = 0.2; // 良好品质增加20%成功率
          } else {
            bonusRate = 0.1; // 普通品质增加10%成功率
          }
          console.log(`根据品质猜测突破加成: ${bonusRate * 100}%`);
        }

        // 如果还是没有获取到突破加成，根据名称猜测
        if (bonusRate <= 0) {
          if (breakthroughPill.name.includes('普通突破丹')) {
            bonusRate = 0.1;
          } else if (breakthroughPill.name.includes('上品突破丹')) {
            bonusRate = 0.2;
          } else if (breakthroughPill.name.includes('稀有突破丹')) {
            bonusRate = 0.3;
          } else if (breakthroughPill.name.includes('突破丹')) {
            // 默认突破丹
            bonusRate = 0.2;
          }
          console.log(`从名称猜测突破加成: ${bonusRate * 100}%`);
        }

        // 使用一颗突破丹
        game.gameStateManager.removeItem(breakthroughPill.id, 1);
      }
    }

    // 最终成功率
    const finalSuccessRate = Math.min(0.95, successRate + bonusRate);

    // 随机决定是否突破成功
    const random = Math.random();
    if (random <= finalSuccessRate) {
      // 突破成功
      const nextCultivation = this.getNextCultivation(character.cultivation);
      character.cultivation = nextCultivation;

      // 如果角色有lingli属性，将其设置为0（兼容旧版本）
      if (character.hasOwnProperty('lingli')) {
        character.lingli = 0;
      }

      // 重置角色的exp属性
      character.exp = 0;

      // 提升角色等级
      character.level += 1;
      console.log(`角色 ${character.name} 等级提升到 ${character.level}`);

      // 从REALM_CONFIG中获取下一个等级的配置
      const nextLevel = this.getLevelFromCultivation(nextCultivation);
      const nextLevelConfig = REALM_CONFIG.find(config => config.level === nextLevel);

      // 更新角色属性
      if (nextLevelConfig && nextLevelConfig.baseAttributes) {
        character.attributes.hp = nextLevelConfig.baseAttributes.hp;
        character.attributes.mp = nextLevelConfig.baseAttributes.hp / 2; // 假设法力值为生命值的一半
        character.attributes.attack = nextLevelConfig.baseAttributes.attack;
        character.attributes.defense = nextLevelConfig.baseAttributes.defense;
        character.attributes.speed = nextLevelConfig.baseAttributes.speed;
      }

      // 其他属性增长
      character.attributes.speed += 5;
      character.attributes.critRate += 0.01; // 每境界增加1%暴击率
      character.attributes.critDamage += 0.05; // 每境界增加0.05倍暴击伤害

      // 如果是大境界突破（从练气期到筑基期等），额外增加属性
      if (nextCultivation.includes('筑基') && character.cultivation.includes('练气期') ||
          nextCultivation.includes('金丹') && character.cultivation.includes('筑基') ||
          nextCultivation.includes('元婴') && character.cultivation.includes('金丹') ||
          nextCultivation.includes('化神') && character.cultivation.includes('元婴') ||
          nextCultivation.includes('返虚') && character.cultivation.includes('化神') ||
          nextCultivation.includes('合道') && character.cultivation.includes('返虚') ||
          nextCultivation.includes('渡劫') && character.cultivation.includes('合道') ||
          nextCultivation.includes('大乘') && character.cultivation.includes('渡劫')) {
        character.attributes.daoRule += 1; // 增加大道法则
        character.attributes.speed += 5; // 额外增加速度
      }

      // 保存旧战力用于比较
      const oldPower = character.power || 0;

      // 重新计算战力
      const newPower = character.calculatePower();

      // 更新角色
      game.gameStateManager.updateCharacter(character.id, character);

      // 扣除灵力
      player.resources.lingli -= requiredLingLi;

      // 重置突破加成
      player.breakthroughBonus = 0;

      game.gameStateManager.setPlayer(player);

      // 显示突破成功提示
      wx.showToast({
        title: '突破成功',
        icon: 'success',
        duration: 1000
      });

      // 延迟1秒后计算战力并显示战力增长动画
      setTimeout(() => {
        // 计算玩家战力
        game.calculatePlayerPower();

        // 如果角色战力增加，触发角色战力增长事件
        if (newPower > oldPower) {
          console.log(`角色战力增长: ${oldPower} -> ${newPower}, 增长: ${newPower - oldPower}`);

          // 触发角色战力增长事件
          if (game.eventSystem) {
            game.eventSystem.emit('characterPowerIncrease', {
              characterId: character.id,
              oldPower,
              newPower,
              increase: newPower - oldPower
            });
          }
        }
      }, 1000); // 延迟1秒，等待突破成功提示消失

      // 突破成功后重新初始化UI
      this.initUI();
    } else {
      // 突破失败
      // 增加突破成功率
      if (!player.breakthroughBonus) {
        player.breakthroughBonus = 0;
      }
      player.breakthroughBonus += 0.05; // 每次失败增加5%成功率

      // 最高加成30%
      if (player.breakthroughBonus > 0.3) {
        player.breakthroughBonus = 0.3;
      }

      // 不扣除灵力，不添加冷却时间
      game.gameStateManager.setPlayer(player);

      // 显示失败提示
      wx.showToast({
        title: `突破失败，成功率+5%`,
        icon: 'none',
        duration: 2000
      });

      // 突破失败后重新初始化UI
      this.initUI();
    }
  }

  // 获取角色
  getCharacter() {
    if (!this.characterId) return null;
    return game.gameStateManager.getCharacterById(this.characterId);
  }

  // 获取突破所需灵力
  getBreakthroughRequirement(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取下一个等级的配置
    const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

    // 如果找到下一个等级的配置，返回所需灵力，否则返回一个很大的值
    return nextLevelConfig ? nextLevelConfig.requiredLingLi : 999999;
  }

  // 根据境界名称获取等级
  getLevelFromCultivation(cultivation) {
    // 查找匹配的境界配置
    const realmConfig = REALM_CONFIG.find(config => config.name === cultivation);

    // 如果找到匹配的配置，返回等级，否则返回1
    return realmConfig ? realmConfig.level : 1;
  }

  // 获取突破成功率
  getBreakthroughSuccessRate(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取当前等级的配置
    const currentLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel);

    // 获取玩家的突破加成
    const player = game.gameStateManager.getPlayer();
    const extraBonus = player && player.breakthroughBonus ? player.breakthroughBonus : 0;

    // 如果找到当前等级的配置，返回成功率加上玩家的突破加成，否则返回默认值
    return currentLevelConfig ? Math.min(currentLevelConfig.successRate + extraBonus, 0.95) : 0.9;
  }

  // 获取下一个境界
  getNextCultivation(cultivation) {
    // 从REALM_CONFIG中获取当前境界对应的等级
    const currentLevel = this.getLevelFromCultivation(cultivation);

    // 获取下一个等级的配置
    const nextLevelConfig = REALM_CONFIG.find(config => config.level === currentLevel + 1);

    // 如果找到下一个等级的配置，返回境界名称，否则返回当前境界
    return nextLevelConfig ? nextLevelConfig.name : cultivation;
  }

  // 场景显示时的回调
  onShow(params) {
    if (params) {
      if (params.characterId) {
        this.characterId = params.characterId;
      }

      if (params.returnScene) {
        this.returnScene = params.returnScene;
      }
    }

    // 初始化UI
    this.initUI();
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制突破信息
    this.drawBreakthroughInfo();
  }

  // 绘制背景
  drawBackground() {
    // 纯白色背景
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制突破信息
  drawBreakthroughInfo() {
    const character = this.getCharacter();
    if (!character) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 绘制标题
    this.ctx.font = 'bold 28px Arial';
    this.ctx.fillStyle = '#000000';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('境界突破', this.screenWidth / 2, 60);

    // 绘制角色名称和当前境界
    this.ctx.font = 'bold 22px Arial';
    this.ctx.fillStyle = '#000000';
    this.ctx.fillText(character.name, this.screenWidth / 2, 100);

    this.ctx.font = '20px Arial';
    this.ctx.fillStyle = '#000000';
    this.ctx.fillText(`当前境界: ${character.cultivation}`, this.screenWidth / 2, 130);

    // 获取下一个境界
    const nextCultivation = this.getNextCultivation(character.cultivation);
    this.ctx.fillStyle = '#ffcc00';
    this.ctx.fillText(`目标境界: ${nextCultivation}`, this.screenWidth / 2, 160);

    // 获取当前灵力和突破所需灵力
    const currentLingLi = player.resources.lingli || 0;
    const requiredLingLi = this.getBreakthroughRequirement(character.cultivation);

    // 绘制灵力信息
    this.ctx.fillStyle = '#000000';
    this.ctx.fillText(`当前灵力: ${currentLingLi}/${requiredLingLi}`, this.screenWidth / 2, 200);

    // 绘制灵力进度条
    const barWidth = this.screenWidth * 0.7;
    const barHeight = 20;
    const barX = (this.screenWidth - barWidth) / 2;
    const barY = 220;

    // 进度条背景
    this.ctx.fillStyle = 'rgba(100, 100, 100, 0.5)';
    this.ctx.fillRect(barX, barY, barWidth, barHeight);

    // 进度条进度
    const progress = Math.min(1, currentLingLi / requiredLingLi);
    this.ctx.fillStyle = '#4CAF50';
    this.ctx.fillRect(barX, barY, barWidth * progress, barHeight);

    // 绘制突破成功率
    let successRate = this.getBreakthroughSuccessRate(character.cultivation);
    let bonusRate = 0;

    // 检查是否使用突破丹
    if (this.useBreakthroughPill && this.breakthroughPillId) {
      const items = game.gameStateManager.getItems();
      const breakthroughPill = items.find(item => item && typeof item.id === 'string' && item.id === this.breakthroughPillId);

      if (breakthroughPill) {
        // 检查是否是境界特定突破丹
        const isSpecificPill = breakthroughPill.effects &&
                              breakthroughPill.effects.cultivation &&
                              character.cultivation.includes(breakthroughPill.effects.cultivation);

        // 如果是境界特定突破丹，使用效果值
        if (isSpecificPill && breakthroughPill.effects.breakthrough) {
          bonusRate = breakthroughPill.effects.breakthrough;

          // 绘制境界特定突破丹信息
          this.ctx.fillStyle = '#00ffff'; // 青色
          this.ctx.fillText(`[境界特定] ${breakthroughPill.name}: +${(bonusRate * 100).toFixed(0)}%`, this.screenWidth / 2, 300);
        }
        // 否则根据品质提供不同加成
        else if (breakthroughPill.effects && breakthroughPill.effects.breakthrough) {
          bonusRate = breakthroughPill.effects.breakthrough;

          // 绘制通用突破丹信息
          this.ctx.fillStyle = '#00ff00'; // 绿色
          this.ctx.fillText(`${breakthroughPill.name}: +${(bonusRate * 100).toFixed(0)}%`, this.screenWidth / 2, 300);
        } else {
          // 兼容旧版突破丹
          if (breakthroughPill.quality === 'rare') {
            bonusRate = 0.3; // 稀有品质增加30%成功率
          } else if (breakthroughPill.quality === 'good' || breakthroughPill.quality === 'uncommon') {
            bonusRate = 0.2; // 良好品质增加20%成功率
          } else {
            bonusRate = 0.1; // 普通品质增加10%成功率
          }

          // 绘制旧版突破丹信息
          this.ctx.fillStyle = '#00ff00'; // 绿色
          this.ctx.fillText(`${breakthroughPill.name}: +${(bonusRate * 100).toFixed(0)}%`, this.screenWidth / 2, 300);
        }
      }
    }

    // 最终成功率
    const finalSuccessRate = Math.min(0.95, successRate + bonusRate);

    // 绘制成功率信息
    this.ctx.fillStyle = '#000000';
    this.ctx.fillText(`基础成功率: ${(successRate * 100).toFixed(0)}%`, this.screenWidth / 2, 270);

    if (bonusRate > 0) {
      this.ctx.fillStyle = '#00ff00';
      this.ctx.fillText(`突破丹加成: +${(bonusRate * 100).toFixed(0)}%`, this.screenWidth / 2, 300);

      this.ctx.fillStyle = '#ffcc00';
      this.ctx.fillText(`最终成功率: ${(finalSuccessRate * 100).toFixed(0)}%`, this.screenWidth / 2, 330);
    }

    // 绘制突破加成信息
    if (player.breakthroughBonus && player.breakthroughBonus > 0) {
      this.ctx.fillStyle = '#FFA500'; // 橙色
      this.ctx.fillText(`突破加成: +${(player.breakthroughBonus * 100).toFixed(0)}%`, this.screenWidth / 2, 360);
    }

    // 绘制提示信息
    this.ctx.fillStyle = '#555555';
    this.ctx.font = '16px Arial';
    this.ctx.fillText('使用突破丹可以提高突破成功率', this.screenWidth / 2, 380);
    this.ctx.fillText('突破失败会增加5%成功率，直到突破成功', this.screenWidth / 2, 405);
  }
}

export default BreakthroughScene;
