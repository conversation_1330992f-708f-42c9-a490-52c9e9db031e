/**
 * 剑骨养成系统配置文件
 * 定义剑骨等级和品阶的属性加成和升级消耗
 */

// 剑骨等级配置（每级属性加成和升级消耗）
const SWORD_BONE_LEVEL_CONFIG = [
  // 等级0（初始状态）
  {
    level: 0,
    attributes: {
      attack: 0,
      critRate: 0,
      critDamage: 0,
      penetration: 0
    },
    // 升至下一级所需灵石
    upgradeCost: 1000
  },
  // 等级1
  {
    level: 1,
    attributes: {
      attack: 10,
      hp:100,
      critRate: 0.01,
      critDamage: 0.05,
      penetration: 5
    },
    upgradeCost: 2000
  },
  // 等级2
  {
    level: 2,
    attributes: {
      attack: 20,
      critRate: 0.02,
      critDamage: 0.10,
      penetration: 10
    },
    upgradeCost: 4000
  },
  // 等级3
  {
    level: 3,
    attributes: {
      attack: 30,
      critRate: 0.03,
      critDamage: 0.15,
      penetration: 15
    },
    upgradeCost: 8000
  },
  // 等级4
  {
    level: 4,
    attributes: {
      attack: 40,
      critRate: 0.04,
      critDamage: 0.20,
      penetration: 20
    },
    upgradeCost: 16000
  },
  // 等级5
  {
    level: 5,
    attributes: {
      attack: 50,
      critRate: 0.05,
      critDamage: 0.25,
      penetration: 25
    },
    upgradeCost: 32000
  },
  // 等级6
  {
    level: 6,
    attributes: {
      attack: 60,
      critRate: 0.06,
      critDamage: 0.30,
      penetration: 30
    },
    upgradeCost: 64000
  },
  // 等级7
  {
    level: 7,
    attributes: {
      attack: 70,
      critRate: 0.07,
      critDamage: 0.35,
      penetration: 35
    },
    upgradeCost: 128000
  },
  // 等级8
  {
    level: 8,
    attributes: {
      attack: 80,
      critRate: 0.08,
      critDamage: 0.40,
      penetration: 40
    },
    upgradeCost: 256000
  },
  // 等级9
  {
    level: 9,
    attributes: {
      attack: 90,
      critRate: 0.09,
      critDamage: 0.45,
      penetration: 45
    },
    upgradeCost: 512000
  },
  // 等级10（当前最高等级）
  {
    level: 10,
    attributes: {
      attack: 100,
      critRate: 0.10,
      critDamage: 0.50,
      penetration: 50
    },
    upgradeCost: null // 已达到最高等级
  }
];

// 剑骨品阶配置（每阶属性倍率和升阶消耗）
const SWORD_BONE_RANK_CONFIG = [
  // 品阶0（初始状态）
  {
    rank: 0,
    name: "凡骨",
    description: "普通的剑骨，尚未觉醒真正的力量",
    multiplier: 1.0, // 基础属性倍率
    // 升至下一阶所需仙玉
    upgradeCost: 100
  },
  // 品阶1
  {
    rank: 1,
    name: "通灵骨",
    description: "初步觉醒灵性的剑骨，能感知剑气流动",
    multiplier: 1.2, // 属性提升20%
    upgradeCost: 300
  },
  // 品阶2
  {
    rank: 2,
    name: "锋锐骨",
    description: "锋芒毕露的剑骨，能增强剑气锐利程度",
    multiplier: 1.5, // 属性提升50%
    upgradeCost: 600
  },
  // 品阶3
  {
    rank: 3,
    name: "玄金骨",
    description: "蕴含玄金之力的剑骨，坚韧无比",
    multiplier: 1.8, // 属性提升80%
    upgradeCost: 1000
  },
  // 品阶4
  {
    rank: 4,
    name: "龙脊骨",
    description: "拥有龙族血脉的剑骨，威力惊人",
    multiplier: 2.2, // 属性提升120%
    upgradeCost: 2000
  },
  // 品阶5
  {
    rank: 5,
    name: "仙灵骨",
    description: "蕴含仙灵之气的剑骨，超凡脱俗",
    multiplier: 2.6, // 属性提升160%
    upgradeCost: 3000
  },
  // 品阶6
  {
    rank: 6,
    name: "混沌骨",
    description: "混沌初开时形成的神秘剑骨，拥有毁天灭地之力",
    multiplier: 3.0, // 属性提升200%
    upgradeCost: 5000
  },
  // 品阶7
  {
    rank: 7,
    name: "太初骨",
    description: "太初之气凝结而成的剑骨，蕴含开天辟地之力",
    multiplier: 3.5, // 属性提升250%
    upgradeCost: 8000
  },
  // 品阶8
  {
    rank: 8,
    name: "道源骨",
    description: "大道本源凝结而成的剑骨，与天地共鸣",
    multiplier: 4.0, // 属性提升300%
    upgradeCost: 12000
  },
  // 品阶9（当前最高品阶）
  {
    rank: 9,
    name: "鸿蒙骨",
    description: "鸿蒙紫气孕育的至高剑骨，超越凡俗的极致",
    multiplier: 5.0, // 属性提升400%
    upgradeCost: null // 已达到最高品阶
  }
];

// 剑骨特殊效果配置（根据品阶解锁）
const SWORD_BONE_EFFECTS = [
  {
    rank: 1, // 通灵骨解锁
    name: "灵感",
    description: "感知敌人弱点，暴击率提高10%",
    effect: { critRateBonus: 0.1 }
  },
  {
    rank: 3, // 玄金骨解锁
    name: "破甲",
    description: "剑气能穿透敌人防御，穿透值提高20%",
    effect: { penetrationBonus: 0.2 }
  },
  {
    rank: 5, // 仙灵骨解锁
    name: "剑意",
    description: "剑气凝聚成剑意，暴击伤害提高30%",
    effect: { critDamageBonus: 0.3 }
  },
  {
    rank: 7, // 太初骨解锁
    name: "剑域",
    description: "形成剑气领域，攻击范围扩大，攻击力提高20%",
    effect: { attackBonus: 0.2 }
  },
  {
    rank: 9, // 鸿蒙骨解锁
    name: "剑道",
    description: "领悟剑道真谛，所有伤害提高30%",
    effect: { damageBonus: 0.3 }
  }
];

// 导出配置
export {
  SWORD_BONE_LEVEL_CONFIG,
  SWORD_BONE_RANK_CONFIG,
  SWORD_BONE_EFFECTS
};
