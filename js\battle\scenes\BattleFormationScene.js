/**
 * 战斗布阵场景
 * 用于玩家在战斗前布置阵容
 */
import BaseScene from '../../scenes/BaseScene';
import Button from '../../ui/Button';
import CharacterCard from '../ui/CharacterCard';
import game from '../../../game';

class BattleFormationScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 布阵相关数据
    this.enemyFormation = [];     // 敌方阵容
    this.availableCharacters = []; // 可用角色列表
    this.selectedCharacters = [];  // 已选择的角色
    this.maxFormationSize = 6;     // 最大阵容大小
    
    // 布阵完成回调
    this.onFormationComplete = null;
    
    // 布阵UI元素
    this.characterCards = [];      // 角色卡片列表
    this.formationSlots = [];      // 阵容槽位
    this.autoFormationButton = null; // 一键布阵按钮
    this.confirmButton = null;     // 布阵完成按钮
    this.backButton = null;        // 返回按钮
    
    // 页面布局参数
    this.headerHeight = 80;        // 顶部标题栏高度
    this.footerHeight = 80;        // 底部按钮区域高度
    this.formationAreaHeight = 300; // 阵容区域高度，增加以适应两行布局
    this.characterListPadding = 10; // 角色列表内边距
    this.characterCardGap = 10;     // 角色卡片间距
  }
  
  /**
   * 场景显示回调
   * @param {Object} params 场景参数
   */
  onShow(params) {
    console.log('布阵场景显示', params);
    
    // 清空UI元素
    this.clearUIElements();
    
    // 保存敌方阵容和回调
    this.enemyFormation = params.enemyFormation || [];
    this.stageData = params.stageData || {};
    this.onFormationComplete = params.onFormationComplete || null;
    
    // 获取玩家所有角色
    this.availableCharacters = game.gameStateManager.getCharacters() || [];
    
    // 输出调试信息
    console.log('可用角色数量:', this.availableCharacters.length);
    console.log('可用角色:', this.availableCharacters);
    
    // 确保角色有战力属性，没有则默认为0
    this.availableCharacters.forEach(character => {
      if (character.combatPower === undefined) {
        character.combatPower = character.power || 0;
      }
      // 确保角色有基本属性
      if (!character.level) character.level = 1;
      if (!character.name) character.name = "未知角色";
    });
    
    // 初始化已选角色列表
    this.selectedCharacters = [];
    
    // 尝试加载玩家上次的布阵数据
    const savedFormation = game.gameStateManager.getPlayerFormation();
    console.log('加载上次布阵数据:', savedFormation);
    
    if (savedFormation && savedFormation.length > 0) {
      // 将保存的角色ID转换为完整角色对象
      savedFormation.forEach(item => {
        const character = this.availableCharacters.find(c => c.id === item.id);
        if (character) {
          this.selectedCharacters.push(character);
          console.log(`成功加载布阵角色: ${character.name}`);
        } else {
          console.warn(`无法找到布阵角色ID: ${item.id}`);
        }
      });
      
      // 限制选择的角色数量
      if (this.selectedCharacters.length > this.maxFormationSize) {
        this.selectedCharacters = this.selectedCharacters.slice(0, this.maxFormationSize);
      }
    } else {
      console.log('没有找到保存的布阵数据');
    }
    
    // 初始化资源
    this.resources = game.resourceLoader.resources || {};
    
    // 初始化UI
    this.initUI();
    
    // 设置场景为可见
    this.visible = true;
    
    // 更新UI显示，确保布阵角色显示在对应位置
    this.updateUI();
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    // 资源初始化
    this.resources = game.resourceLoader.resources;
    
    // 初始化返回按钮
    this.initBackButton();
    
    // 初始化阵容槽位
    this.initFormationSlots();
    
    // 初始化角色列表
    this.initCharacterList();
    
    // 初始化一键布阵按钮
    this.initAutoFormationButton();
    
    // 初始化布阵完成按钮
    this.initConfirmButton();
  }
  
  /**
   * 初始化返回按钮
   */
  initBackButton() {
    const buttonSize = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonSize,
      buttonSize,
      '返回',
      null,
      null,
      () => {
        // 返回到前一个场景
        this.sceneManager.showScene('trial');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  /**
   * 初始化阵容槽位
   */
  initFormationSlots() {
    const slotWidth = 80;
    const slotHeight = 120;
    const columnGap = 20;
    const rowGap = 30;
    const columns = 3; // 每行3个槽位
    const rows = 2;    // 共2行
    
    // 计算整个阵型区域的宽度和高度
    const totalWidth = columns * slotWidth + (columns - 1) * columnGap;
    const totalHeight = rows * slotHeight + (rows - 1) * rowGap;
    
    // 计算起始坐标，使阵型居中
    const startX = (this.screenWidth - totalWidth) / 2;
    const startY = this.headerHeight + 30; // 增加顶部间距，确保不与标题重叠
    
    this.formationSlots = [];
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < columns; col++) {
        const index = row * columns + col;
        const x = startX + col * (slotWidth + columnGap);
        const y = startY + row * (slotHeight + rowGap);
        
        // 创建槽位卡片（空卡片，用于显示位置）
        const slotCard = new CharacterCard(
          this.ctx,
          x,
          y,
          slotWidth,
          slotHeight,
          null,
          this.resources,
          (character, isSelected) => {
            // 点击已放置的角色，将其移除
            if (character && this.selectedCharacters.includes(character)) {
              this.removeCharacterFromFormation(character);
            }
          }
        );
        
        // 添加位置信息，用于阵型布局
        slotCard.formationPosition = {
          row: row,
          col: col,
          index: index
        };
        
        this.formationSlots.push(slotCard);
        this.addUIElement(slotCard);
      }
    }
  }
  
  /**
   * 初始化角色列表
   */
  initCharacterList() {
    const cardWidth = 80;
    const cardHeight = 120;
    const gap = this.characterCardGap;
    const padding = this.characterListPadding;
    const cardsPerRow = Math.floor((this.screenWidth - padding * 2 + gap) / (cardWidth + gap));
    const startX = (this.screenWidth - (cardWidth * cardsPerRow + gap * (cardsPerRow - 1))) / 2;
    
    // 确保角色列表开始位置在阵容区域下方，避免重叠
    const startY = this.headerHeight + this.formationAreaHeight + 20; // 增加间距
    
    this.characterCards = [];
    
    // 按战力排序
    this.availableCharacters.sort((a, b) => b.combatPower - a.combatPower);
    
    this.availableCharacters.forEach((character, index) => {
      const row = Math.floor(index / cardsPerRow);
      const col = index % cardsPerRow;
      
      const x = startX + col * (cardWidth + gap);
      const y = startY + row * (cardHeight + gap);
      
      // 创建角色卡片
      const card = new CharacterCard(
        this.ctx,
        x,
        y,
        cardWidth,
        cardHeight,
        character,
        this.resources,
        (character, isSelected) => {
          // 点击卡片添加/移除角色
          if (isSelected) {
            this.addCharacterToFormation(character);
          } else {
            this.removeCharacterFromFormation(character);
          }
        }
      );
      
      // 配置拖拽回调
      this.setupCardDragEvents(card);
      
      this.characterCards.push(card);
      this.addUIElement(card);
    });
  }
  
  /**
   * 为卡片设置拖拽事件
   * @param {CharacterCard} card 角色卡片
   */
  setupCardDragEvents(card) {
    // 当前正在拖拽的卡片
    this.currentDraggingCard = null;
    
    // 拖拽开始
    card.setOnDragStart((card, x, y) => {
      this.currentDraggingCard = card;
      // 将卡片移到最上层
      this.bringCardToFront(card);
    });
    
    // 拖拽中
    card.setOnDrag((card, x, y, dx, dy) => {
      // 在拖拽过程中无需额外处理
    });
    
    // 拖拽结束
    card.setOnDragEnd((card, x, y) => {
      // 检查是否放置在阵容槽位上
      const targetSlot = this.findSlotAt(x, y);
      if (targetSlot) {
        // 如果放在阵容槽位上，添加到阵容
        this.addCharacterToFormation(card.character, targetSlot.formationPosition.index);
      }
      
      // 重置卡片位置
      card.resetPosition();
      this.currentDraggingCard = null;
    });
  }
  
  /**
   * 将卡片移到最上层
   * @param {CharacterCard} card 要移到最上层的卡片
   */
  bringCardToFront(card) {
    // 从UI元素列表中移除并重新添加到最后（最上层）
    this.removeUIElement(card);
    this.addUIElement(card);
  }
  
  /**
   * 查找指定坐标处的阵容槽位
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @returns {CharacterCard|null} 找到的槽位或null
   */
  findSlotAt(x, y) {
    for (const slot of this.formationSlots) {
      if (slot.isPointInside(x, y)) {
        return slot;
      }
    }
    return null;
  }
  
  /**
   * 添加角色到阵容
   * @param {Object} character 要添加的角色
   * @param {number} [slotIndex] 槽位索引，可选
   */
  addCharacterToFormation(character, slotIndex) {
    // 如果阵容已满，不能再添加
    if (this.selectedCharacters.length >= this.maxFormationSize && slotIndex === undefined) {
      console.log('阵容已满，无法添加更多角色');
      return;
    }
    
    // 如果角色已经在阵容中，先移除
    this.removeCharacterFromFormation(character);
    
    // 如果指定了槽位
    if (slotIndex !== undefined) {
      // 检查槽位是否已被占用
      const existingCharAtSlot = this.selectedCharacters.find(
        c => c.formationPosition === slotIndex
      );
      
      if (existingCharAtSlot) {
        // 如果槽位已经有角色，先移除
        this.removeCharacterFromFormation(existingCharAtSlot);
      }
      
      // 添加角色到指定位置
      character.formationPosition = slotIndex;
      this.selectedCharacters.push(character);
    } else {
      // 找到第一个空槽位
      let emptySlotIndex = 0;
      while (
        emptySlotIndex < this.maxFormationSize && 
        this.selectedCharacters.some(c => c.formationPosition === emptySlotIndex)
      ) {
        emptySlotIndex++;
      }
      
      // 如果找到空槽位，添加角色
      if (emptySlotIndex < this.maxFormationSize) {
        character.formationPosition = emptySlotIndex;
        this.selectedCharacters.push(character);
      }
    }
    
    // 更新UI状态
    this.updateUI();
  }
  
  /**
   * 从阵容中移除角色
   * @param {Object} character 要移除的角色
   */
  removeCharacterFromFormation(character) {
    // 从阵容中移除角色
    const index = this.selectedCharacters.indexOf(character);
    if (index !== -1) {
      this.selectedCharacters.splice(index, 1);
      
      // 更新UI状态
      this.updateUI();
    }
  }
  
  /**
   * 确认布阵，进入战斗
   */
  confirmFormation() {
    // 如果没有选择角色，提示玩家
    if (this.selectedCharacters.length === 0) {
      console.log('请至少选择一名角色上阵');
      return;
    }
    
    // 保存布阵到玩家数据
    game.gameStateManager.setPlayerFormation(this.selectedCharacters);
    
    // 调用布阵完成回调
    if (this.onFormationComplete) {
      this.onFormationComplete(this.selectedCharacters);
    }
  }
  
  /**
   * 更新UI状态
   */
  updateUI() {
    console.log('更新布阵UI，已选角色数量:', this.selectedCharacters.length);
    
    // 清除所有阵位上的角色
    this.formationSlots.forEach(slot => {
      slot.character = null;
    });
    
    // 将已选角色放置到阵位上，从前往后顺序排列
    this.selectedCharacters.forEach((character, index) => {
      if (index < this.formationSlots.length) {
        this.formationSlots[index].character = character;
        console.log(`将角色 ${character.name} 放置在阵位 ${index}`);
      }
    });
    
    // 更新角色卡片状态 - 已在阵容中的角色显示为选中状态
    this.characterCards.forEach(card => {
      if (card.character) {
        const isSelected = this.selectedCharacters.some(c => c.id === card.character.id);
        card.isSelected = isSelected;
      }
    });
    
    // 更新确认按钮状态
    if (this.confirmButton) {
      this.confirmButton.isEnabled = this.selectedCharacters.length > 0;
    }
  }
  
  /**
   * 场景隐藏回调
   */
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制敌方阵容信息
    this.drawEnemyInfo();
    
    // 绘制阵容区域
    this.drawFormationArea();
    
    // 绘制角色列表区域
    this.drawCharacterListArea();
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  /**
   * 绘制标题
   */
  drawTitle() {
    // 绘制顶部标题栏
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.headerHeight);
    
    // 绘制标题文本
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('布阵', this.screenWidth / 2, this.headerHeight / 2);
    
    // 绘制底部按钮区域
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, this.screenHeight - this.footerHeight, this.screenWidth, this.footerHeight);
  }
  
  /**
   * 绘制敌方阵容信息
   */
  drawEnemyInfo() {
    // 绘制敌方信息文本
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'right';
    this.ctx.textBaseline = 'middle';
    
    const enemyCount = this.enemyFormation.length;
    const stageName = this.stageData.name || '未知关卡';
    const stageLevel = this.stageData.level || 1;
    
    this.ctx.fillText(`关卡: ${stageName} Lv.${stageLevel}`, this.screenWidth - 20, this.headerHeight / 2 - 10);
    this.ctx.fillText(`敌方: ${enemyCount}个单位`, this.screenWidth - 20, this.headerHeight / 2 + 10);
  }
  
  /**
   * 绘制阵容区域
   */
  drawFormationArea() {
    // 绘制阵容区域背景
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.5)';
    this.ctx.fillRect(0, this.headerHeight, this.screenWidth, this.formationAreaHeight);
    
    // 绘制提示文本
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('选择角色上阵 (最多6个)', 20, this.headerHeight + 10);
    
    // 绘制已选角色数量
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`已选: ${this.selectedCharacters.length}/${this.maxFormationSize}`, this.screenWidth - 20, this.headerHeight + 10);
  }
  
  /**
   * 绘制角色列表区域
   */
  drawCharacterListArea() {
    // 绘制角色列表区域背景，确保从formationAreaHeight之后开始
    const characterListTop = this.headerHeight + this.formationAreaHeight;
    
    this.ctx.fillStyle = 'rgba(30, 30, 30, 0.5)';
    this.ctx.fillRect(0, characterListTop, this.screenWidth, 
                     this.screenHeight - characterListTop - this.footerHeight);
    
    // 绘制提示文本
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('可用角色', 20, characterListTop + 10);
  }
  
  /**
   * 初始化一键布阵按钮
   */
  initAutoFormationButton() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 20;
    
    this.autoFormationButton = new Button(
      this.ctx,
      margin,
      this.screenHeight - this.footerHeight / 2 - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      '一键布阵',
      null,
      null,
      () => {
        this.autoFormation();
      }
    );
    
    this.addUIElement(this.autoFormationButton);
  }
  
  /**
   * 初始化布阵完成按钮
   */
  initConfirmButton() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 20;
    
    this.confirmButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      this.screenHeight - this.footerHeight / 2 - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      '布阵完成',
      null,
      null,
      () => {
        this.confirmFormation();
      }
    );
    
    this.addUIElement(this.confirmButton);
  }
  
  /**
   * 一键布阵
   */
  autoFormation() {
    // 清空当前已选角色
    this.selectedCharacters = [];
    
    // 按战力排序选择角色
    const sortedCharacters = [...this.availableCharacters].sort((a, b) => b.combatPower - a.combatPower);
    
    // 最多添加到最大阵容数
    const charactersToAdd = sortedCharacters.slice(0, this.maxFormationSize);
    
    // 将角色添加到布阵中
    charactersToAdd.forEach((character, index) => {
      character.formationPosition = index;
      this.selectedCharacters.push(character);
    });
    
    // 更新UI状态
    this.updateUI();
  }
}

export default BattleFormationScene; 