/**
 * 战斗管理器
 * 负责协调战斗流程、场景切换以及战斗结果处理
 */
import BattleEngine from './models/BattleEngine';
import game from '../../game';
import BattleFormationScene from './scenes/BattleFormationScene';
import BattleScene from './scenes/BattleScene';
import BattleResultScene from './scenes/BattleResultScene';

class BattleManager {
  constructor() {
    // 战斗相关场景
    this.battleFormationScene = null;
    this.battleScene = null;
    this.battleResultScene = null;
    
    // 战斗引擎
    this.battleEngine = null;
    
    // 战斗状态
    this.isBattleActive = false;
    
    // 当前战斗参数
    this.currentBattleParams = null;
    
    // 初始化战斗管理器
    this.init();
  }
  
  /**
   * 初始化战斗管理器
   */
  init() {
    try {
      console.log('初始化战斗管理器');
      
      // 创建战斗引擎
      this.battleEngine = new BattleEngine();
      
      // 检查场景管理器是否初始化
      if (!game.sceneManager) {
        console.warn('场景管理器尚未初始化，将在场景管理器初始化后自动注册战斗场景');
        // 延迟初始化场景，等待场景管理器初始化
        setTimeout(() => {
          if (game.sceneManager) {
            this.initBattleScenes();
          } else {
            console.error('场景管理器仍未初始化，无法注册战斗场景');
          }
        }, 1000);
      } else {
        // 场景管理器已经初始化，可以直接初始化战斗场景
        this.initBattleScenes();
      }
      
    } catch (error) {
      console.error('初始化战斗管理器失败:', error);
    }
  }
  
  /**
   * 初始化战斗相关场景
   */
  initBattleScenes() {
    try {
      const sceneManager = game.sceneManager;
      if (!sceneManager) {
        console.error('场景管理器未初始化，无法注册战斗场景');
        return;
      }
      
      const ctx = game.ctx;
      const width = game.gameWidth || game.screenWidth;
      const height = game.gameHeight || game.screenHeight;
      
      // 创建战斗阵型场景
      this.battleFormationScene = new BattleFormationScene(ctx, width, height, sceneManager);
      sceneManager.registerScene('battleFormation', this.battleFormationScene);
      
      // 创建战斗场景
      this.battleScene = new BattleScene(ctx, width, height, sceneManager);
      sceneManager.registerScene('battle', this.battleScene);
      
      // 创建战斗结果场景
      this.battleResultScene = new BattleResultScene(ctx, width, height, sceneManager);
      sceneManager.registerScene('battleResult', this.battleResultScene);
      
      console.log('战斗场景初始化完成');
    } catch (error) {
      console.error('初始化战斗场景失败:', error);
    }
  }
  
  /**
   * 开始战斗
   * @param {Object} params 战斗参数
   * @param {Array} params.playerUnits 玩家单位列表
   * @param {Array} params.enemyUnits 敌人单位列表
   * @param {string} params.battleType 战斗类型 ('normal', 'boss', 'arena', 等)
   * @param {Object} params.battleConfig 战斗配置参数
   * @param {Function} params.onComplete 战斗完成回调
   */
  startBattle(params) {
    try {
      console.log('开始战斗:', params);
      
      // 保存当前战斗参数
      this.currentBattleParams = params;
      
      // 设置战斗状态为活跃
      this.isBattleActive = true;
      
      // 准备玩家单位
      const playerUnits = this.preparePlayerUnits(params.playerUnits);
      
      // 准备敌人单位
      const enemyUnits = this.prepareEnemyUnits(params.enemyUnits);
      
      // 战斗类型和配置
      const battleType = params.battleType || 'normal';
      const battleConfig = params.battleConfig || {};
      
      // 跳转到战斗场景
      game.sceneManager.showScene('battle', {
        playerUnits: playerUnits,
        enemyUnits: enemyUnits,
        config: battleConfig,
        onComplete: (result) => {
          this.handleBattleComplete(result);
        }
      });
    } catch (error) {
      console.error('开始战斗失败:', error);
      this.handleBattleError(error);
    }
  }
  
  /**
   * 处理战斗完成
   * @param {Object} result 战斗结果
   */
  handleBattleComplete(result) {
    // 设置战斗状态为非活跃
    this.isBattleActive = false;
    
    console.log('战斗完成:', result);
    
    // 调用完成回调
    if (this.currentBattleParams && this.currentBattleParams.onComplete) {
      this.currentBattleParams.onComplete(result);
    }
  }
  
  /**
   * 准备玩家单位
   * @param {Array} unitData 玩家单位数据
   * @returns {Array} 处理后的战斗单位
   */
  preparePlayerUnits(unitData) {
    console.log('准备玩家单位数据:', unitData);
    return (unitData || []).map(unit => {
      // 创建完整的单位数据副本
      const battleUnit = {
        ...unit,
        // 确保保留formationPosition属性
        formationPosition: unit.formationPosition !== undefined ? unit.formationPosition : 0
      };
      
      // 处理属性数据 - 确保血量从attributes正确传递
      if (unit.attributes) {
        battleUnit.attributes = { ...unit.attributes };
        
        // 以下是HP初始化的改进逻辑
        // 第1步：确定最大生命值maxHp
        if (unit.maxHp !== undefined) {
          // 如果unit直接有maxHp属性，优先使用
          battleUnit.maxHp = unit.maxHp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用直接指定的maxHp: ${battleUnit.maxHp}`);
        } else if (unit.attributes.hp !== undefined) {
          // 其次使用attributes.hp
          battleUnit.maxHp = unit.attributes.hp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用attributes.hp作为maxHp: ${battleUnit.maxHp}`);
        } else if (unit.attributes.maxHp !== undefined) {
          // 再次使用attributes.maxHp
          battleUnit.maxHp = unit.attributes.maxHp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用attributes.maxHp: ${battleUnit.maxHp}`);
        } else {
          // 最后使用默认值
          battleUnit.maxHp = 100 + (unit.level || 1) * 10;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用默认计算的maxHp: ${battleUnit.maxHp}`);
        }
        
        // 第2步：确定当前生命值hp
        if (unit.hp !== undefined) {
          // 如果unit直接有hp属性，优先使用
          battleUnit.hp = unit.hp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用直接指定的hp: ${battleUnit.hp}`);
        } else if (unit.attributes.currentHp !== undefined) {
          // 其次使用attributes.currentHp
          battleUnit.hp = unit.attributes.currentHp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用attributes.currentHp: ${battleUnit.hp}`);
        } else {
          // 最后使用maxHp
          battleUnit.hp = battleUnit.maxHp;
          console.log(`玩家单位 ${unit.name || '未命名'} 使用maxHp作为初始hp: ${battleUnit.hp}`);
        }
        
        // 确保hp不超过maxHp
        battleUnit.hp = Math.min(battleUnit.hp, battleUnit.maxHp);
        
        // 复制其他战斗属性
        if (unit.attributes.attack) battleUnit.attack = unit.attributes.attack;
        if (unit.attributes.defense) battleUnit.defense = unit.attributes.defense;
        if (unit.attributes.speed) battleUnit.speed = unit.attributes.speed;
        if (unit.attributes.critRate) battleUnit.critRate = unit.attributes.critRate;
        if (unit.attributes.critDamage) battleUnit.critDamage = unit.attributes.critDamage;
      } else {
        // 没有attributes对象，使用直接属性或默认值
        battleUnit.maxHp = unit.maxHp || 100 + (unit.level || 1) * 10;
        battleUnit.hp = (unit.hp !== undefined) ? unit.hp : battleUnit.maxHp;
        console.log(`玩家单位 ${unit.name || '未命名'} 没有attributes对象，直接设置hp: ${battleUnit.hp}/${battleUnit.maxHp}`);
      }
      
      // 保留技能数据
      if (unit.skills && Array.isArray(unit.skills)) {
        battleUnit.skills = [...unit.skills];
      }
      
      // 添加isAlive函数
      battleUnit.isAlive = function() {
        return this.hp > 0;
      };
      
      console.log(`玩家单位 ${battleUnit.name || '未命名'} 准备完成，血量: ${
        battleUnit.hp
      }/${
        battleUnit.maxHp
      }, 攻击: ${battleUnit.attack || '未设置'}, 防御: ${battleUnit.defense || '未设置'}, 速度: ${battleUnit.speed || '未设置'}`);
      
      return battleUnit;
    });
  }
  
  /**
   * 准备敌人单位
   * @param {Array} unitData 敌人单位数据
   * @returns {Array} 处理后的战斗单位
   */
  prepareEnemyUnits(unitData) {
    console.log('准备敌人单位数据:', unitData);
    return (unitData || []).map((unit, index) => {
      // 创建完整的单位数据副本
      const battleUnit = {
        ...unit,
        // 确保保留formationPosition属性
        formationPosition: unit.formationPosition !== undefined ? unit.formationPosition : index
      };
      
      // 处理属性数据 - 确保血量从attributes正确传递
      if (unit.attributes) {
        battleUnit.attributes = { ...unit.attributes };
        
        // 以下是HP初始化的改进逻辑
        // 第1步：确定最大生命值maxHp
        if (unit.maxHp !== undefined) {
          // 如果unit直接有maxHp属性，优先使用
          battleUnit.maxHp = unit.maxHp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用直接指定的maxHp: ${battleUnit.maxHp}`);
        } else if (unit.attributes.hp !== undefined) {
          // 其次使用attributes.hp
          battleUnit.maxHp = unit.attributes.hp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用attributes.hp作为maxHp: ${battleUnit.maxHp}`);
        } else if (unit.attributes.maxHp !== undefined) {
          // 再次使用attributes.maxHp
          battleUnit.maxHp = unit.attributes.maxHp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用attributes.maxHp: ${battleUnit.maxHp}`);
        } else {
          // 最后使用默认值
          battleUnit.maxHp = 100 + (unit.level || 1) * 10;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用默认计算的maxHp: ${battleUnit.maxHp}`);
        }
        
        // 第2步：确定当前生命值hp
        if (unit.hp !== undefined) {
          // 如果unit直接有hp属性，优先使用
          battleUnit.hp = unit.hp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用直接指定的hp: ${battleUnit.hp}`);
        } else if (unit.attributes.currentHp !== undefined) {
          // 其次使用attributes.currentHp
          battleUnit.hp = unit.attributes.currentHp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用attributes.currentHp: ${battleUnit.hp}`);
        } else {
          // 最后使用maxHp
          battleUnit.hp = battleUnit.maxHp;
          console.log(`敌人单位 ${unit.name || '未命名'} 使用maxHp作为初始hp: ${battleUnit.hp}`);
        }
        
        // 确保hp不超过maxHp
        battleUnit.hp = Math.min(battleUnit.hp, battleUnit.maxHp);
        
        // 复制其他战斗属性
        if (unit.attributes.attack) battleUnit.attack = unit.attributes.attack;
        if (unit.attributes.defense) battleUnit.defense = unit.attributes.defense;
        if (unit.attributes.speed) battleUnit.speed = unit.attributes.speed;
        if (unit.attributes.critRate) battleUnit.critRate = unit.attributes.critRate;
        if (unit.attributes.critDamage) battleUnit.critDamage = unit.attributes.critDamage;
      } else {
        // 没有attributes对象，使用直接属性或默认值
        battleUnit.maxHp = unit.maxHp || 100 + (unit.level || 1) * 10;
        battleUnit.hp = (unit.hp !== undefined) ? unit.hp : battleUnit.maxHp;
        console.log(`敌人单位 ${unit.name || '未命名'} 没有attributes对象，直接设置hp: ${battleUnit.hp}/${battleUnit.maxHp}`);
      }
      
      // 保留技能数据
      if (unit.skills && Array.isArray(unit.skills)) {
        battleUnit.skills = [...unit.skills];
      }
      
      // 添加isAlive函数
      battleUnit.isAlive = function() {
        return this.hp > 0;
      };
      
      console.log(`敌人单位 ${battleUnit.name || '未命名'} 准备完成，血量: ${
        battleUnit.hp
      }/${
        battleUnit.maxHp
      }, 攻击: ${battleUnit.attack || '未设置'}, 防御: ${battleUnit.defense || '未设置'}, 速度: ${battleUnit.speed || '未设置'}`);
      
      return battleUnit;
    });
  }
  
  /**
   * 处理战斗错误
   * @param {Error} error 错误对象
   */
  handleBattleError(error) {
    console.error('战斗系统错误:', error);
    
    // 设置战斗状态为非活跃
    this.isBattleActive = false;
    
    // 调用完成回调，传递错误状态
    if (this.currentBattleParams && this.currentBattleParams.onComplete) {
      this.currentBattleParams.onComplete({ 
        error: true,
        message: error.message || '战斗过程中发生错误'
      });
    }
  }
  
  /**
   * 检查战斗是否激活
   * @returns {boolean} 战斗是否激活
   */
  isBattleInProgress() {
    return this.isBattleActive;
  }
}

export default BattleManager; 