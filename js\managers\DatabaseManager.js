/**
 * 数据库管理器
 * 负责与腾讯云数据库的交互
 */

class DatabaseManager {
  constructor() {
    // 初始化数据库连接
    this.db = null;
    this.initialized = false;

    // 延迟初始化数据库
    this.initDatabase();

    // 表名常量
    this.TABLES = {
      PLAYERS: 'players',
      CHARACTERS: 'characters',
      EQUIPMENTS: 'equipments',
      ITEMS: 'items',
      SKILLS: 'skills',
      SWORD_HEARTS: 'sword_hearts',
      SWORD_BONES: 'sword_bones',
      DONGFU_SYSTEM: 'dongfu_system',
      RECHARGE_RECORDS: 'recharge_records',
      ARENA_DATA: 'arena_data',
      IDLE_PROGRESS: 'idle_progress',
      SKILL_CULTIVATION: 'skill_cultivation'
    };
  }

  /**
   * 初始化数据库连接
   */
  async initDatabase() {
    try {
      // 检查是否在微信小程序环境中
      if (typeof wx === 'undefined') {
        console.warn('不在微信小程序环境中，跳过云数据库初始化');
        return;
      }

      // 检查云开发是否可用
      if (!wx.cloud) {
        console.warn('云开发不可用，跳过云数据库初始化');
        return;
      }

      // 尝试初始化云开发
      try {
        wx.cloud.init({
          env: 'cloud1-9gzbxxbff827656f', // 使用游戏的云开发环境ID
          traceUser: true
        });

        // 初始化数据库
        this.db = wx.cloud.database();
        this.initialized = true;
        console.log('云数据库初始化成功');
      } catch (initError) {
        console.warn('云数据库初始化失败，将使用本地存储:', initError);
        this.initialized = false;
      }
    } catch (error) {
      console.warn('数据库初始化过程中出现错误:', error);
      this.initialized = false;
    }
  }

  /**
   * 检查数据库是否已初始化
   */
  isInitialized() {
    return this.initialized && this.db !== null;
  }

  /**
   * 获取当前用户的openid
   */
  getCurrentOpenId() {
    try {
      // 检查是否在微信小程序环境中
      if (typeof wx === 'undefined') {
        console.warn('不在微信小程序环境中，无法获取openid');
        return null;
      }

      // 尝试从本地存储获取openid
      const openid = wx.getStorageSync('openid');
      if (openid) {
        return openid;
      }

      // 如果本地存储没有，尝试从全局变量获取
      if (typeof window !== 'undefined' && window.userOpenId) {
        return window.userOpenId;
      }

      // 尝试从游戏实例的登录管理器获取
      if (typeof AppContext !== 'undefined' &&
          AppContext &&
          AppContext.game &&
          AppContext.game.loginManager &&
          AppContext.game.loginManager.userOpenId) {
        return AppContext.game.loginManager.userOpenId;
      }

      // 如果都没有，返回null
      console.warn('未找到用户openid，可能需要重新登录');
      return null;
    } catch (error) {
      console.error('获取openid时出错:', error);
      return null;
    }
  }

  // ==================== 玩家数据相关 ====================

  /**
   * 获取玩家数据
   */
  async getPlayerData() {
    try {
      // 检查数据库是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过获取玩家数据');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过获取玩家数据');
        return null;
      }

      const res = await this.db.collection(this.TABLES.PLAYERS)
        .where({ _openid: openid })
        .get();

      if (res.data.length > 0) {
        return res.data[0];
      } else {
        // 如果没有数据，创建默认玩家数据
        return await this.createDefaultPlayerData(openid);
      }
    } catch (error) {
      console.error('获取玩家数据失败:', error);
      return null;
    }
  }

  /**
   * 创建默认玩家数据
   */
  async createDefaultPlayerData(openid) {
    try {
      const defaultData = {
        _openid: openid,
        nickname: '修仙者',
        avatar_url: '',
        dongfu_level: 1,
        xianyu: 1000,
        lingshi: 1000,
        sword_intent: 0,
        lianlidian: 100,
        vip_level: 0,
        total_recharge: 0,
        last_vip_reward_time: null,
        formation: [],
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.PLAYERS).add({
        data: defaultData
      });

      return { ...defaultData, _id: res._id };
    } catch (error) {
      console.error('创建默认玩家数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新玩家数据
   */
  async updatePlayerData(updateData) {
    try {
      // 检查数据库是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过更新玩家数据');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过更新玩家数据');
        return null;
      }

      // 添加更新时间
      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.PLAYERS)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新玩家数据失败:', error);
      return null;
    }
  }

  /**
   * 更新玩家资源
   */
  async updatePlayerResources(resources) {
    try {
      const updateData = {};

      if (resources.xianyu !== undefined) updateData.xianyu = resources.xianyu;
      if (resources.lingshi !== undefined) updateData.lingshi = resources.lingshi;
      if (resources.sword_intent !== undefined) updateData.sword_intent = resources.sword_intent;
      if (resources.lianlidian !== undefined) updateData.lianlidian = resources.lianlidian;

      return await this.updatePlayerData(updateData);
    } catch (error) {
      console.error('更新玩家资源失败:', error);
      throw error;
    }
  }

  // ==================== 角色数据相关 ====================

  /**
   * 获取玩家所有角色
   */
  async getPlayerCharacters() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取角色数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加角色
   */
  async addCharacter(characterData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...characterData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.CHARACTERS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加角色失败:', error);
      throw error;
    }
  }

  /**
   * 更新角色数据
   */
  async updateCharacter(characterId, updateData) {
    try {
      // 检查数据库是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过更新角色数据');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过更新角色数据');
        return null;
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({
          _openid: openid,
          character_id: characterId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新角色数据失败:', error);
      return null;
    }
  }

  /**
   * 删除角色
   */
  async deleteCharacter(characterId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({
          _openid: openid,
          character_id: characterId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除角色失败:', error);
      throw error;
    }
  }

  // ==================== 装备数据相关 ====================

  /**
   * 获取玩家所有装备
   */
  async getPlayerEquipments() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取装备数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加装备
   */
  async addEquipment(equipmentData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...equipmentData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.EQUIPMENTS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加装备失败:', error);
      throw error;
    }
  }

  /**
   * 更新装备数据
   */
  async updateEquipment(equipmentId, updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({
          _openid: openid,
          equipment_id: equipmentId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新装备数据失败:', error);
      throw error;
    }
  }

  /**
   * 删除装备
   */
  async deleteEquipment(equipmentId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({
          _openid: openid,
          equipment_id: equipmentId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除装备失败:', error);
      throw error;
    }
  }

  // ==================== 物品数据相关 ====================

  /**
   * 获取玩家所有物品
   */
  async getPlayerItems() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取物品数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加物品
   */
  async addItem(itemData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      // 检查是否已存在相同物品，如果存在则增加数量
      const existingRes = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemData.item_id
        })
        .get();

      if (existingRes.data.length > 0) {
        // 物品已存在，增加数量
        const existingItem = existingRes.data[0];
        const newCount = existingItem.count + (itemData.count || 1);

        await this.db.collection(this.TABLES.ITEMS)
          .doc(existingItem._id)
          .update({
            data: {
              count: newCount,
              updated_at: new Date()
            }
          });

        return { ...existingItem, count: newCount };
      } else {
        // 新物品，直接添加
        const data = {
          ...itemData,
          _openid: openid,
          created_at: new Date(),
          updated_at: new Date()
        };

        const res = await this.db.collection(this.TABLES.ITEMS).add({
          data: data
        });

        return { ...data, _id: res._id };
      }
    } catch (error) {
      console.error('添加物品失败:', error);
      throw error;
    }
  }

  /**
   * 更新物品数量
   */
  async updateItemCount(itemId, count) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      if (count <= 0) {
        // 数量为0或负数，删除物品
        return await this.deleteItem(itemId);
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemId
        })
        .update({
          data: {
            count: count,
            updated_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('更新物品数量失败:', error);
      throw error;
    }
  }

  /**
   * 删除物品
   */
  async deleteItem(itemId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除物品失败:', error);
      throw error;
    }
  }

  // ==================== 技能数据相关 ====================

  /**
   * 获取玩家所有技能
   */
  async getPlayerSkills() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SKILLS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取技能数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加技能
   */
  async addSkill(skillData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...skillData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.SKILLS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加技能失败:', error);
      throw error;
    }
  }

  /**
   * 更新技能数据
   */
  async updateSkill(skillId, updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.SKILLS)
        .where({
          _openid: openid,
          skill_id: skillId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新技能数据失败:', error);
      throw error;
    }
  }

  // ==================== 剑心数据相关 ====================

  /**
   * 获取玩家所有剑心
   */
  async getPlayerSwordHearts() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SWORD_HEARTS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取剑心数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加或更新剑心
   */
  async savePlayerSwordHearts(swordHeartsData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      // 先删除现有的剑心数据
      await this.db.collection(this.TABLES.SWORD_HEARTS)
        .where({ _openid: openid })
        .remove();

      // 批量添加新的剑心数据
      const promises = swordHeartsData.map(swordHeart => {
        const data = {
          ...swordHeart,
          _openid: openid,
          created_at: new Date(),
          updated_at: new Date()
        };

        return this.db.collection(this.TABLES.SWORD_HEARTS).add({
          data: data
        });
      });

      const results = await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('保存剑心数据失败:', error);
      throw error;
    }
  }

  // ==================== 剑骨数据相关 ====================

  /**
   * 获取玩家剑骨数据
   */
  async getPlayerSwordBone() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SWORD_BONES)
        .where({ _openid: openid })
        .get();

      if (res.data.length > 0) {
        return res.data[0];
      } else {
        // 创建默认剑骨数据
        return await this.createDefaultSwordBone(openid);
      }
    } catch (error) {
      console.error('获取剑骨数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建默认剑骨数据
   */
  async createDefaultSwordBone(openid) {
    try {
      const defaultData = {
        _openid: openid,
        level: 0,
        rank: 0,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.SWORD_BONES).add({
        data: defaultData
      });

      return { ...defaultData, _id: res._id };
    } catch (error) {
      console.error('创建默认剑骨数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新剑骨数据
   */
  async updateSwordBone(updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.SWORD_BONES)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新剑骨数据失败:', error);
      throw error;
    }
  }

  // ==================== 洞府系统数据相关 ====================

  /**
   * 获取玩家洞府系统数据
   */
  async getPlayerDongfuSystem() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM)
        .where({ _openid: openid })
        .get();

      if (res.data.length > 0) {
        return res.data[0];
      } else {
        // 创建默认洞府系统数据
        return await this.createDefaultDongfuSystem(openid);
      }
    } catch (error) {
      console.error('获取洞府系统数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建默认洞府系统数据
   */
  async createDefaultDongfuSystem(openid) {
    try {
      const defaultData = {
        _openid: openid,
        current_lingqi: 0,
        last_collect_time: null,
        cultivation_start_time: null,
        cultivation_character_id: null,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM).add({
        data: defaultData
      });

      return { ...defaultData, _id: res._id };
    } catch (error) {
      console.error('创建默认洞府系统数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新洞府系统数据
   */
  async updateDongfuSystem(updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新洞府系统数据失败:', error);
      throw error;
    }
  }

  // ==================== 充值记录相关 ====================

  /**
   * 添加充值记录
   */
  async addRechargeRecord(rechargeData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...rechargeData,
        _openid: openid,
        created_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加充值记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取玩家充值记录
   */
  async getPlayerRechargeRecords() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS)
        .where({ _openid: openid })
        .orderBy('created_at', 'desc')
        .get();

      return res.data;
    } catch (error) {
      console.error('获取充值记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新充值记录状态
   */
  async updateRechargeRecordStatus(orderId, status) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS)
        .where({
          _openid: openid,
          order_id: orderId
        })
        .update({
          data: {
            status: status,
            updated_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('更新充值记录状态失败:', error);
      throw error;
    }
  }
}

export default DatabaseManager;
