/**
 * 竞技场场景
 * 显示玩家排名、可挑战对手和每日奖励
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import game from '../../game';

class ArenaScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引
    
    // 可挑战的对手列表
    this.opponents = [];
    
    // 初始化UI
    this.initUI();
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建返回按钮
    this.createBackButton();
    
    // 创建刷新按钮
    this.createRefreshButton();
    
    // 创建领奖按钮
    this.createClaimRewardButton();
    
    // 加载对手列表
    this.loadOpponents();
    
    // 创建对手按钮
    this.createOpponentButtons();
  }
  
  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  // 创建刷新按钮
  createRefreshButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.refreshButton = new Button(
      this.ctx,
      this.screenWidth - buttonWidth - margin,
      margin,
      buttonWidth,
      buttonHeight,
      '刷新',
      null,
      '#4299e1',
      () => {
        this.loadOpponents();
        this.createOpponentButtons();
      }
    );
    
    this.addUIElement(this.refreshButton);
  }
  
  // 创建领奖按钮
  createClaimRewardButton() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const margin = 10;
    
    this.claimRewardButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - buttonHeight - margin - 60, // 底部导航栏上方
      buttonWidth,
      buttonHeight,
      '领取奖励',
      null,
      '#f56565',
      () => {
        this.claimDailyReward();
      }
    );
    
    this.addUIElement(this.claimRewardButton);
  }
  
  // 加载对手列表
  loadOpponents() {
    if (game.gameStateManager && game.gameStateManager.arenaManager) {
      this.opponents = game.gameStateManager.arenaManager.getOpponents();
    } else {
      this.opponents = [];
    }
  }
  
  // 创建对手按钮
  createOpponentButtons() {
    const headerHeight = 80;
    const buttonWidth = this.screenWidth - 40;
    const buttonHeight = 80;
    const margin = 20;
    
    this.opponentButtons = [];
    
    this.opponents.forEach((opponent, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        headerHeight + 100 + index * (buttonHeight + margin),
        buttonWidth,
        buttonHeight,
        `${opponent.rank}. ${opponent.name}`,
        null,
        opponent.rank < this.getPlayerRank() ? '#4299e1' : '#718096',
        () => {
          this.showChallengeDialog(opponent);
        }
      );
      
      this.opponentButtons.push(button);
      this.addUIElement(button);
    });
  }
  
  // 显示挑战对话框
  showChallengeDialog(opponent) {
    // 检查剩余挑战次数
    const remainingChallenges = this.getRemainingChallenges();
    if (remainingChallenges <= 0) {
      this.showMessage('今日挑战次数已用完');
      return;
    }
    
    const dialogButtons = [
      {
        text: '挑战',
        normalImg: null,
        pressedImg: null,
        onClick: () => {
          this.challengeOpponent(opponent.rank);
        },
        closeDialog: true
      },
      {
        text: '取消',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];
    
    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '确认挑战',
      `是否挑战排名第${opponent.rank}的${opponent.name}？\n对手境界：${opponent.cultivation}\n对手战力：${opponent.power}\n今日剩余挑战次数：${remainingChallenges}`,
      dialogButtons
    );
    
    this.addUIElement(dialog);
    dialog.show();
  }
  
  // 挑战对手
  challengeOpponent(opponentRank) {
    if (!game.gameStateManager || !game.gameStateManager.arenaManager) {
      this.showMessage('竞技场系统未初始化');
      return;
    }
    
    // 执行挑战
    const result = game.gameStateManager.arenaManager.challengeOpponent(opponentRank);
    
    if (!result.success) {
      this.showMessage(result.message);
      return;
    }
    
    // 显示战斗结果
    if (result.playerWin) {
      if (result.oldRank !== result.newRank) {
        this.showMessage(`挑战成功！\n你击败了${result.opponent.name}，排名提升至第${result.newRank}名！`);
      } else {
        this.showMessage(`挑战成功！\n你击败了${result.opponent.name}，但排名未变动。`);
      }
    } else {
      this.showMessage(`挑战失败！\n你被${result.opponent.name}击败了，排名未变动。`);
    }
    
    // 刷新对手列表
    this.loadOpponents();
    this.createOpponentButtons();
  }
  
  // 领取每日奖励
  claimDailyReward() {
    if (!game.gameStateManager || !game.gameStateManager.arenaManager) {
      this.showMessage('竞技场系统未初始化');
      return;
    }
    
    // 领取奖励
    const result = game.gameStateManager.arenaManager.claimDailyReward();
    
    if (!result.success) {
      this.showMessage(result.message);
      return;
    }
    
    // 显示奖励结果
    this.showMessage(`领取成功！\n获得${result.reward.xianyu}仙玉\n奖励类型：${result.reward.description}`);
  }
  
  // 获取玩家排名
  getPlayerRank() {
    if (!game.gameStateManager || !game.gameStateManager.arenaManager) {
      return 1500;
    }
    
    return game.gameStateManager.arenaManager.getPlayerRank();
  }
  
  // 获取玩家历史最高排名
  getPlayerHighestRank() {
    if (!game.gameStateManager || !game.gameStateManager.arenaManager) {
      return 1500;
    }
    
    return game.gameStateManager.arenaManager.getPlayerHighestRank();
  }
  
  // 获取剩余挑战次数
  getRemainingChallenges() {
    if (!game.gameStateManager || !game.gameStateManager.arenaManager) {
      return 0;
    }
    
    return game.gameStateManager.arenaManager.getRemainingChallenges();
  }
  
  // 显示消息对话框
  showMessage(message) {
    const dialogButtons = [
      {
        text: '确定',
        normalImg: null,
        pressedImg: null,
        onClick: null,
        closeDialog: true
      }
    ];
    
    const dialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '提示',
      message,
      dialogButtons
    );
    
    this.addUIElement(dialog);
    dialog.show();
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();
    
    // 初始化UI
    this.initUI();
    
    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制玩家排名信息
    this.drawPlayerRankInfo();
    
    // 绘制对手信息
    this.drawOpponentsInfo();
  }
  
  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a202c');
    gradient.addColorStop(1, '#2d3748');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  // 绘制标题
  drawTitle() {
    const headerHeight = 80;
    
    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('修仙竞技场', this.screenWidth / 2, headerHeight / 2);
  }
  
  // 绘制玩家排名信息
  drawPlayerRankInfo() {
    const headerHeight = 80;
    const margin = 20;
    
    // 绘制排名信息背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, headerHeight + margin, this.screenWidth - margin * 2, 60);
    
    // 获取玩家排名
    const playerRank = this.getPlayerRank();
    const playerHighestRank = this.getPlayerHighestRank();
    const remainingChallenges = this.getRemainingChallenges();
    
    // 绘制排名信息
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(`当前排名: 第${playerRank}名`, margin * 2, headerHeight + margin + 20);
    this.ctx.fillText(`历史最高: 第${playerHighestRank}名`, margin * 2, headerHeight + margin + 40);
    
    // 绘制挑战次数
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`今日剩余挑战: ${remainingChallenges}次`, this.screenWidth - margin * 2, headerHeight + margin + 30);
  }
  
  // 绘制对手信息
  drawOpponentsInfo() {
    const headerHeight = 80;
    const margin = 20;
    
    // 绘制对手列表标题
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('可挑战对手', this.screenWidth / 2, headerHeight + margin * 2 + 60);
  }
}

export default ArenaScene;
