/**
 * 资源加载器
 * 用于加载图片等资源
 */
class ResourceLoader {
  constructor() {
    // 已加载的资源缓存
    this.resources = {};
    
    // 资源加载错误记录
    this.loadErrors = {};
    
    // 默认资源
    this.defaultResources = {};
    
    // 是否已初始化
    this.initialized = false;
  }
  
  // 初始化默认资源
  initialize() {
    if (this.initialized) {
      return;
    }
    
    try {
      // 创建默认图片资源（1x1透明像素）
      const canvas = wx.createCanvas();
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = 'rgba(0, 0, 0, 0)';
      ctx.fillRect(0, 0, 1, 1);
      
      this.defaultResources.emptyImage = canvas;
      
      // 创建默认头像资源
      const avatarCanvas = wx.createCanvas();
      avatarCanvas.width = 60;
      avatarCanvas.height = 60;
      const avatarCtx = avatarCanvas.getContext('2d');
      
      // 绘制默认头像
      avatarCtx.fillStyle = '#cccccc';
      avatarCtx.beginPath();
      avatarCtx.arc(30, 30, 30, 0, Math.PI * 2);
      avatarCtx.fill();
      
      // 绘制人形图标
      avatarCtx.fillStyle = '#ffffff';
      // 头部
      avatarCtx.beginPath();
      avatarCtx.arc(30, 20, 10, 0, Math.PI * 2);
      avatarCtx.fill();
      
      // 身体
      avatarCtx.beginPath();
      avatarCtx.moveTo(30, 30);
      avatarCtx.lineTo(20, 50);
      avatarCtx.lineTo(40, 50);
      avatarCtx.closePath();
      avatarCtx.fill();
      
      this.defaultResources.defaultAvatar = avatarCanvas;
      
      this.initialized = true;
    } catch (error) {
      console.error('初始化资源加载器默认资源失败:', error);
    }
  }
  
  // 加载单个资源
  loadResource(key, url) {
    // 确保初始化
    this.initialize();
    
    return new Promise((resolve, reject) => {
      // 检查资源类型
      const fileExt = url.split('.').pop().toLowerCase();
      
      // 根据资源类型进行不同的加载
      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        // 加载图片
        try {
          const image = wx.createImage();
          
          image.onload = () => {
            this.resources[key] = image;
            resolve(image);
          };
          
          image.onerror = (e) => {
            console.error(`加载图片失败: ${url}`, e);
            this.loadErrors[key] = {
              url: url,
              error: e,
              time: new Date()
            };
            
            // 使用默认资源作为替代
            this.resources[key] = this.defaultResources.emptyImage;
            
            // 虽然加载失败但仍然标记为已解决，以继续游戏流程
            resolve(this.defaultResources.emptyImage);
          };
          
          // 设置一个超时，防止永远不返回
          setTimeout(() => {
            if (!this.resources[key]) {
              console.warn(`加载图片超时: ${url}`);
              this.loadErrors[key] = {
                url: url,
                error: '加载超时',
                time: new Date()
              };
              this.resources[key] = this.defaultResources.emptyImage;
              resolve(this.defaultResources.emptyImage);
            }
          }, 5000);
          
          image.src = url;
        } catch (error) {
          console.error(`创建图片对象失败: ${url}`, error);
          this.loadErrors[key] = {
            url: url,
            error: error,
            time: new Date()
          };
          this.resources[key] = this.defaultResources.emptyImage;
          resolve(this.defaultResources.emptyImage);
        }
      } else {
        // 其他类型资源暂不支持
        console.error(`不支持的资源类型: ${fileExt}`);
        this.loadErrors[key] = {
          url: url,
          error: `不支持的资源类型: ${fileExt}`,
          time: new Date()
        };
        
        // 创建一个空对象作为占位符
        this.resources[key] = null;
        resolve(null);
      }
    });
  }
  
  // 加载多个资源
  loadResources(resourceMap, onProgress, onComplete) {
    // 确保初始化
    this.initialize();
    
    const keys = Object.keys(resourceMap);
    const totalCount = keys.length;
    let loadedCount = 0;
    let errorCount = 0;
    
    // 如果没有资源需要加载，直接完成
    if (totalCount === 0) {
      if (onComplete) {
        onComplete(this.resources, 0);
      }
      return;
    }
    
    try {
      // 循环加载每个资源
      keys.forEach(key => {
        const url = resourceMap[key];
        
        this.loadResource(key, url)
          .then(() => {
            loadedCount++;
            
            // 检查是否加载失败
            if (this.isResourceError(key)) {
              errorCount++;
              console.warn(`资源 ${key} (${url}) 加载失败，将使用默认资源替代`);
            }
            
            // 更新进度
            if (onProgress) {
              onProgress(loadedCount / totalCount, key);
            }
            
            // 检查是否全部加载完成
            if (loadedCount === totalCount && onComplete) {
              // 如果有错误，输出汇总信息
              if (errorCount > 0) {
                console.warn(`资源加载完成，但有 ${errorCount} 个资源加载失败，已使用默认资源替代`);
              }
              
              onComplete(this.resources, errorCount);
            }
          })
          .catch(error => {
            console.error(`加载资源出错: ${key} (${url})`, error);
            loadedCount++;
            errorCount++;
            
            // 记录错误信息
            this.loadErrors[key] = {
              url: url,
              error: error.toString(),
              time: new Date()
            };
            
            // 使用默认资源作为替代
            this.resources[key] = this.defaultResources.emptyImage;
            
            // 更新进度
            if (onProgress) {
              onProgress(loadedCount / totalCount, key);
            }
            
            // 检查是否全部加载完成
            if (loadedCount === totalCount && onComplete) {
              console.warn(`资源加载完成，但有 ${errorCount} 个资源加载失败，已使用默认资源替代`);
              onComplete(this.resources, errorCount);
            }
          });
      });
    } catch (error) {
      console.error('资源加载过程中发生未捕获的错误:', error);
      // 即使出错，也尝试调用完成回调
      if (onComplete) {
        onComplete(this.resources, totalCount);
      }
    }
  }
  
  // 获取已加载的资源
  getResource(key) {
    if (this.resources[key]) {
      return this.resources[key];
    }
    
    // 如果没有找到资源，返回默认资源
    console.warn(`资源未找到: ${key}，使用默认资源替代`);
    return this.defaultResources.emptyImage;
  }
  
  // 获取头像类型资源
  getAvatarResource(key) {
    if (this.resources[key]) {
      return this.resources[key];
    }
    
    // 如果没有找到资源，返回默认头像
    console.warn(`头像资源未找到: ${key}，使用默认头像替代`);
    return this.defaultResources.defaultAvatar;
  }
  
  // 判断资源是否已加载
  isResourceLoaded(key) {
    return !!this.resources[key];
  }
  
  // 判断资源是否加载失败
  isResourceError(key) {
    return !!this.loadErrors[key];
  }
  
  // 获取加载错误信息
  getLoadError(key) {
    return this.loadErrors[key];
  }
  
  // 获取所有加载错误
  getAllLoadErrors() {
    return this.loadErrors;
  }
  
  // 重试加载失败的资源
  retryFailedResources(onProgress, onComplete) {
    const failedKeys = Object.keys(this.loadErrors);
    const totalCount = failedKeys.length;
    
    if (totalCount === 0) {
      if (onComplete) {
        onComplete(this.resources, 0);
      }
      return;
    }
    
    let loadedCount = 0;
    let errorCount = 0;
    
    // 循环重试每个失败的资源
    failedKeys.forEach(key => {
      const url = this.loadErrors[key].url;
      
      // 移除原有错误记录
      delete this.loadErrors[key];
      
      this.loadResource(key, url)
        .then(() => {
          loadedCount++;
          
          // 检查是否加载失败
          if (this.isResourceError(key)) {
            errorCount++;
          }
          
          // 更新进度
          if (onProgress) {
            onProgress(loadedCount / totalCount, key);
          }
          
          // 检查是否全部加载完成
          if (loadedCount === totalCount && onComplete) {
            onComplete(this.resources, errorCount);
          }
        })
        .catch(error => {
          console.error(`重试加载资源出错: ${key}`, error);
          loadedCount++;
          errorCount++;
          
          // 更新进度
          if (onProgress) {
            onProgress(loadedCount / totalCount, key);
          }
          
          // 检查是否全部加载完成
          if (loadedCount === totalCount && onComplete) {
            onComplete(this.resources, errorCount);
          }
        });
    });
  }
  
  // 移除已加载的资源
  removeResource(key) {
    if (this.resources[key]) {
      delete this.resources[key];
      if (this.loadErrors[key]) {
        delete this.loadErrors[key];
      }
      return true;
    }
    return false;
  }
  
  // 清空所有已加载的资源
  clearResources() {
    this.resources = {};
    this.loadErrors = {};
  }
  
  // 预加载关键资源
  preloadCriticalResources(callback) {
    // 定义关键资源列表
    const criticalResources = {
      // 通用UI元素
      'avatarFrame': 'images/ui/avatar_frame.png',
      'iconXianyu': 'images/ui/icon_xianyu.png',
      'iconLingshi': 'images/ui/icon_lingshi.png',
      'buttonBg': 'images/ui/button_bg.png',
      
      // 场景背景
      'mainBg': 'images/backgrounds/main_bg.jpg',
      
      // 默认头像
      'defaultAvatar': 'images/avatars/default.png'
    };
    
    this.loadResources(criticalResources, null, (resources, errorCount) => {
      if (callback) {
        callback(errorCount === 0);
      }
    });
  }
}

export default ResourceLoader; 