/**
 * 事件发射器类
 * 用于实现简单的事件订阅和触发功能
 */
class EventEmitter {
  constructor() {
    // 事件监听器
    this.eventListeners = {};
  }
  
  /**
   * 注册事件监听器
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  on(eventName, callback) {
    if (!this.eventListeners[eventName]) {
      this.eventListeners[eventName] = [];
    }
    this.eventListeners[eventName].push(callback);
    
    // 返回取消订阅的函数
    return () => this.off(eventName, callback);
  }
  
  /**
   * 移除事件监听器
   * @param {string} eventName 事件名称
   * @param {Function} callback 要移除的回调函数
   */
  off(eventName, callback) {
    if (!this.eventListeners[eventName]) return;
    
    if (callback) {
      // 移除特定回调
      this.eventListeners[eventName] = this.eventListeners[eventName].filter(
        cb => cb !== callback
      );
    } else {
      // 移除所有该事件的回调
      delete this.eventListeners[eventName];
    }
  }
  
  /**
   * 触发事件
   * @param {string} eventName 事件名称
   * @param {*} data 事件数据
   */
  emit(eventName, data) {
    if (!this.eventListeners[eventName]) return;
    
    this.eventListeners[eventName].forEach(callback => {
      try {
        callback(data);
      } catch (err) {
        console.error(`事件处理器错误 [${eventName}]:`, err);
      }
    });
  }
  
  /**
   * 一次性事件监听器
   * @param {string} eventName 事件名称
   * @param {Function} callback 回调函数
   */
  once(eventName, callback) {
    const onceWrapper = (data) => {
      callback(data);
      this.off(eventName, onceWrapper);
    };
    
    return this.on(eventName, onceWrapper);
  }
  
  /**
   * 清除所有事件监听器
   */
  clearAllListeners() {
    this.eventListeners = {};
  }
}

export default EventEmitter; 