/**
 * 测试修复后的代码
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    console.log(`模拟获取存储: ${key}`);
    return null;
  },
  setStorageSync: (key, value) => {
    console.log(`模拟保存存储: ${key}`);
  },
  cloud: null // 模拟云开发不可用
};

// 模拟AppContext
global.AppContext = {
  game: null
};

// 导入需要测试的类
import Character from './js/models/Character.js';
import DatabaseManager from './js/managers/DatabaseManager.js';
import GameStateManager from './js/managers/GameStateManager.js';

console.log('开始测试修复...');

// 测试1: Character构造函数
try {
  console.log('测试1: Character构造函数');
  const character = new Character({
    id: 1,
    name: '测试角色',
    level: 1,
    exp: 0,
    attributes: {
      hp: 100,
      attack: 10,
      defense: 5,
      speed: 10,
      critRate: 0.05,
      critDamage: 1.5
    },
    cultivation: '练气期一层',
    star: 0
  });
  
  console.log('✓ Character构造函数测试通过');
  console.log('角色信息:', {
    id: character.id,
    name: character.name,
    star: character.star,
    cultivation: character.cultivation
  });
} catch (error) {
  console.error('✗ Character构造函数测试失败:', error);
}

// 测试2: DatabaseManager初始化
try {
  console.log('\n测试2: DatabaseManager初始化');
  const dbManager = new DatabaseManager();
  
  console.log('✓ DatabaseManager初始化测试通过');
  console.log('数据库初始化状态:', dbManager.isInitialized());
} catch (error) {
  console.error('✗ DatabaseManager初始化测试失败:', error);
}

// 测试3: GameStateManager初始化
try {
  console.log('\n测试3: GameStateManager初始化');
  const gameStateManager = new GameStateManager();
  
  console.log('✓ GameStateManager初始化测试通过');
  console.log('游戏状态:', {
    characters: gameStateManager.state.characters.length,
    items: gameStateManager.state.items.length,
    player: gameStateManager.state.player.nickname
  });
} catch (error) {
  console.error('✗ GameStateManager初始化测试失败:', error);
}

console.log('\n测试完成！');
