# 新登录系统说明

## 系统概述

新的登录系统实现了您要求的完整登录流程：

1. **游戏加载完成后自动判断**：检查玩家是否曾经登录过
2. **老用户自动登录**：如果曾经登录过，自动获取openid并从云数据库加载数据
3. **新用户完整流程**：首次登录需要隐私授权 → 服务器选择 → 进入游戏
4. **延迟执行openid相关功能**：只在登录完成后才启动数据库操作和自动同步

## 核心组件

### 1. LoginManager (登录管理器)

**位置**: `js/managers/LoginManager.js`

**主要功能**:
- 管理登录状态和流程
- 处理隐私权限授权
- 服务器选择
- openid获取和管理
- 云数据库数据加载

**登录状态**:
```javascript
LOGIN_STATUS = {
  NOT_LOGGED: 'not_logged',           // 未登录
  PRIVACY_PENDING: 'privacy_pending', // 等待隐私授权
  SERVER_SELECTION: 'server_selection', // 服务器选择
  LOGGED_IN: 'logged_in'              // 已登录
}
```

### 2. 登录流程

#### 自动登录流程（老用户）
```
游戏启动 → 检查登录历史 → 获取openid → 选择上次服务器 → 加载云数据 → 启动openid功能 → 完成登录
```

#### 完整登录流程（新用户）
```
游戏启动 → 检查登录历史 → 显示隐私授权弹窗 → 用户授权 → 服务器选择 → 获取openid → 加载云数据 → 启动openid功能 → 完成登录
```

## 主要方法

### LoginManager 核心方法

1. **initializeLogin()** - 初始化登录流程
2. **checkPreviousLogin()** - 检查是否曾经登录过
3. **autoLogin()** - 自动登录（老用户）
4. **showPrivacyAuthDialog()** - 显示隐私授权弹窗
5. **showServerSelection()** - 显示服务器选择
6. **getUserOpenId()** - 获取用户openid
7. **loadUserDataFromCloud()** - 从云数据库加载数据
8. **startOpenIdRelatedFeatures()** - 启动openid相关功能

### Game类集成

1. **initializeLoginFlow()** - 在游戏启动后调用登录流程
2. **startAutoSync()** - 修改为只在登录后启动

## 使用方式

### 1. 游戏启动流程

游戏会在 `startGame()` 方法最后自动调用 `initializeLoginFlow()`：

```javascript
// 在game.js的startGame方法中
// 初始化登录流程（在游戏完全加载后）
this.initializeLoginFlow();
```

### 2. 手动登录

用户点击登录按钮时：

```javascript
// 在MainScene.js中
handleLoginButton() {
  game.loginManager.manualLogin();
}
```

### 3. 检查登录状态

```javascript
const loginStatus = game.loginManager.getLoginStatus();
console.log('登录状态:', loginStatus);
// 返回: { isLoggedIn, status, openid, server, hasPrivacyAuth }
```

### 4. 登录完成事件

```javascript
// 监听登录完成事件
game.eventSystem.on('loginCompleted', (data) => {
  console.log('登录完成:', data.openid, data.server);
  // 在这里可以执行登录后的操作
});
```

## 服务器配置

### 可用服务器列表

```javascript
getAvailableServers() {
  return [
    { id: 'server1', name: '青云门', region: 'cn-east' },
    { id: 'server2', name: '天音寺', region: 'cn-south' },
    { id: 'server3', name: '鬼王宗', region: 'cn-north' },
    { id: 'server4', name: '合欢派', region: 'cn-west' }
  ];
}
```

### 默认服务器

```javascript
getDefaultServer() {
  return { id: 'server1', name: '青云门', region: 'cn-east' };
}
```

## 数据存储

### 本地存储

- `userInfo` - 用户基本信息和授权状态
- `openid` - 用户openid
- `lastSelectedServer` - 上次选择的服务器

### 云数据库

通过 `DatabaseManager` 进行数据操作，只在登录完成后才执行。

## 错误处理

### 1. 登录失败处理

```javascript
handleLoginError(error) {
  wx.showModal({
    title: '登录失败',
    content: '登录过程中出现错误，是否重试？',
    confirmText: '重试',
    cancelText: '离线游戏',
    success: (res) => {
      if (res.confirm) {
        this.initializeLogin();
      } else {
        this.startOfflineMode();
      }
    }
  });
}
```

### 2. 隐私授权被拒绝

```javascript
handlePrivacyAuthDenied() {
  wx.showModal({
    title: '提示',
    content: '未授权将无法保存游戏进度，您可以继续游戏但数据不会同步到云端。是否重新授权？',
    confirmText: '重新授权',
    cancelText: '继续游戏',
    success: (res) => {
      if (res.confirm) {
        this.showPrivacyAuthDialog();
      } else {
        this.startOfflineMode();
      }
    }
  });
}
```

### 3. 离线模式

如果用户拒绝授权或登录失败，可以启动离线模式：

```javascript
startOfflineMode() {
  console.log('启动离线模式');
  this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;
  
  wx.showToast({
    title: '离线模式',
    icon: 'none',
    duration: 2000
  });
}
```

## 优势特性

### 1. 智能判断

- 自动检测用户是否曾经登录过
- 老用户无需重复授权，直接进入游戏
- 新用户引导完整授权流程

### 2. 容错机制

- 登录失败自动重试或进入离线模式
- 网络异常时使用本地存储
- 完整的错误处理和用户提示

### 3. 性能优化

- 延迟执行openid相关功能
- 避免游戏启动时的无效数据库调用
- 只在必要时进行云数据库操作

### 4. 用户体验

- 流畅的登录流程
- 清晰的状态提示
- 支持离线游戏模式

## 注意事项

1. **云函数依赖**: 需要确保 `login` 云函数正常工作
2. **数据库权限**: 确保云数据库权限配置正确
3. **服务器配置**: 可以根据需要修改服务器列表
4. **隐私政策**: 确保隐私授权描述符合微信小程序规范

## 测试建议

1. **首次登录测试**: 清除本地存储，测试完整登录流程
2. **重复登录测试**: 测试自动登录功能
3. **网络异常测试**: 测试离线模式和错误处理
4. **服务器切换测试**: 测试服务器选择功能

现在您的游戏具备了完整的登录系统，可以智能判断用户状态并提供相应的登录流程！
