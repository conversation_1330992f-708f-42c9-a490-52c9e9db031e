/**
 * 竞技场战斗结果场景
 * 显示竞技场战斗的结果和排名变化
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class ArenaBattleResultScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 战斗结果
    this.battleResult = null;
    
    // 完成回调
    this.onComplete = null;
    
    // 初始化UI
    this.initUI();
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建确认按钮
    this.createConfirmButton();
  }
  
  // 创建确认按钮
  createConfirmButton() {
    const buttonWidth = 150;
    const buttonHeight = 50;
    const margin = 20;
    
    this.confirmButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - buttonHeight - margin - 60, // 底部导航栏上方
      buttonWidth,
      buttonHeight,
      '确认',
      null,
      '#4299e1',
      () => {
        if (this.onComplete) {
          this.onComplete();
        } else {
          this.sceneManager.showScene('arena');
        }
      }
    );
    
    this.addUIElement(this.confirmButton);
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 保存战斗结果
    if (params && params.result) {
      this.battleResult = params.result;
    }
    
    // 保存完成回调
    if (params && params.onComplete) {
      this.onComplete = params.onComplete;
    } else {
      this.onComplete = null;
    }
    
    // 清空UI元素
    this.clearUIElements();
    
    // 初始化UI
    this.initUI();
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 重置战斗结果
    this.battleResult = null;
    this.onComplete = null;
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制战斗结果
    this.drawBattleResult();
  }
  
  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    
    if (this.battleResult && this.battleResult.playerWin) {
      // 胜利背景
      gradient.addColorStop(0, '#1a4731');
      gradient.addColorStop(1, '#276749');
    } else {
      // 失败背景
      gradient.addColorStop(0, '#742a2a');
      gradient.addColorStop(1, '#9b2c2c');
    }
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  // 绘制标题
  drawTitle() {
    const headerHeight = 80;
    
    // 绘制标题背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制标题文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    if (this.battleResult && this.battleResult.playerWin) {
      this.ctx.fillText('战斗胜利', this.screenWidth / 2, headerHeight / 2);
    } else {
      this.ctx.fillText('战斗失败', this.screenWidth / 2, headerHeight / 2);
    }
  }
  
  // 绘制战斗结果
  drawBattleResult() {
    if (!this.battleResult) return;
    
    const headerHeight = 80;
    const margin = 20;
    const centerY = (this.screenHeight + headerHeight) / 2;
    
    // 绘制结果信息背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(margin, centerY - 100, this.screenWidth - margin * 2, 200);
    
    // 绘制结果信息
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    // 对手信息
    this.ctx.fillText(`对手: ${this.battleResult.opponent.name}`, this.screenWidth / 2, centerY - 70);
    this.ctx.fillText(`对手境界: ${this.battleResult.opponent.cultivation}`, this.screenWidth / 2, centerY - 40);
    this.ctx.fillText(`对手战力: ${this.battleResult.opponentPower}`, this.screenWidth / 2, centerY - 10);
    
    // 玩家信息
    this.ctx.fillText(`你的战力: ${this.battleResult.playerPower}`, this.screenWidth / 2, centerY + 20);
    
    // 排名变化
    if (this.battleResult.playerWin && this.battleResult.oldRank !== this.battleResult.newRank) {
      this.ctx.fillText(`排名变化: 第${this.battleResult.oldRank}名 → 第${this.battleResult.newRank}名`, this.screenWidth / 2, centerY + 50);
    } else {
      this.ctx.fillText(`排名: 第${this.battleResult.newRank}名 (未变动)`, this.screenWidth / 2, centerY + 50);
    }
    
    // 结果说明
    if (this.battleResult.playerWin) {
      if (this.battleResult.oldRank !== this.battleResult.newRank) {
        this.ctx.fillText('恭喜你击败对手，排名提升！', this.screenWidth / 2, centerY + 80);
      } else {
        this.ctx.fillText('恭喜你击败对手，但排名未变动。', this.screenWidth / 2, centerY + 80);
      }
    } else {
      this.ctx.fillText('很遗憾，你被对手击败了。', this.screenWidth / 2, centerY + 80);
    }
  }
}

export default ArenaBattleResultScene;
