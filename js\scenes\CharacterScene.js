/**
 * 角色页面场景类
 * 展示所有拥有的角色
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class CharacterScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 角色列表
    this.characters = [];
    
    // 页面状态
    this.pageTitle = '角色';
    
    // 移除在构造函数中对initUI的调用
    // 初始化UI将在onShow方法中调用
  }
  
  // 初始化UI
  initUI() {
    // 尝试获取资源
    this.resources = game.resourceLoader.resources;
    
    // 创建返回按钮
    const backButtonSize = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回主场景
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();
    
    // 获取角色列表
    this.characters = game.gameStateManager.getCharacters();
    
    // 设置场景为可见状态
    this.visible = true;
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  // 处理触摸开始事件
  handleTouchStart(x, y) {
    console.log(`CharacterScene handleTouchStart: ${x},${y}`);
    
    // 检查是否点击了角色卡片
    for (let i = 0; i < this.characters.length; i++) {
      const character = this.characters[i];
      const card = this.characterCards[i];
      
      if (card && card.isPointInside(x, y)) {
        console.log(`点击了角色卡片: ${character.name}`);
        this.showCharacterDetail(character.id);
        return true;
      }
    }
    
    return false;
  }

  // 处理触摸移动事件
  handleTouchMove(x, y) {
    return false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    return false;
  }
  
  // 获取指定位置的角色
  getCharacterAtPosition(x, y) {
    // 顶部导航栏高度
    const headerHeight = 80;
    
    // 角色卡片尺寸
    const cardWidth = this.screenWidth / 3;
    const cardHeight = 150;
    
    // 计算行列
    const row = Math.floor((y - headerHeight) / cardHeight);
    const col = Math.floor(x / cardWidth);
    
    // 计算索引
    const index = row * 3 + col;
    
    // 检查索引是否有效
    if (index >= 0 && index < this.characters.length) {
      return this.characters[index];
    }
    
    return null;
  }
  
  // 显示角色详情
  showCharacterDetail(characterId) {
    console.log('显示角色详情，角色ID:', characterId);
    // 切换到角色详情场景，并传递角色ID
    this.sceneManager.showScene('characterDetail', { characterId });
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(168, 242, 243, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制顶部导航栏
    this.drawHeader();
    
    // 绘制角色列表
    this.drawCharacterList();
  }
  
  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    
    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 8);
    
    // 绘制资源信息
    const player = game.gameStateManager.getPlayer();
    const resourceY = headerHeight / 2;
    const iconSize = 20;
    
    // 绘制仙玉
    this.drawResourceIcon('iconXianyu', this.screenWidth / 2, resourceY, iconSize, '#ffd700');
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth / 2 + iconSize + 5, resourceY + 5);
    
    // 绘制灵石
    const lingshiX = this.screenWidth / 2 + 120;
    this.drawResourceIcon('iconLingshi', lingshiX, resourceY, iconSize, '#c0c0c0');
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${player.resources.lingshi}`, lingshiX + iconSize + 5, resourceY + 5);
  }
  
  // 绘制资源图标
  drawResourceIcon(iconKey, x, y, size, fallbackColor) {
    if (this.resources && this.resources[iconKey]) {
      try {
        this.ctx.drawImage(
          this.resources[iconKey],
          x,
          y - size / 2,
          size,
          size
        );
      } catch (error) {
        console.error(`绘制图标 ${iconKey} 失败`, error);
        this.drawDefaultIcon(x, y, size, fallbackColor);
      }
    } else {
      this.drawDefaultIcon(x, y, size, fallbackColor);
    }
  }
  
  // 绘制默认图标
  drawDefaultIcon(x, y, size, color) {
    // 如果是圆形图标
    if (color === '#ffd700') { // 仙玉用圆形
      this.ctx.fillStyle = color;
      this.ctx.beginPath();
      this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
      this.ctx.fill();
    } else { // 灵石用方形
      this.ctx.fillStyle = color;
      this.ctx.fillRect(x, y - size / 2, size, size);
    }
  }
  
  // 绘制角色列表
  drawCharacterList() {
    // 顶部导航栏高度
    const headerHeight = 80;
    
    // 角色卡片尺寸
    const cardWidth = this.screenWidth / 3;
    const cardHeight = 150;
    const avatarSize = 80;
    
    // 没有角色时显示提示
    if (!this.characters.length) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('暂无角色', this.screenWidth / 2, headerHeight + 100);
      return;
    }
    
    // 初始化角色卡片数组
    this.characterCards = [];
    
    // 绘制角色卡片
    this.characters.forEach((character, index) => {
      // 计算行列
      const row = Math.floor(index / 3);
      const col = index % 3;
      
      // 计算位置
      const x = col * cardWidth;
      const y = headerHeight + row * cardHeight;
      
      // 创建角色卡片对象（简化版，只记录位置和尺寸）
      this.characterCards[index] = {
        x: x + 5,
        y: y + 5,
        width: cardWidth - 10,
        height: cardHeight - 10,
        isPointInside: function(px, py) {
          return px >= this.x && px <= this.x + this.width && 
                 py >= this.y && py <= this.y + this.height;
        }
      };
      
      // 绘制卡片背景
      this.ctx.fillStyle = 'rgba(37, 181, 64, 0.7)';
      this.ctx.fillRect(x + 5, y + 5, cardWidth - 10, cardHeight - 10);
      
      // 绘制角色头像
      const avatarX = x + (cardWidth - avatarSize) / 2;
      const avatarY = y + 15;
      
      // 头像背景（圆形）
      this.ctx.fillStyle = '#ffffff';
      this.ctx.beginPath();
      this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2);
      this.ctx.fill();
      
      // 如果有角色图片资源，绘制角色图片
      const characterImgKey = `character${character.id}`;
      if (this.resources && this.resources[characterImgKey]) {
        try {
          // 绘制圆形头像
          this.ctx.save();
          this.ctx.beginPath();
          this.ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2);
          this.ctx.closePath();
          this.ctx.clip();
          
          this.ctx.drawImage(
            this.resources[characterImgKey],
            avatarX,
            avatarY,
            avatarSize,
            avatarSize
          );
          
          this.ctx.restore();
        } catch (error) {
          console.error(`绘制角色头像 ${characterImgKey} 失败`, error);
          this.drawDefaultCharacterAvatar(character, avatarX, avatarY, avatarSize);
        }
      } else {
        // 如果没有角色图片资源，绘制角色名称的第一个字作为头像
        this.drawDefaultCharacterAvatar(character, avatarX, avatarY, avatarSize);
      }
      
      // 绘制角色名称
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(
        character.name,
        x + cardWidth / 2,
        avatarY + avatarSize + 10
      );
      
      // 绘制角色等级和修为
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText(
        `Lv.${character.level} ${character.cultivation}`,
        x + cardWidth / 2,
        avatarY + avatarSize + 30
      );
    });
  }
  
  // 绘制默认角色头像（使用名字首字）
  drawDefaultCharacterAvatar(character, x, y, size) {
    this.ctx.font = 'bold 40px Arial';
    this.ctx.fillStyle = '#333333';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(
      character.name.charAt(0),
      x + size / 2,
      y + size / 2
    );
  }
}

export default CharacterScene; 