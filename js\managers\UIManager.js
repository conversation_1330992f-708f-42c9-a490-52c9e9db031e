/**
 * UI管理器类
 * 负责管理游戏中的UI元素
 */

// 引入UI组件
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';
import Loading from '../ui/Loading';

class UIManager {
  constructor(ctx, screenWidth, screenHeight) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    
    // UI元素
    this.loading = new Loading(this.ctx, this.screenWidth, this.screenHeight);
    
    // 缓存的UI元素
    this.cachedUI = {};
  }
  
  // 创建按钮
  createButton(x, y, width, height, text, normalImg, pressedImg, onClick) {
    return new Button(this.ctx, x, y, width, height, text, normalImg, pressedImg, onClick);
  }
  
  // 创建对话框
  createDialog(title, content, buttons, width, height) {
    const dialog = new Dialog(this.ctx, this.screenWidth, this.screenHeight, title, content, buttons, width, height);
    return dialog;
  }
  
  // 显示加载界面
  showLoading(text) {
    this.loading.show(text);
  }
  
  // 更新加载进度
  updateLoadingProgress(progress) {
    this.loading.updateProgress(progress);
  }
  
  // 隐藏加载界面
  hideLoading() {
    this.loading.hide();
  }
  
  // 绘制背景图片
  drawBackground(image, x = 0, y = 0, width = this.screenWidth, height = this.screenHeight) {
    this.ctx.drawImage(image, x, y, width, height);
  }
  
  // 绘制文本
  drawText(text, x, y, fontSize = 20, color = '#ffffff', align = 'left', maxWidth) {
    this.ctx.font = `${fontSize}px Arial`;
    this.ctx.fillStyle = color;
    this.ctx.textAlign = align;
    
    if (maxWidth) {
      this.ctx.fillText(text, x, y, maxWidth);
    } else {
      this.ctx.fillText(text, x, y);
    }
  }
  
  // 绘制圆形头像
  drawAvatar(image, x, y, radius) {
    this.ctx.save();
    this.ctx.beginPath();
    this.ctx.arc(x + radius, y + radius, radius, 0, Math.PI * 2, true);
    this.ctx.closePath();
    this.ctx.clip();
    
    this.ctx.drawImage(image, x, y, radius * 2, radius * 2);
    
    this.ctx.restore();
  }
  
  // 绘制带边框的圆形头像
  drawAvatarWithFrame(avatarImg, frameImg, x, y, radius) {
    // 绘制头像
    this.drawAvatar(avatarImg, x, y, radius);
    
    // 绘制边框
    const frameSize = radius * 2 + 10;
    this.ctx.drawImage(frameImg, x - 5, y - 5, frameSize, frameSize);
  }
  
  // 绘制资源图标和数量
  drawResource(iconImg, count, x, y, iconSize = 30) {
    // 绘制图标
    this.ctx.drawImage(iconImg, x, y, iconSize, iconSize);
    
    // 绘制数量
    this.drawText(count.toString(), x + iconSize + 5, y + iconSize / 2 + 5, 18, '#ffffff', 'left');
  }
  
  // 绘制底部选项卡
  drawTabBar(tabBarImg, tabIcons, selectedIndex, x, y, width, height, iconSize = 40) {
    // 绘制背景
    this.ctx.drawImage(tabBarImg, x, y, width, height);
    
    // 计算选项卡间距
    const tabCount = tabIcons.length;
    const spacing = width / tabCount;
    
    // 绘制选项卡图标
    for (let i = 0; i < tabCount; i++) {
      const iconX = x + spacing * i + (spacing - iconSize) / 2;
      const iconY = y + (height - iconSize) / 2;
      
      // 如果是选中的选项卡，绘制选中效果
      if (i === selectedIndex) {
        // 绘制选中背景
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.fillRect(x + spacing * i, y, spacing, height);
      }
      
      // 绘制图标
      this.ctx.drawImage(tabIcons[i], iconX, iconY, iconSize, iconSize);
    }
  }
  
  // 绘制装备格子
  drawEquipSlot(x, y, width, height, equipped = false, equipImg = null) {
    // 绘制背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    this.ctx.strokeStyle = equipped ? '#ffd700' : '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
    
    // 如果有装备，绘制装备图标
    if (equipped && equipImg) {
      this.ctx.drawImage(equipImg, x + 5, y + 5, width - 10, height - 10);
    } else {
      // 绘制空格子标记
      this.ctx.beginPath();
      this.ctx.moveTo(x + 10, y + 10);
      this.ctx.lineTo(x + width - 10, y + height - 10);
      this.ctx.moveTo(x + width - 10, y + 10);
      this.ctx.lineTo(x + 10, y + height - 10);
      this.ctx.strokeStyle = '#ffffff';
      this.ctx.lineWidth = 1;
      this.ctx.stroke();
    }
  }
}

export default UIManager; 