/**
 * 物品模板
 * 定义游戏中的各种物品，包括装备、消耗品等
 */

// 物品类型
export const ITEM_TYPES = {
    // 装备类型
    WEAPON: 'weapon',     // 武器
    ARMOR: 'armor',       // 护甲
    ACCESSORY: 'accessory', // 饰品
    ARTIFACT: 'artifact',   // 灵宝

    // 消耗品类型
    POTION: 'potion',     // 药水
    MATERIAL: 'material', // 材料
    FRAGMENT: 'fragment', // 角色碎片
    CURRENCY: 'currency', // 货币
};

// 物品品质
export const ITEM_QUALITY = {
    NORMAL: 0,    // 普通
    GOOD: 1,      // 良好
    RARE: 2,      // 稀有
    EPIC: 3,      // 史诗
    LEGENDARY: 4, // 传说
};

// 物品品质名称
export const ITEM_QUALITY_NAMES = [
    '普通', // 0
    '良好', // 1
    '稀有', // 2
    '史诗', // 3
    '传说'  // 4
];

// 物品品质颜色
export const ITEM_QUALITY_COLORS = [
    '#CCCCCC', // 普通 (灰色)
    '#55AA55', // 良好 (绿色)
    '#5555AA', // 稀有 (蓝色)
    '#AA55AA', // 史诗 (紫色)
    '#AAAA55'  // 传说 (金色)
];

// 装备模板
const EQUIPMENT_TEMPLATES = [
    // 武器 - 普通
    {
        id: 'weapon_normal_1',
        name: '练气木剑',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 1,
        description: '刚入门弟子常用的木剑，用于入门修炼。',
        attributes: {
            attack: 10
        }
    },
    {
        id: 'weapon_normal_2',
        name: '青铜短刀',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 5,
        description: '青铜打造的短刀，略有锋利。',
        attributes: {
            attack: 20
        }
    },

    // 武器 - 良好
    {
        id: 'weapon_good_1',
        name: '玄铁剑',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 10,
        description: '用玄铁精心打造的剑，锋利坚固。',
        attributes: {
            attack: 40,
            critRate: 0.03
        }
    },
    {
        id: 'weapon_good_2',
        name: '青龙偃月刀',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 15,
        description: '刀身弯曲如月，舞动间如青龙出海。',
        attributes: {
            attack: 60,
            critDamage: 0.10
        }
    },

    // 武器 - 稀有
    {
        id: 'weapon_rare_1',
        name: '冰魄枪',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 20,
        description: '枪尖蕴含寒冰之力，刺中敌人会带来额外伤害。',
        attributes: {
            attack: 80,
            critRate: 0.06,
            critDamage: 0.15
        }
    },
    {
        id: 'weapon_rare_2',
        name: '紫霄神剑',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 25,
        description: '剑身缭绕紫色雷电，击中敌人会造成麻痹效果。',
        attributes: {
            attack: 100,
            critRate: 0.08,
            critDamage: 0.20
        }
    },

    // 武器 - 史诗
    {
        id: 'weapon_epic_1',
        name: '赤焰心剑',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 30,
        description: '剑身燃烧着永不熄灭的真火，每一击都带有灼热之力。',
        attributes: {
            attack: 150,
            critRate: 0.10,
            critDamage: 0.30,
            penetration: 10
        }
    },
    {
        id: 'weapon_epic_2',
        name: '太阳神弓',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 35,
        description: '弓箭蕴含太阳真火，射出的箭矢如同流星。',
        attributes: {
            attack: 180,
            critRate: 0.12,
            critDamage: 0.35,
            penetration: 15
        }
    },

    // 武器 - 传说
    {
        id: 'weapon_legendary_1',
        name: '诛仙剑',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 40,
        description: '上古仙器，一剑可诛仙，剑气纵横三千里。',
        attributes: {
            attack: 250,
            critRate: 0.15,
            critDamage: 0.50,
            penetration: 25,
            daoRule: 10
        }
    },
    {
        id: 'weapon_legendary_2',
        name: '混沌神戟',
        type: ITEM_TYPES.WEAPON,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 45,
        description: '蕴含混沌之力的神戟，挥舞时空间都为之扭曲。',
        attributes: {
            attack: 300,
            critRate: 0.18,
            critDamage: 0.60,
            penetration: 30,
            daoRule: 15
        }
    },

    // 护甲 - 普通
    {
        id: 'armor_normal_1',
        name: '布衣',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 1,
        description: '普通的布制衣物，几乎没有防护效果。',
        attributes: {
            hp: 20,
            defense: 5
        }
    },
    {
        id: 'armor_normal_2',
        name: '皮甲',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 5,
        description: '兽皮制成的护甲，提供基础防护。',
        attributes: {
            hp: 40,
            defense: 10
        }
    },

    // 护甲 - 良好
    {
        id: 'armor_good_1',
        name: '锁子甲',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 10,
        description: '金属锁环编织而成，灵活且有一定防护。',
        attributes: {
            hp: 80,
            defense: 20
        }
    },
    {
        id: 'armor_good_2',
        name: '玄铁铠甲',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 15,
        description: '玄铁打造的铠甲，坚固耐用。',
        attributes: {
            hp: 120,
            defense: 30
        }
    },

    // 护甲 - 稀有
    {
        id: 'armor_rare_1',
        name: '寒冰甲',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 20,
        description: '蕴含寒冰之力的铠甲，可抵御火属性攻击。',
        attributes: {
            hp: 180,
            defense: 45,
            speed: 5
        }
    },
    {
        id: 'armor_rare_2',
        name: '紫电战铠',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 25,
        description: '缭绕着雷电的铠甲，被击中的敌人会受到反击伤害。',
        attributes: {
            hp: 240,
            defense: 60,
            speed: 8
        }
    },

    // 护甲 - 史诗
    {
        id: 'armor_epic_1',
        name: '龙鳞甲',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 30,
        description: '真龙鳞片制成的铠甲，坚不可摧。',
        attributes: {
            hp: 350,
            defense: 80,
            speed: 10,
            critDamage: 0.10
        }
    },
    {
        id: 'armor_epic_2',
        name: '星辰战衣',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 35,
        description: '融入星辰之力的战衣，闪烁着点点星光。',
        attributes: {
            hp: 450,
            defense: 100,
            speed: 15,
            critDamage: 0.15
        }
    },

    // 护甲 - 传说
    {
        id: 'armor_legendary_1',
        name: '仙王法袍',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 40,
        description: '仙王穿过的法袍，蕴含无尽法则之力。',
        attributes: {
            hp: 600,
            defense: 150,
            speed: 20,
            critDamage: 0.20,
            daoRule: 8
        }
    },
    {
        id: 'armor_legendary_2',
        name: '混沌圣铠',
        type: ITEM_TYPES.ARMOR,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 45,
        description: '以混沌本源凝聚而成的铠甲，几乎无物可破。',
        attributes: {
            hp: 800,
            defense: 200,
            speed: 25,
            critDamage: 0.25,
            daoRule: 12
        }
    },

    // 饰品 - 各品质
    {
        id: 'accessory_normal_1',
        name: '玉佩',
        type: ITEM_TYPES.ACCESSORY,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 1,
        description: '普通的玉佩，略有灵气。',
        attributes: {
            hp: 10,
            speed: 2
        }
    },
    {
        id: 'accessory_good_1',
        name: '灵玉手环',
        type: ITEM_TYPES.ACCESSORY,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 10,
        description: '灵玉制成的手环，可以增强体内灵力流动。',
        attributes: {
            hp: 30,
            speed: 5,
            critRate: 0.02
        }
    },
    {
        id: 'accessory_rare_1',
        name: '青龙珠',
        type: ITEM_TYPES.ACCESSORY,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 20,
        description: '蕴含青龙之力的宝珠，可提升身法速度。',
        attributes: {
            hp: 50,
            speed: 15,
            critRate: 0.05
        }
    },
    {
        id: 'accessory_epic_1',
        name: '天机玉简',
        type: ITEM_TYPES.ACCESSORY,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 30,
        description: '记载天机的玉简，持有者可预知部分未来。',
        attributes: {
            hp: 100,
            speed: 25,
            critRate: 0.08,
            critDamage: 0.20
        }
    },
    {
        id: 'accessory_legendary_1',
        name: '太极图',
        type: ITEM_TYPES.ACCESSORY,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 40,
        description: '蕴含阴阳变化之理的太极图，可掌控周围力量变化。',
        attributes: {
            hp: 200,
            speed: 40,
            critRate: 0.12,
            critDamage: 0.30,
            daoRule: 5
        }
    },

    // 灵宝 - 各品质
    {
        id: 'artifact_normal_1',
        name: '聚灵盆',
        type: ITEM_TYPES.ARTIFACT,
        quality: ITEM_QUALITY.NORMAL,
        baseLevel: 1,
        description: '可以聚集周围一定范围内的灵气。',
        attributes: {
            attack: 5,
            defense: 5
        }
    },
    {
        id: 'artifact_good_1',
        name: '引雷旗',
        type: ITEM_TYPES.ARTIFACT,
        quality: ITEM_QUALITY.GOOD,
        baseLevel: 10,
        description: '可以引导天雷之力的法器。',
        attributes: {
            attack: 15,
            defense: 10,
            penetration: 5
        }
    },
    {
        id: 'artifact_rare_1',
        name: '八卦炉',
        type: ITEM_TYPES.ARTIFACT,
        quality: ITEM_QUALITY.RARE,
        baseLevel: 20,
        description: '以八卦为基础的炼器之宝，可加速炼化各种材料。',
        attributes: {
            attack: 30,
            defense: 20,
            penetration: 10,
            critRate: 0.04
        }
    },
    {
        id: 'artifact_epic_1',
        name: '昊天镜',
        type: ITEM_TYPES.ARTIFACT,
        quality: ITEM_QUALITY.EPIC,
        baseLevel: 30,
        description: '可照见万物本质的宝镜，破除一切虚妄。',
        attributes: {
            attack: 50,
            defense: 35,
            penetration: 20,
            critRate: 0.06,
            critDamage: 0.15
        }
    },
    {
        id: 'artifact_legendary_1',
        name: '山河社稷图',
        type: ITEM_TYPES.ARTIFACT,
        quality: ITEM_QUALITY.LEGENDARY,
        baseLevel: 40,
        description: '可容纳山河的灵宝，内有小世界。',
        attributes: {
            attack: 80,
            defense: 60,
            penetration: 30,
            critRate: 0.08,
            critDamage: 0.25,
            daoRule: 10
        }
    }
];

// 消耗品模板
const CONSUMABLE_TEMPLATES = [
    // 药水
    {
        id: 'potion_minor_healing',
        name: '小型回春丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.NORMAL,
        description: '服用后回复少量生命值',
        effect: {
            type: 'heal',
            value: 100
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'potion_medium_healing',
        name: '中型回春丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.GOOD,
        description: '服用后回复中量生命值',
        effect: {
            type: 'heal',
            value: 300
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'potion_major_healing',
        name: '大型回春丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.RARE,
        description: '服用后回复大量生命值',
        effect: {
            type: 'heal',
            value: 600
        },
        stackable: true,
        stackLimit: 99
    },
    // 灵力丹
    {
        id: 'linglidan_small',
        name: '小灵力丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.NORMAL,
        description: '服用后增加少量灵力，加速修炼进度',
        effect: {
            type: 'lingli',
            value: 100
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'linglidan_medium',
        name: '中灵力丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.GOOD,
        description: '服用后增加中量灵力，加速修炼进度',
        effect: {
            type: 'lingli',
            value: 500
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'linglidan_large',
        name: '大灵力丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.RARE,
        description: '服用后增加大量灵力，加速修炼进度',
        effect: {
            type: 'lingli',
            value: 2000
        },
        stackable: true,
        stackLimit: 99
    },
    // 突破丹
    {
        id: 'breakthrough_normal',
        name: '普通突破丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.NORMAL,
        description: '突破时使用，增加10%突破成功率',
        effect: {
            type: 'breakthrough',
            value: 0.1
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'breakthrough_good',
        name: '上品突破丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.GOOD,
        description: '突破时使用，增加20%突破成功率',
        effect: {
            type: 'breakthrough',
            value: 0.2
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'breakthrough_rare',
        name: '稀有突破丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.RARE,
        description: '突破时使用，增加30%突破成功率',
        effect: {
            type: 'breakthrough',
            value: 0.3
        },
        stackable: true,
        stackLimit: 99
    },
    {
        id: 'potion_exp',
        name: '修为丹',
        type: ITEM_TYPES.POTION,
        quality: ITEM_QUALITY.GOOD,
        description: '服用后可增加角色经验',
        effect: {
            type: 'exp',
            value: 100
        },
        stackable: true,
        stackLimit: 99
    },

    // 材料
    {
        id: 'material_iron_ore',
        name: '铁矿石',
        type: ITEM_TYPES.MATERIAL,
        quality: ITEM_QUALITY.NORMAL,
        description: '常见的铁矿石，可用于锻造装备',
        stackable: true,
        stackLimit: 999
    },
    {
        id: 'material_spirit_stone',
        name: '灵石',
        type: ITEM_TYPES.MATERIAL,
        quality: ITEM_QUALITY.NORMAL,
        description: '蕴含灵气的石头，是修仙界的通用货币',
        stackable: true,
        stackLimit: 9999
    },
    {
        id: 'material_high_spirit_stone',
        name: '高级灵石',
        type: ITEM_TYPES.MATERIAL,
        quality: ITEM_QUALITY.GOOD,
        description: '蕴含丰富灵气的灵石，价值10块普通灵石',
        stackable: true,
        stackLimit: 9999
    }
];

// 合并所有模板
export const ITEM_TEMPLATES = [...EQUIPMENT_TEMPLATES, ...CONSUMABLE_TEMPLATES];

/**
 * 根据ID获取物品模板
 * @param {string} id 物品ID
 * @returns {Object|null} 物品模板或null
 */
export function getItemTemplateById(id) {
    return ITEM_TEMPLATES.find(item => item.id === id) || null;
}

/**
 * 根据类型获取物品模板
 * @param {string} type 物品类型
 * @returns {Array} 物品模板数组
 */
export function getItemTemplatesByType(type) {
    return ITEM_TEMPLATES.filter(item => item.type === type);
}

/**
 * 根据品质获取物品模板
 * @param {number} quality 物品品质
 * @returns {Array} 物品模板数组
 */
export function getItemTemplatesByQuality(quality) {
    return ITEM_TEMPLATES.filter(item => item.quality === quality);
}

/**
 * 根据类型和品质获取物品模板
 * @param {string} type 物品类型
 * @param {number} quality 物品品质
 * @returns {Array} 物品模板数组
 */
export function getItemTemplatesByTypeAndQuality(type, quality) {
    return ITEM_TEMPLATES.filter(item => item.type === type && item.quality === quality);
}

/**
 * 创建物品实例
 * @param {string} templateId 物品模板ID
 * @param {Object} overrides 覆盖默认值的对象
 * @returns {Object|null} 物品实例或null
 */
export function createItemFromTemplate(templateId, overrides = {}) {
    const template = getItemTemplateById(templateId);
    if (!template) return null;

    // 生成唯一ID
    const uniqueId = `item_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // 创建物品实例
    const item = {
        id: uniqueId,
        templateId: template.id,
        name: template.name,
        type: template.type,
        quality: template.quality,
        description: template.description,
        level: template.baseLevel || 1,
        // 合并属性
        attributes: { ...(template.attributes || {}) },
        // 如果是消耗品，复制效果
        ...(template.effect ? { effect: { ...template.effect } } : {}),
        // 可堆叠属性
        stackable: template.stackable || false,
        stackLimit: template.stackLimit || 1,
        count: 1,
        // 覆盖默认值
        ...overrides
    };

    return item;
}

/**
 * 随机生成一个物品
 * @param {Object} options 选项
 * @param {string} [options.type] 物品类型（可选）
 * @param {number} [options.minQuality] 最低品质（可选）
 * @param {number} [options.maxQuality] 最高品质（可选）
 * @returns {Object|null} 物品实例或null
 */
export function generateRandomItem(options = {}) {
    // 过滤符合条件的模板
    let eligibleTemplates = [...ITEM_TEMPLATES];

    // 根据类型过滤
    if (options.type) {
        eligibleTemplates = eligibleTemplates.filter(item => item.type === options.type);
    }

    // 根据品质范围过滤
    if (options.minQuality !== undefined) {
        eligibleTemplates = eligibleTemplates.filter(item => item.quality >= options.minQuality);
    }

    if (options.maxQuality !== undefined) {
        eligibleTemplates = eligibleTemplates.filter(item => item.quality <= options.maxQuality);
    }

    if (eligibleTemplates.length === 0) return null;

    // 随机选择一个模板
    const randomTemplate = eligibleTemplates[Math.floor(Math.random() * eligibleTemplates.length)];

    // 随机等级加成
    const levelBonus = Math.floor(Math.random() * 5);

    // 创建物品实例
    return createItemFromTemplate(randomTemplate.id, {
        level: (randomTemplate.baseLevel || 1) + levelBonus
    });
}

export default {
    ITEM_TYPES,
    ITEM_QUALITY,
    ITEM_QUALITY_NAMES,
    ITEM_QUALITY_COLORS,
    ITEM_TEMPLATES,
    getItemTemplateById,
    getItemTemplatesByType,
    getItemTemplatesByQuality,
    getItemTemplatesByTypeAndQuality,
    createItemFromTemplate,
    generateRandomItem
};