/**
 * 战斗单位模型
 * 表示参与战斗的角色或敌人
 */
class BattleUnit {
  /**
   * 构造函数
   * @param {Object} data 单位数据
   * @param {string} type 单位类型 ('player' 或 'enemy')
   * @param {number} index 单位在阵容中的索引
   */
  constructor(data, type, index) {
    // 基本信息
    this.id = data.id;
    this.name = data.name || (type === 'player' ? `角色${index + 1}` : `敌人${index + 1}`);
    this.type = type;
    this.index = index;
    
    // 战斗属性
    this.level = data.level || 1;
    this.maxHp = data.maxHp || data.hp || 100; // 最大生命值
    this.hp = data.hp || 100; // 当前生命值
    this.attack = data.attack || 10; // 攻击力
    this.defense = data.defense || 5; // 防御力
    this.speed = data.speed || 10; // 速度
    this.critical = data.critical || 0.05; // 暴击率
    this.criticalDamage = data.criticalDamage || 1.5; // 暴击伤害
    
    // 技能
    this.skills = data.skills || []; // 技能列表
    
    // 战斗状态
    this.isDead = false; // 是否已死亡
    this.buffs = []; // 增益效果
    this.debuffs = []; // 减益效果
    
    // 其他属性
    this.combatPower = this.calculateCombatPower(); // 战斗力
    this.actionPoints = 0; // 行动点数
    
    // 阵型位置
    this.positionIndex = data.positionIndex || index; // 在战斗UI中的位置索引
    this.formationPosition = data.formationPosition; // 在阵型中的位置
  }
  
  /**
   * 计算战斗力
   * @returns {number} 战斗力数值
   */
  calculateCombatPower() {
    // 简单的战斗力计算公式
    const hp = this.maxHp;
    const atk = this.attack;
    const def = this.defense;
    const spd = this.speed;
    
    // 计算战斗力，给予不同属性不同权重
    return Math.floor(hp * 0.5 + atk * 2 + def * 1.5 + spd * 1);
  }
  
  /**
   * 检查单位是否存活
   * @returns {boolean} 是否存活
   */
  isAlive() {
    return this.hp > 0 && !this.isDead;
  }
  
  /**
   * 造成伤害
   * @param {number} damage 伤害值
   * @param {boolean} isCritical 是否暴击
   * @returns {number} 实际造成的伤害
   */
  takeDamage(damage, isCritical = false) {
    // 确保伤害至少为1
    const actualDamage = Math.max(1, damage);
    
    // 减少生命值
    this.hp = Math.max(0, this.hp - actualDamage);
    
    // 如果生命值为0，标记为死亡
    if (this.hp <= 0) {
      this.isDead = true;
    }
    
    return actualDamage;
  }
  
  /**
   * 恢复生命值
   * @param {number} amount 恢复量
   * @returns {number} 实际恢复的生命值
   */
  heal(amount) {
    // 确保恢复量至少为0
    const healAmount = Math.max(0, amount);
    
    // 不能超过最大生命值
    const actualHeal = Math.min(healAmount, this.maxHp - this.hp);
    
    // 增加生命值
    this.hp += actualHeal;
    
    return actualHeal;
  }
  
  /**
   * 计算对目标造成的伤害
   * @param {BattleUnit} target 目标单位
   * @returns {Object} 伤害信息 {damage, isCritical}
   */
  calculateDamage(target) {
    // 基础伤害 = 攻击力
    let damage = this.attack;
    
    // 检查是否暴击
    const isCritical = Math.random() < this.critical;
    
    // 如果暴击，增加伤害
    if (isCritical) {
      damage = Math.floor(damage * this.criticalDamage);
    }
    
    // 考虑目标防御力
    damage = Math.max(1, damage - target.defense / 2);
    
    return {
      damage: Math.floor(damage),
      isCritical: isCritical
    };
  }
  
  /**
   * 增加buff效果
   * @param {Object} buff buff数据
   */
  addBuff(buff) {
    this.buffs.push({ ...buff, remainingTurns: buff.duration });
  }
  
  /**
   * 增加debuff效果
   * @param {Object} debuff debuff数据
   */
  addDebuff(debuff) {
    this.debuffs.push({ ...debuff, remainingTurns: debuff.duration });
  }
  
  /**
   * 回合开始时更新
   */
  onTurnStart() {
    // 更新buff持续时间
    this.buffs = this.buffs.filter(buff => {
      buff.remainingTurns--;
      return buff.remainingTurns > 0;
    });
    
    // 更新debuff持续时间
    this.debuffs = this.debuffs.filter(debuff => {
      debuff.remainingTurns--;
      return debuff.remainingTurns > 0;
    });
    
    // 增加行动点数
    this.actionPoints += this.speed;
  }
  
  /**
   * 检查是否可以行动
   * @param {number} actionCost 行动消耗的点数
   * @returns {boolean} 是否可以行动
   */
  canAct(actionCost = 100) {
    return this.isAlive() && this.actionPoints >= actionCost;
  }
  
  /**
   * 消耗行动点数
   * @param {number} actionCost 行动消耗的点数
   */
  spendActionPoints(actionCost = 100) {
    this.actionPoints -= actionCost;
  }
  
  /**
   * 复制单位状态（用于战斗模拟）
   * @returns {BattleUnit} 单位状态的副本
   */
  clone() {
    const clone = new BattleUnit({
      id: this.id,
      name: this.name,
      level: this.level,
      maxHp: this.maxHp,
      hp: this.hp,
      attack: this.attack,
      defense: this.defense,
      speed: this.speed,
      critical: this.critical,
      criticalDamage: this.criticalDamage,
      skills: [...this.skills],
      positionIndex: this.positionIndex,
      formationPosition: this.formationPosition
    }, this.type, this.index);
    
    clone.isDead = this.isDead;
    clone.buffs = [...this.buffs];
    clone.debuffs = [...this.debuffs];
    clone.actionPoints = this.actionPoints;
    
    return clone;
  }
}

export default BattleUnit; 