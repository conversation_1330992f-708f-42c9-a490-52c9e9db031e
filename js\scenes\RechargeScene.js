/**
 * 充值场景
 * 用于处理游戏内充值和VIP系统
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class RechargeScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景名称
    this.name = 'recharge';
    this.pageTitle = '充值中心';

    // 当前选中的充值档位索引
    this.selectedTierIndex = -1;

    // 按钮区域
    this.buttons = {
      back: { x: 20, y: 50, width: 80, height: 40, text: '返回' }
    };

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    this.backButton = new Button(
      this.ctx,
      this.buttons.back.x,
      this.buttons.back.y,
      this.buttons.back.width,
      this.buttons.back.height,
      this.buttons.back.text,
      null,
      null,
      () => {
        this.goBack();
      }
    );

    this.addUIElement(this.backButton);

    // 创建充值档位按钮
    this.createRechargeTierButtons();

    // 创建VIP特权按钮
    this.createVIPPrivilegeButton();

    // 创建每日VIP奖励按钮
    this.createDailyVIPRewardButton();
  }

  // 创建充值档位按钮
  createRechargeTierButtons() {
    // 获取充值档位
    const rechargeTiers = game.vipSystem.getAllRechargeTiers();
    if (!rechargeTiers || rechargeTiers.length === 0) return;

    const buttonWidth = 200; // 减小按钮宽度
    const buttonHeight = 50; // 减小按钮高度
    const startY = 180;
    const padding = 15; // 减小间距

    // 创建每个充值档位的按钮
    rechargeTiers.forEach((tier, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        startY + (buttonHeight + padding) * index,
        buttonWidth,
        buttonHeight,
        `${tier.name} (${tier.xianyu}仙玉)`,
        null,
        null,
        () => {
          this.selectedTierIndex = index;
          this.confirmRecharge(tier);
        }
      );

      this.addUIElement(button);
    });
  }

  // 创建VIP特权按钮
  createVIPPrivilegeButton() {
    const buttonWidth = 200;
    const buttonHeight = 50;
    const y = this.screenHeight - 180;

    const vipPrivilegeButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      y,
      buttonWidth,
      buttonHeight,
      'VIP特权详情',
      null,
      null,
      () => {
        this.showVIPPrivileges();
      }
    );

    this.addUIElement(vipPrivilegeButton);
  }

  // 创建每日VIP奖励按钮
  createDailyVIPRewardButton() {
    const buttonWidth = 200;
    const buttonHeight = 50;
    const y = this.screenHeight - 100;

    // 检查是否可以领取每日奖励
    const player = game.gameStateManager.getPlayer();
    const canClaim = game.vipSystem.shouldGiveDailyReward(player);
    const vipLevel = player.vipLevel || 0;

    // 如果不是VIP或今天已经领取过，禁用按钮
    const buttonText = canClaim && vipLevel > 0 ? '领取每日VIP奖励' : '每日VIP奖励(已领取)';
    const buttonColor = canClaim && vipLevel > 0 ? '#4CAF50' : '#9E9E9E';

    const dailyRewardButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      y,
      buttonWidth,
      buttonHeight,
      buttonText,
      buttonColor,
      null,
      () => {
        if (canClaim && vipLevel > 0) {
          this.claimDailyVIPReward();
        } else if (vipLevel === 0) {
          wx.showToast({
            title: '成为VIP后可领取每日奖励',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: '今日已领取，明天再来',
            icon: 'none',
            duration: 2000
          });
        }
      }
    );

    this.addUIElement(dailyRewardButton);
  }

  // 确认充值
  confirmRecharge(tier) {
    if (!tier) return;

    wx.showModal({
      title: '确认充值',
      content: `确定要充值${tier.amount}元，获得${tier.xianyu}仙玉吗？\n\n注意：当前为测试版本，点击确定后将直接获得仙玉，无需实际付款。`,
      success: (res) => {
        if (res.confirm) {
          this.processRecharge(tier);
        }
      }
    });
  }

  // 处理充值
  processRecharge(tier) {
    if (!tier) return;

    // 调用游戏状态管理器处理充值
    const success = game.gameStateManager.processRecharge(tier.amount, tier.xianyu);

    if (success) {
      // 显示充值成功提示
      wx.showToast({
        title: `充值成功，获得${tier.xianyu}仙玉`,
        icon: 'success',
        duration: 2000
      });

      // 检查是否升级了VIP等级
      const player = game.gameStateManager.getPlayer();
      const vipLevel = player.vipLevel || 0;

      // 如果是VIP1或以上，显示VIP升级提示
      if (vipLevel > 0) {
        setTimeout(() => {
          wx.showToast({
            title: `恭喜成为${game.vipSystem.getVIPLevelInfo(vipLevel).name}`,
            icon: 'success',
            duration: 3000
          });
        }, 2000);
      }

      // 更新UI
      this.initUI();
    } else {
      // 显示充值失败提示
      wx.showToast({
        title: '充值失败，请稍后再试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 领取每日VIP奖励
  claimDailyVIPReward() {
    // 调用游戏状态管理器领取每日VIP奖励
    const rewards = game.gameStateManager.claimDailyVIPReward();

    if (rewards) {
      // 显示领取成功提示
      wx.showToast({
        title: `领取成功，获得${rewards.lingshi}灵石和${rewards.xianyu}仙玉`,
        icon: 'success',
        duration: 2000
      });

      // 更新UI
      this.initUI();
    } else {
      // 显示领取失败提示
      wx.showToast({
        title: '领取失败，请稍后再试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 显示VIP特权详情
  showVIPPrivileges() {
    this.sceneManager.showScene('vipPrivilege', {
      returnScene: 'recharge'
    });
  }

  // 返回上一个场景
  goBack() {
    if (this.params && this.params.returnScene) {
      this.sceneManager.showScene(this.params.returnScene, this.params.returnParams || {});
    } else {
      this.sceneManager.showScene('main');
    }
  }

  // 场景显示时的回调
  onShow(params) {
    this.visible = true;

    // 保存传入的参数
    this.params = params || {};

    // 初始化UI
    this.initUI();
  }

  // 处理点击事件
  handleClick(x, y) {
    // 检查是否点击了返回按钮
    if (this.isPointInRect(x, y, this.buttons.back)) {
      this.goBack();
      return;
    }

    // 调用父类的点击处理方法
    super.handleClick(x, y);
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制VIP信息
    this.drawVIPInfo();

    // 绘制充值档位
    this.drawRechargeTiers();
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.pageTitle, this.screenWidth / 2, headerHeight / 2 + 8);

    // 绘制返回按钮
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
    this.ctx.fillRect(this.buttons.back.x, this.buttons.back.y, this.buttons.back.width, this.buttons.back.height);
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      this.buttons.back.text,
      this.buttons.back.x + this.buttons.back.width / 2,
      this.buttons.back.y + this.buttons.back.height / 2 + 5
    );
  }

  // 绘制VIP信息
  drawVIPInfo() {
    const player = game.gameStateManager.getPlayer();
    const vipLevel = player.vipLevel || 0;
    const totalRecharge = player.totalRecharge || 0;

    // 获取VIP等级信息
    const vipInfo = game.vipSystem.getVIPLevelInfo(vipLevel);

    // 获取下一级VIP信息
    const nextVIPInfo = game.vipSystem.getNextVIPLevelRequirement(vipLevel, totalRecharge);

    // 绘制当前VIP等级
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = vipLevel > 0 ? '#FFD700' : '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`当前VIP等级: ${vipInfo.name}`, this.screenWidth / 2, 100);

    // 绘制总充值金额
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`总充值金额: ${totalRecharge}元`, this.screenWidth / 2, 125);

    // 如果有下一级VIP，绘制升级提示
    if (nextVIPInfo.nextLevel) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFCC00';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        `再充值${nextVIPInfo.requiredAmount}元可升级到${nextVIPInfo.nextLevel.name}`,
        this.screenWidth / 2,
        150
      );
    }
  }

  // 绘制充值档位
  drawRechargeTiers() {
    // 获取充值档位
    const rechargeTiers = game.vipSystem.getAllRechargeTiers();
    if (!rechargeTiers || rechargeTiers.length === 0) return;

    const startY = 180;
    const padding = 10;
    const itemHeight = 50; // 减小高度
    const itemWidth = 200; // 减小宽度

    // 绘制充值档位标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('充值档位', this.screenWidth / 2, startY);

    // 绘制每个充值档位
    rechargeTiers.forEach((tier, index) => {
      const y = startY + 30 + (itemHeight + padding) * index;

      // 绘制档位背景
      this.ctx.fillStyle = this.selectedTierIndex === index ? 'rgba(100, 100, 200, 0.5)' : 'rgba(50, 50, 50, 0.5)';
      this.ctx.fillRect((this.screenWidth - itemWidth) / 2, y, itemWidth, itemHeight);

      // 绘制档位名称
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(tier.name, (this.screenWidth - itemWidth) / 2 + 15, y + 20);

      // 绘制仙玉数量
      this.ctx.font = 'bold 16px Arial';
      this.ctx.fillStyle = '#FFD700';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`${tier.xianyu}仙玉`, (this.screenWidth + itemWidth) / 2 - 15, y + 20);

      // 绘制价格
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(`价格: ${tier.amount}元`, (this.screenWidth - itemWidth) / 2 + 15, y + 40);
    });
  }
}

export default RechargeScene;
