/**
 * 战斗结果场景
 * 显示战斗结束后的奖励和结算信息
 */
import BaseScene from '../../scenes/BaseScene';
import Button from '../../ui/Button';
import game from '../../../game';

class BattleResultScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 结算数据
    this.battleResult = null;
    this.rewards = null;
    
    // 结算完成回调
    this.onComplete = null;
    
    // 自动关闭计时器
    this.autoCloseTimer = null;
    this.autoCloseDelay = 5000; // 5秒后自动关闭
    
    // UI元素
    this.closeButton = null;
    
    // 动画相关
    this.showStartTime = 0;
    this.animationDuration = 500;
    this.itemAnimations = []; // 奖励物品动画
  }
  
  /**
   * 场景显示回调
   * @param {Object} params 场景参数
   */
  onShow(params) {
    console.log('结算场景显示', params);
    
    // 清空UI元素
    this.clearUIElements();
    
    // 保存结果和奖励
    this.battleResult = params.result || { victory: false };
    this.rewards = params.rewards || {};
    this.onComplete = params.onComplete || null;
    
    // 记录显示开始时间
    this.showStartTime = Date.now();
    
    // 初始化物品动画
    this.initItemAnimations();
    
    // 初始化UI
    this.initUI();
    
    // 设置场景为可见
    this.visible = true;
    
    // 设置自动关闭计时器
    this.autoCloseTimer = setTimeout(() => {
      this.closeResult();
    }, this.autoCloseDelay);
    
    // 如果战斗胜利，添加奖励到玩家背包
    if (this.battleResult.victory) {
      this.addRewardsToPlayer();
    }
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    // 资源初始化
    this.resources = game.resourceLoader.resources;
    
    // 初始化关闭按钮
    this.initCloseButton();
  }
  
  /**
   * 初始化奖励物品动画
   */
  initItemAnimations() {
    this.itemAnimations = [];
    
    // 添加每种类型的奖励项动画
    let index = 0;
    
    // 遍历所有奖励类型
    Object.entries(this.rewards).forEach(([type, amount]) => {
      if (amount > 0) {
        // 计算动画开始位置
        const startDelay = 200 + index * 100;
        
        this.itemAnimations.push({
          type: type,
          amount: amount,
          startTime: this.showStartTime + startDelay,
          duration: 800,
          startScale: 0.5,
          endScale: 1.0,
          index: index
        });
        
        index++;
      }
    });
  }
  
  /**
   * 初始化关闭按钮
   */
  initCloseButton() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const buttonX = (this.screenWidth - buttonWidth) / 2;
    const buttonY = this.screenHeight * 0.7;
    
    this.closeButton = new Button(
      this.ctx,
      buttonX,
      buttonY,
      buttonWidth,
      buttonHeight,
      '关闭',
      null,
      null,
      () => {
        this.closeResult();
      }
    );
    
    this.addUIElement(this.closeButton);
  }
  
  /**
   * 将奖励添加到玩家账户
   */
  addRewardsToPlayer() {
    try {
      // 使用新增的addResources方法添加资源
      const resourceRewards = {};
      
      // 添加灵石
      if (this.rewards.lingshi) {
        resourceRewards.lingshi = this.rewards.lingshi;
      }
      
      // 添加仙玉
      if (this.rewards.xianyu) {
        resourceRewards.xianyu = this.rewards.xianyu;
      }
      
      // 添加资源
      if (Object.keys(resourceRewards).length > 0) {
        game.gameStateManager.addResources(resourceRewards);
      }
      
      // 添加经验值（如果有处理经验的方法，应该使用它）
      if (this.rewards.exp) {
        // 获取玩家对象
        const player = game.gameStateManager.getPlayer();
        player.exp = (player.exp || 0) + this.rewards.exp;
        game.gameStateManager.setPlayer(player);
      }
      
      // 添加物品
      if (this.rewards.items && Array.isArray(this.rewards.items)) {
        this.rewards.items.forEach(item => {
          game.gameStateManager.addItem(item);
        });
      }
      
      // 添加装备
      if (this.rewards.equipments && Array.isArray(this.rewards.equipments)) {
        this.rewards.equipments.forEach(equipment => {
          game.gameStateManager.addEquipment(equipment);
        });
      }
      
      // 保存更改
      game.gameStateManager.saveGameState();
      
      console.log('奖励已添加到玩家账户', this.rewards);
    } catch (error) {
      console.error('添加奖励时出错:', error);
    }
  }
  
  /**
   * 关闭结算界面
   */
  closeResult() {
    // 清除自动关闭计时器
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    // 调用完成回调
    if (this.onComplete) {
      this.onComplete();
    }
  }
  
  /**
   * 处理触摸开始事件
   * @param {number} x 触摸x坐标
   * @param {number} y 触摸y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleTouchStart(x, y) {
    // 如果点击在弹窗外部，关闭结算界面
    const popupX = this.screenWidth * 0.1;
    const popupY = this.screenHeight * 0.2;
    const popupWidth = this.screenWidth * 0.8;
    const popupHeight = this.screenHeight * 0.6;
    
    if (x < popupX || x > popupX + popupWidth || y < popupY || y > popupY + popupHeight) {
      this.closeResult();
      return true;
    }
    
    return false;
  }
  
  /**
   * 场景隐藏回调
   */
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 清除自动关闭计时器
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  /**
   * 更新场景
   */
  updateScene() {
    // 更新动画
  }
  
  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制弹窗
    this.drawPopup();
    
    // 绘制战斗结果
    this.drawBattleResult();
    
    // 绘制奖励物品
    this.drawRewards();
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    // 使用半透明黑色覆盖
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  /**
   * 绘制弹窗
   */
  drawPopup() {
    const popupX = this.screenWidth * 0.1;
    const popupY = this.screenHeight * 0.2;
    const popupWidth = this.screenWidth * 0.8;
    const popupHeight = this.screenHeight * 0.6;
    
    // 弹窗背景
    const gradient = this.ctx.createLinearGradient(popupX, popupY, popupX, popupY + popupHeight);
    gradient.addColorStop(0, 'rgba(80, 80, 100, 0.9)');
    gradient.addColorStop(1, 'rgba(50, 50, 70, 0.9)');
    
    this.ctx.fillStyle = gradient;
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    
    // 绘制圆角矩形
    this.roundRect(popupX, popupY, popupWidth, popupHeight, 10, true, true);
    
    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(
      this.battleResult.victory ? '战斗胜利' : '战斗失败',
      this.screenWidth / 2,
      popupY + 30
    );
  }
  
  /**
   * 绘制战斗结果
   */
  drawBattleResult() {
    const popupX = this.screenWidth * 0.1;
    const popupY = this.screenHeight * 0.2;
    const popupWidth = this.screenWidth * 0.8;
    
    // 结果文本
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    const resultMessage = this.battleResult.victory
      ? '恭喜你战胜了敌人！'
      : '很遗憾，战斗失败了。';
    
    this.ctx.fillText(resultMessage, this.screenWidth / 2, popupY + 70);
    
    // 额外信息
    if (this.battleResult.turn) {
      this.ctx.font = '16px Arial';
      this.ctx.fillText(`回合数: ${this.battleResult.turn}`, this.screenWidth / 2, popupY + 100);
    }
  }
  
  /**
   * 绘制奖励物品
   */
  drawRewards() {
    // 只有胜利才显示奖励
    if (!this.battleResult.victory) {
      return;
    }
    
    const popupX = this.screenWidth * 0.1;
    const popupY = this.screenHeight * 0.2;
    const popupWidth = this.screenWidth * 0.8;
    
    // 奖励标题
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('获得奖励', this.screenWidth / 2, popupY + 130);
    
    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(popupX + 20, popupY + 150);
    this.ctx.lineTo(popupX + popupWidth - 20, popupY + 150);
    this.ctx.stroke();
    
    // 绘制奖励列表
    const startY = popupY + 170;
    const itemHeight = 30;
    const now = Date.now();
    
    let index = 0;
    
    // 绘制经验值
    if (this.rewards.exp) {
      this.drawRewardItem('经验值', this.rewards.exp, startY + index * itemHeight, 'exp');
      index++;
    }
    
    // 绘制灵石
    if (this.rewards.lingshi) {
      this.drawRewardItem('灵石', this.rewards.lingshi, startY + index * itemHeight, 'lingshi');
      index++;
    }
    
    // 绘制仙玉
    if (this.rewards.xianyu) {
      this.drawRewardItem('仙玉', this.rewards.xianyu, startY + index * itemHeight, 'xianyu');
      index++;
    }
    
    // 绘制物品
    if (this.rewards.items && this.rewards.items.length > 0) {
      this.rewards.items.forEach(item => {
        this.drawRewardItem(item.name, 1, startY + index * itemHeight, 'item');
        index++;
      });
    }
    
    // 绘制装备
    if (this.rewards.equipments && this.rewards.equipments.length > 0) {
      this.rewards.equipments.forEach(equipment => {
        this.drawRewardItem(equipment.name, 1, startY + index * itemHeight, 'equipment');
        index++;
      });
    }
  }
  
  /**
   * 绘制单个奖励项
   * @param {string} name 奖励名称
   * @param {number} amount 奖励数量
   * @param {number} y 绘制Y坐标
   * @param {string} type 奖励类型
   */
  drawRewardItem(name, amount, y, type) {
    const iconSize = 20;
    const iconX = this.screenWidth / 2 - 100;
    
    // 绘制图标
    this.drawRewardIcon(iconX, y, iconSize, type);
    
    // 绘制名称
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(name, iconX + iconSize + 10, y);
    
    // 绘制数量
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`+${amount}`, this.screenWidth / 2 + 100, y);
  }
  
  /**
   * 绘制奖励图标
   * @param {number} x 图标X坐标
   * @param {number} y 图标Y坐标
   * @param {number} size 图标大小
   * @param {string} type 奖励类型
   */
  drawRewardIcon(x, y, size, type) {
    // 尝试使用资源图片
    const iconKey = `icon${type.charAt(0).toUpperCase() + type.slice(1)}`;
    
    if (this.resources && this.resources[iconKey]) {
      this.ctx.drawImage(
        this.resources[iconKey],
        x,
        y - size / 2,
        size,
        size
      );
    } else {
      // 没有资源时使用简单图形
      this.ctx.fillStyle = this.getRewardColor(type);
      
      switch (type) {
        case 'exp':
          // 经验值使用星形
          this.drawStar(x + size / 2, y, size / 2);
          break;
        case 'lingshi':
          // 灵石使用方形
          this.ctx.fillRect(x, y - size / 2, size, size);
          break;
        case 'xianyu':
          // 仙玉使用圆形
          this.ctx.beginPath();
          this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
          this.ctx.fill();
          break;
        case 'item':
          // 物品使用三角形
          this.drawTriangle(x + size / 2, y, size / 2);
          break;
        case 'equipment':
          // 装备使用六边形
          this.drawHexagon(x + size / 2, y, size / 2);
          break;
        default:
          // 默认使用圆形
          this.ctx.beginPath();
          this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
          this.ctx.fill();
      }
    }
  }
  
  /**
   * 获取奖励类型对应的颜色
   * @param {string} type 奖励类型
   * @returns {string} 颜色值
   */
  getRewardColor(type) {
    switch (type) {
      case 'exp':
        return '#ffff00'; // 经验值黄色
      case 'lingshi':
        return '#c0c0c0'; // 灵石银色
      case 'xianyu':
        return '#00ffff'; // 仙玉青色
      case 'item':
        return '#00ff00'; // 物品绿色
      case 'equipment':
        return '#ff00ff'; // 装备紫色
      default:
        return '#ffffff'; // 默认白色
    }
  }
  
  /**
   * 绘制星形
   * @param {number} x 中心X坐标
   * @param {number} y 中心Y坐标
   * @param {number} radius 半径
   */
  drawStar(x, y, radius) {
    const spikes = 5;
    const outerRadius = radius;
    const innerRadius = radius / 2;
    
    this.ctx.beginPath();
    
    for (let i = 0; i < spikes * 2; i++) {
      const r = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = Math.PI / spikes * i;
      
      const currX = x + Math.cos(angle) * r;
      const currY = y + Math.sin(angle) * r;
      
      if (i === 0) {
        this.ctx.moveTo(currX, currY);
      } else {
        this.ctx.lineTo(currX, currY);
      }
    }
    
    this.ctx.closePath();
    this.ctx.fill();
  }
  
  /**
   * 绘制三角形
   * @param {number} x 中心X坐标
   * @param {number} y 中心Y坐标
   * @param {number} radius 半径
   */
  drawTriangle(x, y, radius) {
    this.ctx.beginPath();
    this.ctx.moveTo(x, y - radius);
    this.ctx.lineTo(x + radius * Math.cos(Math.PI / 6), y + radius * Math.sin(Math.PI / 6));
    this.ctx.lineTo(x - radius * Math.cos(Math.PI / 6), y + radius * Math.sin(Math.PI / 6));
    this.ctx.closePath();
    this.ctx.fill();
  }
  
  /**
   * 绘制六边形
   * @param {number} x 中心X坐标
   * @param {number} y 中心Y坐标
   * @param {number} radius 半径
   */
  drawHexagon(x, y, radius) {
    this.ctx.beginPath();
    
    for (let i = 0; i < 6; i++) {
      const angle = Math.PI * 2 / 6 * i;
      const currX = x + Math.cos(angle) * radius;
      const currY = y + Math.sin(angle) * radius;
      
      if (i === 0) {
        this.ctx.moveTo(currX, currY);
      } else {
        this.ctx.lineTo(currX, currY);
      }
    }
    
    this.ctx.closePath();
    this.ctx.fill();
  }
  
  /**
   * 绘制圆角矩形
   * @param {number} x 左上角X坐标
   * @param {number} y 左上角Y坐标
   * @param {number} width 宽度
   * @param {number} height 高度
   * @param {number} radius 圆角半径
   * @param {boolean} fill 是否填充
   * @param {boolean} stroke 是否描边
   */
  roundRect(x, y, width, height, radius, fill, stroke) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
    
    if (fill) {
      this.ctx.fill();
    }
    
    if (stroke) {
      this.ctx.stroke();
    }
  }
}

export default BattleResultScene; 