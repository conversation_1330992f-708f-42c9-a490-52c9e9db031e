/**
 * 丹房场景类
 * 用于炼制丹药的场所
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class DanfangScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 选中的炼丹师
    this.selectedAlchemist = null;
    
    // 选中的丹方
    this.selectedFormula = null;
    
    // 丹方列表
    this.formulas = [
      {
        id: 'lingliDan1',
        name: '一阶灵力丹',
        description: '提升灵力值的丹药',
        effect: { type: 'lingli', value: 100 },
        materials: [{ name: '一阶药材', count: 2 }],
        level: 1,
        successRate: 0.7
      },
      {
        id: 'breakthroughDan1',
        name: '一阶突破丹',
        description: '提升突破成功率的丹药',
        effect: { type: 'breakthrough', value: 0.2 },
        materials: [{ name: '一阶药材', count: 3 }],
        level: 1,
        successRate: 0.6
      }
    ];
  }
  
  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();
    
    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到洞府场景
        this.sceneManager.showScene('dongfu');
      }
    );
    
    this.addUIElement(this.backButton);
    
    // 创建选择丹师按钮
    const alchemistButtonWidth = 150;
    const alchemistButtonHeight = 50;
    
    this.selectAlchemistButton = new Button(
      this.ctx,
      (this.screenWidth - alchemistButtonWidth) / 2,
      this.screenHeight / 3 - alchemistButtonHeight / 2,
      alchemistButtonWidth,
      alchemistButtonHeight,
      '选择丹师',
      null,
      null,
      () => {
        this.showSelectAlchemistDialog();
      }
    );
    
    this.addUIElement(this.selectAlchemistButton);
    
    // 创建选择丹方按钮
    const formulaButtonWidth = 150;
    const formulaButtonHeight = 50;
    
    this.selectFormulaButton = new Button(
      this.ctx,
      (this.screenWidth - formulaButtonWidth) / 2,
      this.screenHeight / 2 - formulaButtonHeight / 2,
      formulaButtonWidth,
      formulaButtonHeight,
      '选择配方',
      null,
      null,
      () => {
        this.showSelectFormulaDialog();
      }
    );
    
    this.addUIElement(this.selectFormulaButton);
    
    // 创建炼丹按钮
    this.createRefinePillButton();
  }
  
  // 获取上次选择的丹师
  getLastSelectedAlchemist() {
    const player = game.gameStateManager.getPlayer();
    if (player && player.lastSelectedAlchemistId) {
      const characters = game.gameStateManager.getCharacters();
      return characters.find(char => char.id === player.lastSelectedAlchemistId);
    }
    return null;
  }
  
  // 保存选择的丹师
  saveSelectedAlchemist(alchemist) {
    if (!alchemist) return;
    
    const player = game.gameStateManager.getPlayer();
    if (player) {
      player.lastSelectedAlchemistId = alchemist.id;
      game.gameStateManager.setPlayer(player);
    }
  }
  
  // 创建炼丹按钮
  createRefinePillButton() {
    // 只有在选择了丹师和配方后才显示炼丹按钮
    if (!this.selectedAlchemist || !this.selectedFormula) return;
    
    const buttonWidth = 150;
    const buttonHeight = 50;
    
    // 检查材料是否足够
    const hasMaterials = this.checkMaterials();
    
    this.refinePillButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight * 0.7 - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      '炼制丹药',
      null,
      hasMaterials ? null : 'rgba(100, 100, 100, 0.7)',
      () => {
        if (hasMaterials) {
          this.refinePill();
        } else {
          wx.showToast({
            title: '材料不足',
            icon: 'none',
            duration: 2000
          });
        }
      }
    );
    
    this.addUIElement(this.refinePillButton);
  }
  
  // 显示选择丹师对话框
  showSelectAlchemistDialog() {
    const characters = game.gameStateManager.getCharacters();
    if (!characters || characters.length === 0) {
      wx.showToast({
        title: '没有可用的角色',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 创建角色列表用于选择
    const items = characters.map(char => ({
      name: `${char.name} (品质:${char.quality || 0})`,
      value: char
    }));
    
    wx.showActionSheet({
      itemList: items.map(item => item.name),
      success: res => {
        if (res.tapIndex >= 0 && res.tapIndex < items.length) {
          this.selectedAlchemist = items[res.tapIndex].value;
          
          // 保存选择的丹师
          this.saveSelectedAlchemist(this.selectedAlchemist);
          
          // 重新创建UI以显示炼丹按钮
          this.initUI();
        }
      }
    });
  }
  
  // 显示选择丹方对话框
  showSelectFormulaDialog() {
    if (!this.formulas || this.formulas.length === 0) {
      wx.showToast({
        title: '没有可用的丹方',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.showActionSheet({
      itemList: this.formulas.map(formula => formula.name),
      success: res => {
        if (res.tapIndex >= 0 && res.tapIndex < this.formulas.length) {
          this.selectedFormula = this.formulas[res.tapIndex];
          
          // 重新创建UI以显示炼丹按钮
          this.initUI();
        }
      }
    });
  }
  
  // 检查材料是否足够
  checkMaterials() {
    if (!this.selectedFormula) return false;
    
    const player = game.gameStateManager.getPlayer();
    const items = game.gameStateManager.getItems();
    
    // 检查每种材料是否足够
    for (const material of this.selectedFormula.materials) {
      const item = items.find(item => item.name === material.name);
      if (!item || item.count < material.count) {
        return false;
      }
    }
    
    return true;
  }
  
  // 炼制丹药
  refinePill() {
    if (!this.selectedAlchemist || !this.selectedFormula) return;
    
    // 消耗材料
    this.consumeMaterials();
    
    // 计算成功率
    const baseSuccessRate = this.selectedFormula.successRate || 0.5;
    const alchemistBonus = this.getAlchemistBonus(this.selectedAlchemist);
    const finalSuccessRate = Math.min(0.95, baseSuccessRate + alchemistBonus);
    
    // 判断是否成功
    const isSuccess = Math.random() <= finalSuccessRate;
    
    if (isSuccess) {
      // 炼丹成功，添加丹药到背包
      this.addPill();
      
      wx.showToast({
        title: '炼丹成功',
        icon: 'success',
        duration: 2000
      });
    } else {
      wx.showToast({
        title: '炼丹失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
  
  // 消耗材料
  consumeMaterials() {
    if (!this.selectedFormula) return;
    
    const items = game.gameStateManager.getItems();
    
    // 消耗每种材料
    for (const material of this.selectedFormula.materials) {
      const item = items.find(item => item.name === material.name);
      if (item && item.count >= material.count) {
        game.gameStateManager.removeItem(item.id, material.count);
      }
    }
  }
  
  // 获取炼丹师加成
  getAlchemistBonus(alchemist) {
    // 根据角色品质提供加成
    const quality = alchemist.quality || 0;
    return quality * 0.05; // 每级品质增加5%成功率
  }
  
  // 添加丹药到背包
  addPill() {
    if (!this.selectedFormula) return;
    
    const items = game.gameStateManager.getItems();
    
    // 检查是否已有同名丹药
    const existingPill = items.find(item => item.name === this.selectedFormula.name);
    
    if (existingPill) {
      // 已有同名丹药，增加数量
      existingPill.count += 1;
      game.gameStateManager.updateItem(existingPill.id, existingPill);
    } else {
      // 没有同名丹药，创建新丹药
      const newPill = {
        id: Date.now(), // 生成唯一ID
        name: this.selectedFormula.name,
        type: 'consumable',
        quality: this.selectedFormula.level || 0,
        count: 1,
        effect: this.selectedFormula.effect,
        description: this.selectedFormula.description
      };
      
      game.gameStateManager.addItem(newPill);
    }
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 尝试恢复上次选择的丹师
    if (!this.selectedAlchemist) {
      this.selectedAlchemist = this.getLastSelectedAlchemist();
    }
    
    // 初始化UI
    this.initUI();
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 仅清空丹方选择，保留丹师选择
    this.selectedFormula = null;
  }
  
  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制丹房信息
    this.drawDanfangInfo();
    
    // 绘制选择信息
    this.drawSelectionInfo();
  }
  
  // 绘制背景
  drawBackground() {
    // 使用纯白色背景
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制淡灰色格子背景
    this.ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
    this.ctx.lineWidth = 1;
    
    // 绘制水平线
    const gridSize = 40;
    for (let y = 0; y <= this.screenHeight; y += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(this.screenWidth, y);
      this.ctx.stroke();
    }
    
    // 绘制垂直线
    for (let x = 0; x <= this.screenWidth; x += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }
  }
  
  // 绘制标题
  drawTitle() {
    this.ctx.fillStyle = '#333333'; // 深灰色文字
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('丹房', this.screenWidth / 2, 80);
  }
  
  // 绘制丹房信息
  drawDanfangInfo() {
    this.ctx.fillStyle = '#555555'; // 中灰色文字
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('在丹房可以炼制各种丹药', this.screenWidth / 2, 120);
    this.ctx.fillText('选择丹师和丹方开始炼制', this.screenWidth / 2, 150);
  }
  
  // 绘制选择信息
  drawSelectionInfo() {
    this.ctx.fillStyle = '#333333'; // 深灰色文字
    this.ctx.font = '18px Arial';
    this.ctx.textAlign = 'center';
    
    // 绘制丹师信息
    const alchemistText = this.selectedAlchemist 
      ? `丹师: ${this.selectedAlchemist.name}`
      : '未选择丹师';
    
    this.ctx.fillText(alchemistText, this.screenWidth / 2, this.screenHeight * 0.4);
    
    // 绘制丹方信息
    const formulaText = this.selectedFormula
      ? `丹方: ${this.selectedFormula.name}`
      : '未选择丹方';
    
    this.ctx.fillText(formulaText, this.screenWidth / 2, this.screenHeight * 0.45);
    
    // 如果选择了丹方，显示材料需求
    if (this.selectedFormula) {
      this.ctx.font = '16px Arial';
      this.ctx.fillText('所需材料:', this.screenWidth / 2, this.screenHeight * 0.5);
      
      let yOffset = 0.55;
      for (const material of this.selectedFormula.materials) {
        const items = game.gameStateManager.getItems();
        const item = items.find(item => item.name === material.name);
        const count = item ? item.count : 0;
        const text = `${material.name} x${material.count} (拥有: ${count})`;
        const color = count >= material.count ? '#4CAF50' : '#E57373'; // 绿色或红色
        
        this.ctx.fillStyle = color;
        this.ctx.fillText(text, this.screenWidth / 2, this.screenHeight * yOffset);
        yOffset += 0.05;
      }
    }
  }
}

// 修改导出方式为CommonJS
module.exports = DanfangScene; 