/**
 * 对话框组件
 */
import <PERSON><PERSON> from './Button';

class Dialog {
  constructor(ctx, screenWidth, screenHeight, title, content, buttons, width, height) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.title = title;
    this.content = content;
    this.buttonConfigs = buttons || [];
    
    // 对话框尺寸
    this.width = width || Math.min(400, this.screenWidth * 0.8);
    this.height = height || Math.min(300, this.screenHeight * 0.6);
    
    // 对话框位置
    this.x = (this.screenWidth - this.width) / 2;
    this.y = (this.screenHeight - this.height) / 2;
    
    // 对话框状态
    this.visible = false;
    
    // 创建按钮
    this.buttons = [];
    this.initButtons();
  }
  
  // 初始化按钮
  initButtons() {
    const buttonWidth = 120;
    const buttonHeight = 40;
    const buttonMargin = 20;
    const totalButtonWidth = this.buttonConfigs.length * buttonWidth + (this.buttonConfigs.length - 1) * buttonMargin;
    const startX = this.x + (this.width - totalButtonWidth) / 2;
    const buttonY = this.y + this.height - buttonHeight - 20;
    
    this.buttons = [];
    
    this.buttonConfigs.forEach((config, index) => {
      const buttonX = startX + (buttonWidth + buttonMargin) * index;
      const button = new Button(
        this.ctx,
        buttonX,
        buttonY,
        buttonWidth,
        buttonHeight,
        config.text,
        config.normalImg,
        config.pressedImg,
        () => {
          if (config.onClick) {
            config.onClick();
          }
          if (config.closeDialog) {
            this.hide();
          }
        }
      );
      this.buttons.push(button);
    });
  }
  
  // 显示对话框
  show() {
    this.visible = true;
  }
  
  // 隐藏对话框
  hide() {
    this.visible = false;
  }
  
  // 更新对话框内容
  updateContent(title, content) {
    this.title = title;
    this.content = content;
  }
  
  // 更新对话框尺寸
  updateSize(width, height) {
    this.width = width;
    this.height = height;
    this.x = (this.screenWidth - this.width) / 2;
    this.y = (this.screenHeight - this.height) / 2;
    this.initButtons();
  }
  
  // 绘制对话框
  render() {
    if (!this.visible) {
      return;
    }
    
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制对话框背景
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.9)';
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.roundRect(this.x, this.y, this.width, this.height, 10, true, true);
    
    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.title, this.x + this.width / 2, this.y + 40);
    
    // 绘制分割线
    this.ctx.beginPath();
    this.ctx.moveTo(this.x + 20, this.y + 60);
    this.ctx.lineTo(this.x + this.width - 20, this.y + 60);
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();
    
    // 绘制内容
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    
    // 如果内容是字符串，按行分割
    if (typeof this.content === 'string') {
      const contentLines = this.content.split('\n');
      contentLines.forEach((line, index) => {
        this.ctx.fillText(line, this.x + 30, this.y + 90 + index * 24);
      });
    }
    
    // 绘制按钮
    this.buttons.forEach(button => {
      button.render();
    });
  }
  
  isPointInside(x, y) {
    return x >= this.x && 
           x <= this.x + this.width &&
           y >= this.y && 
           y <= this.y + this.height;
  }
  // 触摸开始事件处理
  onTouchStart(x, y) {
    if (!this.visible) {
      return false;
    }
    
    console.log('Dialog onTouchStart', x, y);
    
    // 检查是否点击了任意按钮
    for (const button of this.buttons) {
      if (button.onTouchStart(x, y)) {
        console.log('Dialog button touched', button);
        return true;
      }
    }
    
    // 如果点击在对话框内，但不在按钮上，仍然拦截事件
    if (this.isPointInside(x, y)) {
      console.log('Dialog background touched');
      return true;
    }
    
    return false;
  }
  
  // 触摸结束事件处理
  onTouchEnd(x, y) {
    if (!this.visible) {
      return false;
    }
    
    console.log('Dialog onTouchEnd', x, y);
    
    // 检查是否点击了任意按钮
    for (const button of this.buttons) {
      if (button.onTouchEnd(x, y)) {
        console.log('Dialog button clicked', button);
        return true;
      }
    }
    
    // 如果点击在对话框内，但不在按钮上，仍然拦截事件
    if (this.isPointInside(x, y)) {
      console.log('Dialog background clicked');
      return true;
    }
    
    return false;
  }
  
  // 绘制圆角矩形
  roundRect(x, y, width, height, radius, fill, stroke) {
    if (typeof radius === 'number') {
      radius = { tl: radius, tr: radius, br: radius, bl: radius };
    } else {
      radius = { ...{ tl: 0, tr: 0, br: 0, bl: 0 }, ...radius };
    }
    
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius.tl, y);
    this.ctx.lineTo(x + width - radius.tr, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
    this.ctx.lineTo(x + width, y + height - radius.br);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
    this.ctx.lineTo(x + radius.bl, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
    this.ctx.lineTo(x, y + radius.tl);
    this.ctx.quadraticCurveTo(x, y, x + radius.tl, y);
    this.ctx.closePath();
    
    if (fill) {
      this.ctx.fill();
    }
    
    if (stroke) {
      this.ctx.stroke();
    }
  }
}

export default Dialog; 