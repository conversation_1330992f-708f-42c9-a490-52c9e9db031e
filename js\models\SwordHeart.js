/**
 * 剑心模型类
 * 表示角色可以获得的剑心，提供不同的属性加成
 */
import { SWORD_HEART_ADVANCEMENT_BONUS } from '../config/SwordHeartConfig.js';

class SwordHeart {
  constructor({
    id,
    name,
    description,
    level = 1,
    maxLevel = 10,
    unlocked = false,
    attributes = {},
    levelUpAttributes = {}, // 每级提升的属性
    advancementLevel = 0,
    maxAdvancementLevel = 9, // 最高9阶
    swordIntent = 0, // 剑意值，用于升级
    requiredSwordIntent = 100, // 升级所需剑意值
    advancementRequirements = [], // 进阶所需材料
    icon = null, // 剑心图标
    color = '#4299e1' // 剑心颜色
  }) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.level = level;
    this.maxLevel = maxLevel;
    this.unlocked = unlocked;
    this.attributes = attributes || {};
    this.levelUpAttributes = levelUpAttributes || {};
    this.advancementLevel = advancementLevel;
    this.maxAdvancementLevel = maxAdvancementLevel;
    this.swordIntent = swordIntent;
    this.requiredSwordIntent = requiredSwordIntent;
    this.advancementRequirements = advancementRequirements || [];
    this.icon = icon;
    this.color = color;
  }

  /**
   * 获取当前剑心提供的属性加成
   * @returns {Object} 属性加成对象
   */
  getAttributeBonus() {
    // 获取进阶等级对应的加成倍率
    const advancementBonus = SWORD_HEART_ADVANCEMENT_BONUS[this.advancementLevel] || SWORD_HEART_ADVANCEMENT_BONUS[0];
    const advancementMultiplier = advancementBonus.multiplier;

    const result = {};

    // 计算基础属性加成
    for (const [key, baseValue] of Object.entries(this.attributes)) {
      // 获取每级提升的属性值
      const levelUpValue = this.levelUpAttributes[key] || 0;

      // 计算当前等级的总属性值：基础值 + (等级-1) * 每级提升值
      const levelValue = baseValue + (this.level - 1) * levelUpValue;

      // 应用进阶加成倍率
      result[key] = levelValue * advancementMultiplier;
    }

    return result;
  }

  /**
   * 获取下一级所需的剑意值
   * @returns {number} 所需剑意值
   */
  getNextLevelRequirement() {
    return this.requiredSwordIntent * (this.level + 1);
  }

  /**
   * 提升剑心等级
   * @param {number} swordIntent 要添加的剑意值
   * @returns {boolean} 是否成功提升等级
   */
  levelUp(swordIntent) {
    if (this.level >= this.maxLevel) {
      return false;
    }

    this.swordIntent += swordIntent;

    // 检查是否可以升级
    const required = this.getNextLevelRequirement();
    if (this.swordIntent >= required) {
      this.level += 1;
      this.swordIntent -= required;
      return true;
    }

    return false;
  }

  /**
   * 进阶剑心
   * @param {Array} materials 用于进阶的材料列表
   * @returns {boolean} 是否成功进阶
   */
  advance(materials) {
    if (this.advancementLevel >= this.maxAdvancementLevel) {
      return false;
    }

    // 检查材料是否足够
    const requirements = this.advancementRequirements[this.advancementLevel];
    if (!requirements) {
      return false;
    }

    // 检查每种所需材料
    for (const req of requirements) {
      const material = materials.find(m => m.id === req.id);
      if (!material || material.count < req.count) {
        return false;
      }
    }

    // 进阶成功
    this.advancementLevel += 1;
    return true;
  }

  /**
   * 将剑心数据转换为JSON格式
   * @returns {Object} JSON对象
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      level: this.level,
      maxLevel: this.maxLevel,
      unlocked: this.unlocked,
      attributes: { ...this.attributes },
      levelUpAttributes: { ...this.levelUpAttributes },
      advancementLevel: this.advancementLevel,
      maxAdvancementLevel: this.maxAdvancementLevel,
      swordIntent: this.swordIntent,
      requiredSwordIntent: this.requiredSwordIntent,
      advancementRequirements: [...this.advancementRequirements],
      icon: this.icon,
      color: this.color
    };
  }

  /**
   * 从JSON对象创建剑心实例
   * @param {Object} json JSON对象
   * @returns {SwordHeart} 剑心实例
   */
  static fromJSON(json) {
    return new SwordHeart(json);
  }

  /**
   * 获取当前进阶等级的描述
   * @returns {string} 进阶等级描述
   */
  getAdvancementDescription() {
    const advancementBonus = SWORD_HEART_ADVANCEMENT_BONUS[this.advancementLevel] || SWORD_HEART_ADVANCEMENT_BONUS[0];
    return advancementBonus.description;
  }
}

// 导出剑心模型类
export default SwordHeart;
