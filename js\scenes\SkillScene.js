/**
 * 功法界面场景类
 * 展示所有功法，允许管理功法
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import Skill from '../models/Skill';
import AppContext from '../utils/AppContext';
import SkillConfig from '../config/SkillConfig';

class SkillScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = null;

    // 功法数据按境界分组
    this.skillsByRealm = {};

    // 当前选中的功法
    this.selectedSkillId = null;
    this.selectedRealm = null;

    // 页面状态
    this.pageTitle = '功法修炼';

    // 滚动位置
    this.scrollY = 0;
    this.maxScrollY = 0;

    // 触摸状态
    this.touchStartY = 0;
    this.isDragging = false;
  }

  // 初始化UI
  initUI() {
    // 尝试获取资源
    this.resources = game.resourceLoader ? game.resourceLoader.resources : null;

    const margin = 20;

    // 创建返回按钮 - 放在左下角
    const backButtonSize = 60;

    this.backButton = new Button(
      this.ctx,
      margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回主场景
        this.sceneManager.showScene('main');
      }
    );

    this.addUIElement(this.backButton);
  }

  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("SkillScene onShow", params);
      this.visible = true;

      // 保存传入的参数
      this.params = params || {};

      // 从配置文件加载功法数据
      this.loadSkillData();

      // 初始化UI状态
      this.scrollY = 0;
      this.selectedSkillId = null;
      this.selectedRealm = null;

      // 初始化按钮
      this.initUI();
    } catch (error) {
      console.error("SkillScene.onShow 出错:", error);
      this.skillsByRealm = {};
    }
  }

  // 加载功法数据
  loadSkillData() {
    // 从配置文件获取功法数据
    this.skillsByRealm = {};

    for (const realm of SkillConfig.realmOrder) {
      const realmSkills = SkillConfig.getSkillsByRealm(realm);
      if (Object.keys(realmSkills).length > 0) {
        this.skillsByRealm[realm] = realmSkills;
      }
    }

    console.log('加载功法数据:', this.skillsByRealm);
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 设置场景为不可见
    this.visible = false;
  }

  // 处理触摸开始事件
  handleTouchStart(x, y) {
    this.touchStartY = y;
    this.isDragging = false;

    // 检查是否点击了功法卡片
    const headerHeight = 80;
    let currentY = headerHeight + 20 - this.scrollY;

    for (const realm of SkillConfig.realmOrder) {
      if (!this.skillsByRealm[realm]) continue;

      // 境界标题高度
      const realmTitleHeight = 40;
      currentY += realmTitleHeight + 10;

      // 功法卡片
      const skills = this.skillsByRealm[realm];
      const skillNames = Object.keys(skills);
      const cardsPerRow = 3;
      const cardWidth = (this.screenWidth - 60) / cardsPerRow;
      const cardHeight = 120;
      const margin = 10;

      for (let i = 0; i < skillNames.length; i++) {
        const row = Math.floor(i / cardsPerRow);
        const col = i % cardsPerRow;

        const cardX = 20 + col * (cardWidth + margin);
        const cardY = currentY + row * (cardHeight + margin);

        if (x >= cardX && x <= cardX + cardWidth &&
            y >= cardY && y <= cardY + cardHeight) {
          const skillName = skillNames[i];
          const skill = skills[skillName];
          this.selectedSkillId = skill.id;
          this.selectedRealm = realm;

          // 进入功法详情页面
          this.sceneManager.showScene('functionDetail', {
            skillId: skill.id,
            skillName: skill.name,
            realm: realm
          });
          return true;
        }
      }

      // 更新当前Y位置
      const rows = Math.ceil(skillNames.length / cardsPerRow);
      currentY += rows * (cardHeight + margin) + 20;
    }

    return false;
  }

  // 处理触摸移动事件
  handleTouchMove(x, y) {
    if (!this.isDragging && Math.abs(y - this.touchStartY) > 10) {
      this.isDragging = true;
    }

    if (this.isDragging) {
      const deltaY = y - this.touchStartY;
      this.scrollY = Math.max(0, Math.min(this.maxScrollY, this.scrollY - deltaY));
      this.touchStartY = y;
      return true;
    }

    return false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    this.isDragging = false;
    return false;
  }

  // 获取玩家功法等级
  getPlayerSkillLevel(skillId) {
    try {
      const player = game.gameStateManager.getPlayer();
      if (player && player.skills && player.skills[skillId]) {
        return player.skills[skillId].level || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取玩家功法等级失败:', error);
      return 0;
    }
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制功法列表
    this.drawSkillsByRealm();
  }

  // 绘制背景
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(20, 30, 60, 0.95)');
    gradient.addColorStop(1, 'rgba(10, 15, 30, 0.95)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景
    const headerGradient = this.ctx.createLinearGradient(0, 0, 0, headerHeight);
    headerGradient.addColorStop(0, 'rgba(40, 50, 80, 0.9)');
    headerGradient.addColorStop(1, 'rgba(20, 30, 50, 0.9)');
    this.ctx.fillStyle = headerGradient;
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题
    this.ctx.font = 'bold 28px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.pageTitle, this.screenWidth / 2, headerHeight / 2 + 10);

    // 绘制修炼点
    try {
      const player = game.gameStateManager.getPlayer();
      const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#90EE90';
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`修炼点: ${functionPoints}`, this.screenWidth - 20, headerHeight / 2 + 8);
    } catch (error) {
      console.error('绘制修炼点失败:', error);
    }
  }

  // 绘制按境界分组的功法列表
  drawSkillsByRealm() {
    const headerHeight = 80;
    let currentY = headerHeight + 20 - this.scrollY;
    let totalContentHeight = headerHeight + 20;

    // 没有功法时显示提示
    if (!this.skillsByRealm || Object.keys(this.skillsByRealm).length === 0) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('暂无功法', this.screenWidth / 2, currentY + 50);
      return;
    }

    // 遍历每个境界
    for (const realm of SkillConfig.realmOrder) {
      if (!this.skillsByRealm[realm]) continue;

      const skills = this.skillsByRealm[realm];
      const skillNames = Object.keys(skills);

      if (skillNames.length === 0) continue;

      // 绘制境界标题
      if (currentY > -40 && currentY < this.screenHeight + 40) {
        this.drawRealmTitle(realm, currentY);
      }

      const realmTitleHeight = 40;
      currentY += realmTitleHeight + 10;
      totalContentHeight += realmTitleHeight + 10;

      // 绘制功法卡片
      const cardsPerRow = 3;
      const cardWidth = (this.screenWidth - 60) / cardsPerRow;
      const cardHeight = 120;
      const margin = 10;

      for (let i = 0; i < skillNames.length; i++) {
        const row = Math.floor(i / cardsPerRow);
        const col = i % cardsPerRow;

        const cardX = 20 + col * (cardWidth + margin);
        const cardY = currentY + row * (cardHeight + margin);

        // 只绘制可见的卡片
        if (cardY > -cardHeight && cardY < this.screenHeight + cardHeight) {
          const skillName = skillNames[i];
          const skill = skills[skillName];
          this.drawSkillCard(skill, cardX, cardY, cardWidth, cardHeight);
        }
      }

      // 更新当前Y位置
      const rows = Math.ceil(skillNames.length / cardsPerRow);
      const sectionHeight = rows * (cardHeight + margin) + 20;
      currentY += sectionHeight;
      totalContentHeight += sectionHeight;
    }

    // 更新最大滚动距离
    this.maxScrollY = Math.max(0, totalContentHeight - this.screenHeight + 100);
  }

  // 绘制境界标题
  drawRealmTitle(realm, y) {
    // 绘制境界标题背景
    const titleHeight = 40;
    const gradient = this.ctx.createLinearGradient(0, y, 0, y + titleHeight);
    gradient.addColorStop(0, 'rgba(80, 60, 40, 0.8)');
    gradient.addColorStop(1, 'rgba(60, 40, 20, 0.8)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(20, y, this.screenWidth - 40, titleHeight);

    // 绘制境界标题边框
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(20, y, this.screenWidth - 40, titleHeight);

    // 绘制境界标题文字
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`===== ${realm} =====`, this.screenWidth / 2, y + titleHeight / 2 + 7);
  }

  // 绘制功法卡片
  drawSkillCard(skill, x, y, width, height) {
    // 获取玩家功法等级
    const playerLevel = this.getPlayerSkillLevel(skill.id);

    // 绘制卡片背景
    const cardGradient = this.ctx.createLinearGradient(x, y, x, y + height);
    if (playerLevel > 0) {
      // 已学习的功法使用金色渐变
      cardGradient.addColorStop(0, 'rgba(80, 70, 40, 0.9)');
      cardGradient.addColorStop(1, 'rgba(60, 50, 20, 0.9)');
    } else {
      // 未学习的功法使用灰色渐变
      cardGradient.addColorStop(0, 'rgba(60, 60, 60, 0.9)');
      cardGradient.addColorStop(1, 'rgba(40, 40, 40, 0.9)');
    }
    this.ctx.fillStyle = cardGradient;
    this.ctx.fillRect(x, y, width, height);

    // 绘制卡片边框
    this.ctx.strokeStyle = playerLevel > 0 ? '#FFD700' : '#888888';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制功法名称
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = playerLevel > 0 ? '#FFD700' : '#CCCCCC';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(skill.name, x + width / 2, y + 25);

    // 绘制功法等级
    if (playerLevel > 0) {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#90EE90';
      this.ctx.fillText(`等级: ${playerLevel}`, x + width / 2, y + 45);
    } else {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#FF6666';
      this.ctx.fillText('未学习', x + width / 2, y + 45);
    }

    // 绘制功法描述
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    const description = skill.description.length > 20 ? skill.description.substring(0, 20) + '...' : skill.description;
    this.ctx.fillText(description, x + width / 2, y + 65);

    // 绘制进度条（如果已学习）
    if (playerLevel > 0) {
      const progressBarWidth = width - 20;
      const progressBarHeight = 8;
      const progressBarX = x + 10;
      const progressBarY = y + height - 25;

      // 背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

      // 进度
      const progress = playerLevel / 10; // 假设最大等级为10
      this.ctx.fillStyle = '#00FF00';
      this.ctx.fillRect(progressBarX, progressBarY, progressBarWidth * progress, progressBarHeight);

      // 边框
      this.ctx.strokeStyle = '#FFFFFF';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);
    }
  }
}

export default SkillScene;