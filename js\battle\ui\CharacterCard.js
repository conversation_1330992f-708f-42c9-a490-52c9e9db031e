/**
 * 角色卡片组件
 * 用于显示角色头像和基本信息
 */
class CharacterCard {
  /**
   * 构造函数
   * @param {CanvasRenderingContext2D} ctx 画布上下文
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {number} width 宽度
   * @param {number} height 高度
   * @param {Object} character 角色数据
   * @param {Object} resources 资源
   * @param {Function} onClick 点击回调
   */
  constructor(ctx, x, y, width, height, character, resources, onClick) {
    this.ctx = ctx;
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.character = character;
    this.resources = resources;
    this.onClick = onClick;
    
    // 选中状态
    this.selected = false;
    
    // 鼠标悬停状态
    this.hovered = false;
  }
  
  /**
   * 设置角色数据
   * @param {Object} character 角色数据
   */
  setCharacter(character) {
    this.character = character;
  }
  
  /**
   * 设置选中状态
   * @param {boolean} selected 是否选中
   */
  setSelected(selected) {
    this.selected = selected;
  }
  
  /**
   * 处理点击事件
   * @param {number} x 点击X坐标
   * @param {number} y 点击Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleClick(x, y) {
    if (this.isPointInside(x, y)) {
      if (this.character && this.onClick) {
        this.onClick(this.character, !this.selected);
      }
      return true;
    }
    return false;
  }
  
  /**
   * 处理触摸开始事件
   * @param {number} x 触摸X坐标
   * @param {number} y 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchStart(x, y) {
    if (this.isPointInside(x, y)) {
      // 记录拖拽起始位置
      this.dragStartX = x;
      this.dragStartY = y;
      this.isDragging = true;
      this.originalX = this.x;
      this.originalY = this.y;
      
      // 发送拖拽开始事件
      if (this.onDragStart) {
        this.onDragStart(this, x, y);
      }
      
      return true;
    }
    return false;
  }
  
  /**
   * 处理触摸移动事件
   * @param {number} x 触摸X坐标
   * @param {number} y 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchMove(x, y) {
    if (this.isDragging) {
      // 计算偏移量并更新位置
      const dx = x - this.dragStartX;
      const dy = y - this.dragStartY;
      
      this.x = this.originalX + dx;
      this.y = this.originalY + dy;
      
      // 发送拖拽移动事件
      if (this.onDrag) {
        this.onDrag(this, x, y, dx, dy);
      }
      
      return true;
    }
    
    return this.handleMouseMove(x, y);
  }
  
  /**
   * 处理触摸结束事件
   * @param {number} x 触摸X坐标
   * @param {number} y 触摸Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchEnd(x, y) {
    if (this.isDragging) {
      // 重置拖拽状态
      this.isDragging = false;
      
      // 发送拖拽结束事件
      if (this.onDragEnd) {
        this.onDragEnd(this, x, y);
      }
      
      return true;
    }
    return false;
  }
  
  /**
   * 设置拖拽开始回调
   * @param {Function} callback 回调函数
   */
  setOnDragStart(callback) {
    this.onDragStart = callback;
  }
  
  /**
   * 设置拖拽中回调
   * @param {Function} callback 回调函数
   */
  setOnDrag(callback) {
    this.onDrag = callback;
  }
  
  /**
   * 设置拖拽结束回调
   * @param {Function} callback 回调函数
   */
  setOnDragEnd(callback) {
    this.onDragEnd = callback;
  }
  
  /**
   * 重置位置到原始位置
   */
  resetPosition() {
    this.x = this.originalX;
    this.y = this.originalY;
  }
  
  /**
   * 处理鼠标移动事件
   * @param {number} x 鼠标X坐标
   * @param {number} y 鼠标Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleMouseMove(x, y) {
    const wasHovered = this.hovered;
    this.hovered = this.isPointInside(x, y);
    
    // 如果悬停状态改变，返回true以触发重绘
    return wasHovered !== this.hovered;
  }
  
  /**
   * 检查点是否在组件内部
   * @param {number} x 点的X坐标
   * @param {number} y 点的Y坐标
   * @returns {boolean} 是否在组件内
   */
  isPointInside(x, y) {
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.height;
  }
  
  /**
   * 实现BaseScene UI元素所需的render方法
   * 委托给draw方法
   */
  render() {
    this.draw();
  }
  
  /**
   * 绘制组件
   */
  draw() {
    // 绘制卡片背景
    this.drawBackground();
    
    // 绘制角色信息
    if (this.character) {
      this.drawCharacter();
    } else {
      this.drawEmptySlot();
    }
  }
  
  /**
   * 绘制卡片背景
   */
  drawBackground() {
    // 根据选中状态设置不同的背景颜色
    if (this.selected) {
      this.ctx.fillStyle = 'rgba(0, 128, 255, 0.5)';
    } else if (this.hovered) {
      this.ctx.fillStyle = 'rgba(100, 100, 100, 0.7)';
    } else {
      this.ctx.fillStyle = 'rgba(50, 50, 50, 0.5)';
    }
    
    // 绘制卡片背景
    this.roundRect(this.x, this.y, this.width, this.height, 5, true, false);
    
    // 绘制卡片边框
    this.ctx.strokeStyle = this.selected ? '#00aaff' : (this.hovered ? '#aaaaaa' : '#666666');
    this.ctx.lineWidth = this.selected ? 2 : 1;
    this.roundRect(this.x, this.y, this.width, this.height, 5, false, true);
  }
  
  /**
   * 绘制角色信息
   */
  drawCharacter() {
    const padding = 5;
    const avatarSize = this.width - padding * 2;
    const avatarX = this.x + padding;
    const avatarY = this.y + padding;
    
    // 绘制角色头像
    if (this.resources && this.resources[`character${this.character.id}`]) {
      // 尝试使用角色特定的图像
      try {
        this.ctx.drawImage(
          this.resources[`character${this.character.id}`],
          avatarX,
          avatarY,
          avatarSize,
          avatarSize
        );
      } catch (error) {
        console.warn(`无法绘制角色${this.character.id}的图像:`, error);
        // 如果特定图像失败，绘制颜色块代表角色
        this.drawColorAvatar(avatarX, avatarY, avatarSize);
      }
    } else {
      // 如果没有头像资源，绘制颜色块代表角色
      this.drawColorAvatar(avatarX, avatarY, avatarSize);
    }
    
    // 绘制角色名称
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    
    // 最多显示6个字符，超出部分用...代替
    let name = this.character.name;
    if (name && name.length > 6) {
      name = name.substring(0, 5) + '...';
    } else if (!name) {
      name = "未命名";
    }
    
    this.ctx.fillText(name, this.x + this.width / 2, this.y + avatarSize + padding * 2);
    
    // 绘制等级
    this.ctx.font = 'bold 10px Arial';
    this.ctx.fillStyle = '#ffff00';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`Lv.${this.character.level || 1}`, this.x + padding, this.y + padding);
    
    // 绘制战力
    this.ctx.font = '10px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`战力:${this.character.combatPower || this.character.power || 0}`, this.x + this.width / 2, this.y + this.height - padding - 10);
  }
  
  /**
   * 绘制颜色块头像
   * @param {number} x 左上角X坐标
   * @param {number} y 左上角Y坐标
   * @param {number} size 大小
   */
  drawColorAvatar(x, y, size) {
    // 使用角色ID生成颜色
    const id = this.character.id || Math.floor(Math.random() * 100);
    
    // 根据ID生成不同的颜色
    const hue = (id * 53) % 360; // 使用质数53来获得更好的分散度
    
    // 玩家角色使用更鲜艳的颜色
    const saturation = id < 100 ? '70%' : '50%';
    const lightness = id < 100 ? '50%' : '40%';
    
    const color = `hsl(${hue}, ${saturation}, ${lightness})`;
    
    // 绘制色块
    this.ctx.fillStyle = color;
    this.ctx.fillRect(x, y, size, size);
    
    // 添加简单的图案
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.beginPath();
    this.ctx.arc(x + size/2, y + size/2, size/4, 0, Math.PI * 2);
    this.ctx.fill();
    
    // 在色块上添加角色首字母或ID
    const text = this.character.name ? this.character.name.charAt(0) : id;
    
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = 'white';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, x + size/2, y + size/2);
  }
  
  /**
   * 绘制空槽位
   */
  drawEmptySlot() {
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    const size = Math.min(this.width, this.height) * 0.4;
    
    // 绘制加号
    this.ctx.strokeStyle = '#cccccc';
    this.ctx.lineWidth = 2;
    
    // 横线
    this.ctx.beginPath();
    this.ctx.moveTo(centerX - size / 2, centerY);
    this.ctx.lineTo(centerX + size / 2, centerY);
    this.ctx.stroke();
    
    // 竖线
    this.ctx.beginPath();
    this.ctx.moveTo(centerX, centerY - size / 2);
    this.ctx.lineTo(centerX, centerY + size / 2);
    this.ctx.stroke();
    
    // 绘制提示文本
    this.ctx.font = '10px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('空槽位', centerX, centerY + size / 2 + 5);
  }
  
  /**
   * 绘制圆角矩形
   * @param {number} x 左上角X坐标
   * @param {number} y 左上角Y坐标
   * @param {number} width 宽度
   * @param {number} height 高度
   * @param {number} radius 圆角半径
   * @param {boolean} fill 是否填充
   * @param {boolean} stroke 是否描边
   */
  roundRect(x, y, width, height, radius, fill, stroke) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
    
    if (fill) {
      this.ctx.fill();
    }
    
    if (stroke) {
      this.ctx.stroke();
    }
  }
}

export default CharacterCard; 