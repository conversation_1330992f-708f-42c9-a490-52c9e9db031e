/**
 * 竞技场管理器
 * 负责管理PVP竞技场的排名、对手生成和战斗逻辑
 */
import EventEmitter from '../utils/EventEmitter.js';
import { generateNpcOpponent, ARENA_DAILY_REWARDS } from '../config/ArenaConfig.js';
import game from '../../game.js';

class ArenaManager extends EventEmitter {
  constructor() {
    super();
    
    // 玩家当前排名
    this.playerRank = 1500;
    
    // 玩家历史最高排名
    this.playerHighestRank = 1500;
    
    // 今日已挑战次数
    this.challengesUsed = 0;
    
    // 每日最大挑战次数
    this.maxChallenges = 10;
    
    // 上次重置时间
    this.lastResetTime = null;
    
    // 上次领取奖励时间
    this.lastRewardTime = null;
    
    // 竞技场对手列表（包括玩家镜像）
    this.opponents = {};
    
    // 初始化
    this.init();
  }
  
  /**
   * 初始化竞技场管理器
   */
  init() {
    // 检查是否需要重置每日挑战次数
    this.checkDailyReset();
  }
  
  /**
   * 检查是否需要重置每日挑战次数
   */
  checkDailyReset() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    
    if (!this.lastResetTime || this.lastResetTime < today) {
      // 重置每日挑战次数
      this.challengesUsed = 0;
      this.lastResetTime = today;
      
      // 触发事件
      this.emit('dailyReset');
    }
  }
  
  /**
   * 获取玩家当前排名
   * @returns {number} 玩家排名
   */
  getPlayerRank() {
    return this.playerRank;
  }
  
  /**
   * 获取玩家历史最高排名
   * @returns {number} 玩家历史最高排名
   */
  getPlayerHighestRank() {
    return this.playerHighestRank;
  }
  
  /**
   * 获取今日剩余挑战次数
   * @returns {number} 剩余挑战次数
   */
  getRemainingChallenges() {
    this.checkDailyReset();
    return Math.max(0, this.maxChallenges - this.challengesUsed);
  }
  
  /**
   * 获取可挑战的对手列表
   * @returns {Array} 对手列表
   */
  getOpponents() {
    // 检查每日重置
    this.checkDailyReset();
    
    // 生成三个可挑战的对手
    const opponents = [];
    
    // 1. 排名在玩家前面的对手（更强）
    const higherRank = Math.max(1, this.playerRank - Math.floor(Math.random() * 10) - 1);
    
    // 2. 排名接近玩家的对手
    const similarRank = Math.max(1, this.playerRank - Math.floor(Math.random() * 5) + Math.floor(Math.random() * 5));
    
    // 3. 排名在玩家后面的对手（更弱）
    const lowerRank = Math.max(1, this.playerRank + Math.floor(Math.random() * 10) + 1);
    
    // 检查是否有玩家镜像，否则生成NPC
    opponents.push(this.getOpponentByRank(higherRank));
    opponents.push(this.getOpponentByRank(similarRank));
    opponents.push(this.getOpponentByRank(lowerRank));
    
    return opponents;
  }
  
  /**
   * 根据排名获取对手
   * @param {number} rank 排名
   * @returns {Object} 对手信息
   */
  getOpponentByRank(rank) {
    // 检查是否有玩家镜像
    if (this.opponents[rank] && !this.opponents[rank].isNpc) {
      return this.opponents[rank];
    }
    
    // 如果没有玩家镜像或者是NPC，生成/返回NPC对手
    if (!this.opponents[rank]) {
      this.opponents[rank] = generateNpcOpponent(rank);
    }
    
    return this.opponents[rank];
  }
  
  /**
   * 挑战对手
   * @param {number} opponentRank 对手排名
   * @returns {Object} 战斗结果
   */
  challengeOpponent(opponentRank) {
    // 检查每日重置
    this.checkDailyReset();
    
    // 检查剩余挑战次数
    if (this.getRemainingChallenges() <= 0) {
      return {
        success: false,
        message: '今日挑战次数已用完'
      };
    }
    
    // 获取对手
    const opponent = this.getOpponentByRank(opponentRank);
    if (!opponent) {
      return {
        success: false,
        message: '找不到该对手'
      };
    }
    
    // 获取玩家角色
    const mainCharacter = this.getMainCharacter();
    if (!mainCharacter) {
      return {
        success: false,
        message: '找不到主角色'
      };
    }
    
    // 增加已使用挑战次数
    this.challengesUsed++;
    
    // 计算战斗结果
    const playerPower = mainCharacter.power;
    const opponentPower = opponent.power;
    
    // 简单的战力比较，实际游戏中可能需要更复杂的战斗逻辑
    const playerWin = playerPower > opponentPower;
    
    // 如果玩家获胜且对手排名比玩家高，交换排名
    if (playerWin && opponentRank < this.playerRank) {
      // 保存旧排名
      const oldRank = this.playerRank;
      
      // 更新排名
      this.playerRank = opponentRank;
      
      // 更新历史最高排名
      if (this.playerRank < this.playerHighestRank) {
        this.playerHighestRank = this.playerRank;
      }
      
      // 创建玩家镜像在旧排名位置
      this.createPlayerMirror(oldRank);
      
      // 移动对手到玩家的旧排名
      this.opponents[oldRank] = opponent;
      this.opponents[oldRank].rank = oldRank;
    }
    
    // 返回战斗结果
    return {
      success: true,
      playerWin,
      playerPower,
      opponentPower,
      opponent,
      newRank: this.playerRank,
      oldRank: playerWin && opponentRank < this.playerRank ? opponentRank : this.playerRank
    };
  }
  
  /**
   * 创建玩家镜像
   * @param {number} rank 排名
   */
  createPlayerMirror(rank) {
    const mainCharacter = this.getMainCharacter();
    if (!mainCharacter) return;
    
    // 创建玩家镜像
    this.opponents[rank] = {
      id: `player_mirror_${mainCharacter.id}`,
      name: `${mainCharacter.name}(玩家)`,
      power: mainCharacter.power,
      cultivation: mainCharacter.cultivation,
      rank,
      isNpc: false,
      isPlayerMirror: true
    };
  }
  
  /**
   * 获取主角色
   * @returns {Object} 主角色
   */
  getMainCharacter() {
    if (!game.gameStateManager) return null;
    
    const characters = game.gameStateManager.getCharacters();
    if (!characters || characters.length === 0) return null;
    
    // 返回ID为1的角色(女剑仙)
    return characters.find(char => char.id === 1);
  }
  
  /**
   * 领取每日奖励
   * @returns {Object} 奖励结果
   */
  claimDailyReward() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    
    // 检查是否已领取今日奖励
    if (this.lastRewardTime && this.lastRewardTime >= today) {
      return {
        success: false,
        message: '今日已领取奖励'
      };
    }
    
    // 根据排名获取奖励
    const reward = this.getRewardByRank(this.playerRank);
    
    // 添加奖励到玩家账户
    if (game.gameStateManager) {
      game.gameStateManager.addResource('xianyu', reward.xianyu);
    }
    
    // 更新领奖时间
    this.lastRewardTime = now.getTime();
    
    // 返回奖励结果
    return {
      success: true,
      reward
    };
  }
  
  /**
   * 根据排名获取奖励
   * @param {number} rank 排名
   * @returns {Object} 奖励信息
   */
  getRewardByRank(rank) {
    for (const reward of ARENA_DAILY_REWARDS) {
      if (rank >= reward.minRank && rank <= reward.maxRank) {
        return reward;
      }
    }
    
    // 默认奖励
    return ARENA_DAILY_REWARDS[ARENA_DAILY_REWARDS.length - 1];
  }
  
  /**
   * 将竞技场数据转换为JSON格式
   * @returns {Object} JSON对象
   */
  toJSON() {
    return {
      playerRank: this.playerRank,
      playerHighestRank: this.playerHighestRank,
      challengesUsed: this.challengesUsed,
      maxChallenges: this.maxChallenges,
      lastResetTime: this.lastResetTime,
      lastRewardTime: this.lastRewardTime,
      opponents: this.opponents
    };
  }
  
  /**
   * 从JSON对象加载竞技场数据
   * @param {Object} json JSON对象
   */
  fromJSON(json) {
    if (!json) return;
    
    this.playerRank = json.playerRank || 1500;
    this.playerHighestRank = json.playerHighestRank || 1500;
    this.challengesUsed = json.challengesUsed || 0;
    this.maxChallenges = json.maxChallenges || 10;
    this.lastResetTime = json.lastResetTime || null;
    this.lastRewardTime = json.lastRewardTime || null;
    this.opponents = json.opponents || {};
    
    // 检查每日重置
    this.checkDailyReset();
  }
}

export default ArenaManager;
