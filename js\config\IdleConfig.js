/**
 * 挂机游历配置文件
 * 包含不同地点的配置数据
 */

const IdleConfig = {
  // 挂机地点配置
  locations: [
    {
      id: 'bamboo_forest',
      name: '竹林小径',
      level: 1,
      requiredLevel: 1, // 角色需要达到的等级
      description: '清幽的竹林，适合初学者修炼。偶有野兽出没，但实力不强。',

      // 基础收益（每小时）
      baseRewards: {
        lingshi: 100,
        herbs: 5,
        functionPoints: 2,
        beastMaterial1: 3,
        beastMaterial2: 0,
        lianlidian: 5
      },

      // 普通小怪属性
      normalEnemy: {
        name: '竹林野兔',
        hpBase: 100,
        attackBase: 10,
        defenseBase: 5,
        attackSpeed: 0
      },

      // 精英怪属性
      eliteEnemy: {
        name: '竹林狼王',
        hpMultiplier: 10,
        attackMultiplier: 1.5,
        defenseMultiplier: 1.3,
        attackSpeed: 20,
        skills: [
          {
            name: '撕咬',
            damage: 1.8,
            cooldown: 3000,
            description: '凶猛的撕咬攻击'
          }
        ]
      },

      // Boss属性
      bossEnemy: {
        name: '竹林妖王',
        hpMultiplier: 100,
        attackMultiplier: 2.0,
        defenseMultiplier: 1.5,
        attackSpeed: 30,
        skills: [
          {
            name: '妖风',
            damage: 2.5,
            cooldown: 5000,
            description: '召唤妖风攻击敌人'
          },
          {
            name: '治愈',
            type: 'heal',
            healAmount: 0.2, // 恢复20%血量
            cooldown: 8000,
            description: '恢复自身血量'
          }
        ]
      },

      // 击杀奖励（每个敌人的具体奖励）
      killRewards: {
        normal: {
          lingshi: 5,
          herbs: 0.2,
          functionPoints: 0.1,
          beastMaterial1: 0.1,
          beastMaterial2: 0,
          lianlidian: 0.2
        },
        elite: {
          lingshi: 25,
          herbs: 1,
          functionPoints: 0.5,
          beastMaterial1: 0.5,
          beastMaterial2: 0,
          lianlidian: 1
        },
        boss: {
          lingshi: 100,
          herbs: 5,
          functionPoints: 2,
          beastMaterial1: 2,
          beastMaterial2: 0,
          lianlidian: 5
        }
      }
    },

    {
      id: 'misty_valley',
      name: '迷雾山谷',
      level: 2,
      requiredLevel: 2,
      description: '常年被迷雾笼罩的山谷，灵气浓郁但危险重重。适合筑基期修士历练。',

      baseRewards: {
        lingshi: 200,
        herbs: 8,
        functionPoints: 4,
        beastMaterial1: 5,
        beastMaterial2: 2,
        lianlidian: 10
      },

      normalEnemy: {
        name: '迷雾蜘蛛',
        hpBase: 400,
        attackBase: 80,
        defenseBase: 30,
        attackSpeed: 10
      },

      eliteEnemy: {
        name: '迷雾毒蛛',
        hpMultiplier: 10,
        attackMultiplier: 1.6,
        defenseMultiplier: 1.4,
        attackSpeed: 25,
        skills: [
          {
            name: '毒液喷射',
            damage: 2.0,
            cooldown: 4000,
            description: '喷射毒液造成持续伤害'
          }
        ]
      },

      bossEnemy: {
        name: '迷雾蛛后',
        hpMultiplier: 100,
        attackMultiplier: 2.2,
        defenseMultiplier: 1.6,
        attackSpeed: 35,
        skills: [
          {
            name: '蛛网束缚',
            damage: 1.5,
            cooldown: 6000,
            description: '用蛛网束缚敌人'
          },
          {
            name: '召唤小蛛',
            type: 'summon',
            cooldown: 10000,
            description: '召唤小蜘蛛协助战斗'
          }
        ]
      },

      killRewards: {
        normal: {
          lingshi: 8,
          herbs: 0.3,
          functionPoints: 0.2,
          beastMaterial1: 0.2,
          beastMaterial2: 0.1,
          lianlidian: 0.3
        },
        elite: {
          lingshi: 40,
          herbs: 1.5,
          functionPoints: 0.8,
          beastMaterial1: 0.8,
          beastMaterial2: 0.3,
          lianlidian: 1.5
        },
        boss: {
          lingshi: 160,
          herbs: 8,
          functionPoints: 3,
          beastMaterial1: 3,
          beastMaterial2: 1,
          lianlidian: 8
        }
      }
    },

    {
      id: 'flame_canyon',
      name: '烈焰峡谷',
      level: 3,
      requiredLevel: 5,
      description: '炽热的峡谷，火元素浓郁。适合金丹期修士磨练意志。',

      baseRewards: {
        lingshi: 350,
        herbs: 12,
        functionPoints: 6,
        beastMaterial1: 8,
        beastMaterial2: 4,
        lianlidian: 15
      },

      normalEnemy: {
        name: '火焰蜥蜴',
        hpBase: 600,
        attackBase: 120,
        defenseBase: 45,
        attackSpeed: 15
      },

      eliteEnemy: {
        name: '烈焰巨蜥',
        hpMultiplier: 10,
        attackMultiplier: 1.7,
        defenseMultiplier: 1.5,
        attackSpeed: 30,
        skills: [
          {
            name: '火焰吐息',
            damage: 2.3,
            cooldown: 5000,
            description: '喷射炽热火焰'
          }
        ]
      },

      bossEnemy: {
        name: '火龙王',
        hpMultiplier: 100,
        attackMultiplier: 2.5,
        defenseMultiplier: 1.8,
        attackSpeed: 40,
        skills: [
          {
            name: '龙息',
            damage: 3.0,
            cooldown: 7000,
            description: '强力的龙息攻击'
          },
          {
            name: '火焰护盾',
            type: 'shield',
            shieldAmount: 0.3, // 减少30%伤害
            cooldown: 12000,
            description: '火焰护盾减少受到的伤害'
          }
        ]
      },

      killRewards: {
        normal: {
          lingshi: 12,
          herbs: 0.4,
          functionPoints: 0.3,
          beastMaterial1: 0.3,
          beastMaterial2: 0.15,
          lianlidian: 0.5
        },
        elite: {
          lingshi: 60,
          herbs: 2,
          functionPoints: 1.2,
          beastMaterial1: 1.2,
          beastMaterial2: 0.6,
          lianlidian: 2
        },
        boss: {
          lingshi: 240,
          herbs: 10,
          functionPoints: 5,
          beastMaterial1: 5,
          beastMaterial2: 2,
          lianlidian: 10
        }
      }
    },

    {
      id: 'ice_wasteland',
      name: '冰雪荒原',
      level: 4,
      requiredLevel: 10,
      description: '终年冰雪覆盖的荒原，寒气逼人。适合元婴期修士历练。',

      baseRewards: {
        lingshi: 500,
        herbs: 15,
        functionPoints: 8,
        beastMaterial1: 10,
        beastMaterial2: 6,
        lianlidian: 20
      },

      normalEnemy: {
        name: '冰霜狼',
        hpBase: 800,
        attackBase: 160,
        defenseBase: 60,
        attackSpeed: 20
      },

      eliteEnemy: {
        name: '冰霜巨狼',
        hpMultiplier: 10,
        attackMultiplier: 1.8,
        defenseMultiplier: 1.6,
        attackSpeed: 35,
        skills: [
          {
            name: '冰锥术',
            damage: 2.5,
            cooldown: 4500,
            description: '发射锋利的冰锥'
          }
        ]
      },

      bossEnemy: {
        name: '冰雪女王',
        hpMultiplier: 100,
        attackMultiplier: 2.8,
        defenseMultiplier: 2.0,
        attackSpeed: 45,
        skills: [
          {
            name: '暴风雪',
            damage: 3.5,
            cooldown: 8000,
            description: '召唤暴风雪攻击敌人'
          },
          {
            name: '冰封',
            type: 'freeze',
            duration: 3000, // 冰封3秒
            cooldown: 15000,
            description: '冰封敌人使其无法行动'
          }
        ]
      },

      killRewards: {
        normal: {
          lingshi: 15,
          herbs: 0.5,
          functionPoints: 0.4,
          beastMaterial1: 0.4,
          beastMaterial2: 0.2,
          lianlidian: 0.6
        },
        elite: {
          lingshi: 75,
          herbs: 2.5,
          functionPoints: 1.5,
          beastMaterial1: 1.5,
          beastMaterial2: 0.8,
          lianlidian: 2.5
        },
        boss: {
          lingshi: 300,
          herbs: 12,
          functionPoints: 6,
          beastMaterial1: 6,
          beastMaterial2: 3,
          lianlidian: 12
        }
      }
    },

    {
      id: 'void_abyss',
      name: '虚空深渊',
      level: 5,
      requiredLevel: 15,
      description: '神秘的虚空深渊，充满未知的危险。只有化神期以上修士才敢踏足。',

      baseRewards: {
        lingshi: 800,
        herbs: 20,
        functionPoints: 12,
        beastMaterial1: 15,
        beastMaterial2: 10,
        lianlidian: 30
      },

      normalEnemy: {
        name: '虚空魅影',
        hpBase: 1200,
        attackBase: 200,
        defenseBase: 80,
        attackSpeed: 25
      },

      eliteEnemy: {
        name: '虚空恶魔',
        hpMultiplier: 10,
        attackMultiplier: 2.0,
        defenseMultiplier: 1.8,
        attackSpeed: 40,
        skills: [
          {
            name: '虚空撕裂',
            damage: 3.0,
            cooldown: 6000,
            description: '撕裂空间造成巨大伤害'
          }
        ]
      },

      bossEnemy: {
        name: '虚空领主',
        hpMultiplier: 100,
        attackMultiplier: 3.0,
        defenseMultiplier: 2.2,
        attackSpeed: 50,
        skills: [
          {
            name: '虚空风暴',
            damage: 4.0,
            cooldown: 10000,
            description: '召唤虚空风暴'
          },
          {
            name: '时空扭曲',
            type: 'special',
            effect: 'slow', // 减慢敌人攻击速度
            cooldown: 18000,
            description: '扭曲时空减慢敌人速度'
          }
        ]
      },

      killRewards: {
        normal: {
          lingshi: 20,
          herbs: 0.6,
          functionPoints: 0.5,
          beastMaterial1: 0.5,
          beastMaterial2: 0.3,
          lianlidian: 0.8
        },
        elite: {
          lingshi: 100,
          herbs: 3,
          functionPoints: 2,
          beastMaterial1: 2,
          beastMaterial2: 1,
          lianlidian: 3
        },
        boss: {
          lingshi: 400,
          herbs: 15,
          functionPoints: 8,
          beastMaterial1: 8,
          beastMaterial2: 4,
          lianlidian: 15
        }
      }
    }
  ],

  /**
   * 根据角色等级获取推荐地点
   * @param {number} characterLevel 角色等级
   * @returns {Array} 可用地点列表
   */
  getAvailableLocations(characterLevel) {
    return this.locations.filter(location => characterLevel >= location.requiredLevel);
  },

  /**
   * 根据ID获取地点配置
   * @param {string} locationId 地点ID
   * @returns {Object} 地点配置
   */
  getLocationById(locationId) {
    return this.locations.find(location => location.id === locationId);
  }
};

export default IdleConfig;
