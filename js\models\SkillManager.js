/**
 * 功法管理器类
 * 负责功法的创建、管理和持久化
 */
import Skill from './Skill';
import SkillConfig from '../config/SkillConfig';

class SkillManager {
  constructor() {
    // 功法列表
    this.skills = {};

    // 功法碎片和升级材料
    this.fragments = {};
    this.materials = {};

    // 初始化默认功法
    this.initDefaultSkills();

    // 初始化功法碎片和升级材料
    this.initFragmentsAndMaterials();

    console.log('SkillManager初始化完成，功法数量:', Object.keys(this.skills).length);
  }

  /**
   * 初始化默认功法
   */
  initDefaultSkills() {
    // 普通攻击技能
    this.registerSkill({
      id: 'basic_sword_attack',
      name: '基础剑法',
      type: 'normalAttack',
      quality: 'common',
      description: '最基础的剑法攻击，角色移动到敌人身前进行攻击。',
      damage: 100,
      damageType: 'physical',
      targetType: 'enemy',
      animationType: 'melee',
      castTime: 500,
      cooldown: 0
    });

    this.registerSkill({
      id: 'flying_sword_attack',
      name: '御剑术',
      type: 'normalAttack',
      quality: 'uncommon',
      description: '操控飞剑攻击敌人，无需移动。',
      damage: 120,
      damageType: 'physical',
      targetType: 'enemy',
      animationType: 'ranged',
      castTime: 300,
      cooldown: 0
    });

    // 主动技能
    this.registerSkill({
      id: 'lightning_strike',
      name: '雷击术',
      type: 'activeSkill',
      quality: 'rare',
      description: '召唤雷电攻击敌人，造成大量伤害。',
      damage: 300,
      damageType: 'magical',
      targetType: 'enemy',
      animationType: 'magic',
      castTime: 1500,
      cooldown: 5000
    });

    this.registerSkill({
      id: 'heal_light',
      name: '治疗术',
      type: 'activeSkill',
      quality: 'common',
      description: '恢复自身生命值。',
      damage: -200, // 负数表示治疗
      damageType: 'magical',
      targetType: 'self',
      animationType: 'magic',
      castTime: 1000,
      cooldown: 8000
    });

    this.registerSkill({
      id: 'fire_ball',
      name: '火球术',
      type: 'activeSkill',
      quality: 'uncommon',
      description: '发射火球攻击敌人。',
      damage: 250,
      damageType: 'magical',
      targetType: 'enemy',
      animationType: 'ranged',
      castTime: 1200,
      cooldown: 4000
    });

    // 基础练气决（被动技能）
    this.registerSkill({
      id: 'basic_qi_skill',
      name: '基础练气决',
      type: 'passive',
      quality: 'common', // 凡级
      description: '修仙者入门级功法，提升基础属性。',
      attributes: {
        hp: 100,
        attack: 10,
        defense: 10
      }
    });

    // 混沌决
    this.registerSkill({
      id: 'chaos_skill',
      name: '混沌决',
      quality: 'mythic', // 神级
      description: '传说中的上古功法，能引动混沌之力，大幅提升修仙者属性。',
      attributes: {
        hpPercent: 200,
        attackPercent: 200
      },
      restriction: {
        minLevel: 9
      }
    });

    // 注册其他功法...
    this.registerSkill({
      id: 'golden_sun_skill',
      name: '金阳神功',
      quality: 'uncommon', // 灵级
      description: '吸收金阳之力，增强防御能力。',
      attributes: {
        hp: 50,
        defense: 20,
        defensePercent: 10
      }
    });

    this.registerSkill({
      id: 'azure_cloud_skill',
      name: '碧云心法',
      quality: 'rare', // 玄级
      description: '以云养气，以气化形，提升攻击与速度。',
      attributes: {
        attack: 25,
        speed: 15,
        critical: 5 // 增加5%暴击率
      }
    });

    this.registerSkill({
      id: 'thunder_dao_skill',
      name: '雷道真经',
      quality: 'epic', // 天级
      description: '感悟雷之大道，引动天地雷霆之力。',
      attributes: {
        attack: 40,
        attackPercent: 20,
        critDamage: 30 // 增加30%暴击伤害
      },
      restriction: {
        minLevel: 5
      }
    });

    this.registerSkill({
      id: 'immortal_heart_skill',
      name: '仙心诀',
      quality: 'legendary', // 仙级
      description: '修炼仙家心法，全面提升修仙者素质。',
      attributes: {
        hp: 150,
        hpPercent: 20,
        attack: 30,
        attackPercent: 15,
        defense: 25,
        defensePercent: 10,
        speed: 10
      },
      restriction: {
        minLevel: 7
      }
    });
  }

  /**
   * 注册新功法
   * @param {Object} skillData 功法数据
   * @returns {Skill} 功法实例
   */
  registerSkill(skillData) {
    const skill = new Skill(skillData);
    this.skills[skill.id] = skill;
    return skill;
  }

  /**
   * 获取所有功法
   * @returns {Array} 功法列表
   */
  getAllSkills() {
    return Object.values(this.skills);
  }

  /**
   * 按境界获取功法
   * @param {string} realm 境界名称
   * @returns {Object} 功法对象
   */
  getSkillsByRealm(realm) {
    return SkillConfig.getSkillsByRealm(realm);
  }

  /**
   * 获取所有境界的功法数据
   * @returns {Object} 按境界分组的功法数据
   */
  getAllSkillsByRealm() {
    const skillsByRealm = {};
    for (const realm of SkillConfig.realmOrder) {
      const realmSkills = SkillConfig.getSkillsByRealm(realm);
      if (Object.keys(realmSkills).length > 0) {
        skillsByRealm[realm] = realmSkills;
      }
    }
    return skillsByRealm;
  }

  /**
   * 通过ID获取功法
   * @param {string} id 功法ID
   * @returns {Skill} 功法实例
   */
  getSkillById(id) {
    return this.skills[id];
  }

  /**
   * 创建功法实例（深拷贝）
   * @param {string} id 功法ID
   * @returns {Skill} 新的功法实例
   */
  createSkill(id) {
    const template = this.getSkillById(id);
    if (!template) return null;

    // 深拷贝功法数据
    const data = JSON.parse(JSON.stringify(template.toJSON()));
    return new Skill(data);
  }

  /**
   * 根据品质获取功法
   * @param {string} quality 功法品质
   * @returns {Array} 功法列表
   */
  getSkillsByQuality(quality) {
    return Object.values(this.skills).filter(skill => skill.quality === quality);
  }

  /**
   * 根据等级获取功法
   * @param {number} minLevel 最低等级
   * @param {number} maxLevel 最高等级
   * @returns {Array} 功法列表
   */
  getSkillsByLevel(minLevel, maxLevel = Infinity) {
    return Object.values(this.skills).filter(skill => {
      return skill.level >= minLevel && skill.level <= maxLevel;
    });
  }

  /**
   * 初始化功法碎片和升级材料
   */
  initFragmentsAndMaterials() {
    // 功法碎片
    this.fragments = {
      common_fragment: { id: 'common_fragment', name: '凡级功法碎片', count: 100 },
      uncommon_fragment: { id: 'uncommon_fragment', name: '灵级功法碎片', count: 100 },
      rare_fragment: { id: 'rare_fragment', name: '玄级功法碎片', count: 100 },
      epic_fragment: { id: 'epic_fragment', name: '天级功法碎片', count: 100 },
      legendary_fragment: { id: 'legendary_fragment', name: '仙级功法碎片', count: 100 },
      mythic_fragment: { id: 'mythic_fragment', name: '神级功法碎片', count: 100 }
    };

    // 心法要义（用于升级功法）
    this.materials = {
      skill_essence: { id: 'skill_essence', name: '心法要义', count: 100 },
      advancement_crystal: { id: 'advancement_crystal', name: '进阶晶核', count: 50 }
    };

    // 初始化功法组合效果定义
    this.initSkillCombinations();
  }

  /**
   * 初始化功法组合效果
   */
  initSkillCombinations() {
    // 定义功法组合效果
    this.skillCombinations = {
      // 五行相生组合
      'five_elements_generation': {
        name: '五行相生',
        description: '金生水，水生木，木生火，火生土，土生金的五行相生组合',
        skills: ['metal_skill', 'water_skill', 'wood_skill', 'fire_skill', 'earth_skill'],
        powerBonus: 500,
        attributeBonus: {
          hp: 200,
          attack: 50,
          defense: 50,
          speed: 20
        }
      },
      // 阴阳平衡组合
      'yin_yang_balance': {
        name: '阴阳平衡',
        description: '阴阳功法相互配合，达到平衡状态',
        skills: ['yin_skill', 'yang_skill'],
        powerBonus: 300,
        attributeBonus: {
          critRate: 0.1,
          critDamage: 0.3
        }
      },
      // 基础组合：基础练气决 + 金阳神功
      'basic_golden': {
        name: '基础金阳',
        description: '基础练气决与金阳神功的组合，增强防御能力',
        skills: ['basic_qi_skill', 'golden_sun_skill'],
        powerBonus: 200,
        attributeBonus: {
          defense: 30,
          defenseBonus: 10
        }
      },
      // 雷云组合：雷道真经 + 碧云心法
      'thunder_cloud': {
        name: '雷云合一',
        description: '雷道真经与碧云心法的组合，提升速度与暴击伤害',
        skills: ['thunder_dao_skill', 'azure_cloud_skill'],
        powerBonus: 250,
        attributeBonus: {
          speed: 30,
          critDamage: 0.2
        }
      }
    };
  }

  /**
   * 获取功法升级所需材料
   * @param {Skill} skill 要升级的功法
   * @returns {Object} 所需材料和数量
   */
  getUpgradeMaterials(skill) {
    if (!skill) return null;

    const qualityMultiplier = {
      'common': 1,
      'uncommon': 2,
      'rare': 3,
      'epic': 5,
      'legendary': 8,
      'mythic': 12
    };

    // 基础消耗
    const baseCost = 10;
    // 当前等级影响
    const levelFactor = skill.level * 5;
    // 品质影响
    const qualityFactor = qualityMultiplier[skill.quality] || 1;

    // 计算心法要义消耗
    const essenceCost = Math.floor(baseCost + levelFactor) * qualityFactor;

    return {
      skill_essence: essenceCost
    };
  }

  /**
   * 获取功法升星所需碎片
   * @param {Skill} skill 要升星的功法
   * @returns {Object} 所需碎片和数量
   */
  getStarUpgradeMaterials(skill) {
    if (!skill) return null;

    // 星级影响
    const starFactor = skill.stars * 10;
    // 基础消耗
    const baseCost = 20;

    // 根据品质确定碎片类型
    const fragmentMap = {
      'common': 'common_fragment',
      'uncommon': 'uncommon_fragment',
      'rare': 'rare_fragment',
      'epic': 'epic_fragment',
      'legendary': 'legendary_fragment',
      'mythic': 'mythic_fragment'
    };

    const fragmentType = fragmentMap[skill.quality] || 'common_fragment';
    const fragmentCost = baseCost + starFactor;

    const result = {};
    result[fragmentType] = fragmentCost;

    return result;
  }

  /**
   * 获取玩家拥有的材料
   * @returns {Object} 玩家拥有的材料
   */
  getMaterials() {
    return { ...this.materials };
  }

  /**
   * 获取玩家拥有的碎片
   * @returns {Object} 玩家拥有的碎片
   */
  getFragments() {
    return { ...this.fragments };
  }

  /**
   * 消耗材料
   * @param {string} materialId 材料ID
   * @param {number} count 消耗数量
   * @returns {boolean} 是否消耗成功
   */
  consumeMaterial(materialId, count) {
    if (!this.materials[materialId] || this.materials[materialId].count < count) {
      return false;
    }

    this.materials[materialId].count -= count;
    return true;
  }

  /**
   * 消耗碎片
   * @param {string} fragmentId 碎片ID
   * @param {number} count 消耗数量
   * @returns {boolean} 是否消耗成功
   */
  consumeFragment(fragmentId, count) {
    if (!this.fragments[fragmentId] || this.fragments[fragmentId].count < count) {
      return false;
    }

    this.fragments[fragmentId].count -= count;
    return true;
  }

  /**
   * 保存数据
   * @param {Object} gameState 游戏状态对象
   */
  saveToGameState(gameState) {
    if (!gameState) return;

    // 保存材料和碎片数据
    gameState.skillMaterials = { ...this.materials };
    gameState.skillFragments = { ...this.fragments };
  }

  /**
   * 获取功法进阶所需材料
   * @param {Skill} skill 要进阶的功法
   * @returns {Object} 所需材料和数量
   */
  getAdvancementMaterials(skill) {
    if (!skill) return null;

    return skill.getAdvancementMaterials();
  }

  /**
   * 功法进阶
   * @param {Skill} skill 要进阶的功法
   * @returns {Object} 进阶结果
   */
  advanceSkill(skill) {
    if (!skill) return { success: false, reason: '功法无效' };

    // 获取进阶所需材料
    const materials = this.getAdvancementMaterials(skill);
    if (!materials) {
      return { success: false, reason: '无法获取进阶所需材料' };
    }

    // 检查材料是否足够
    for (const [materialId, count] of Object.entries(materials)) {
      if (!this.materials[materialId] || this.materials[materialId].count < count) {
        return { success: false, reason: `材料${this.materials[materialId]?.name || materialId}不足` };
      }
    }

    // 消耗材料
    for (const [materialId, count] of Object.entries(materials)) {
      this.consumeMaterial(materialId, count);
    }

    // 如果功法没有进阶效果，添加默认的进阶效果
    if (!skill.advancementEffects || skill.advancementEffects.length === 0) {
      skill.advancementEffects = skill.getDefaultAdvancementEffects();
    }

    // 进行进阶
    const result = skill.advance();

    return result;
  }

  /**
   * 获取功法组合效果
   * @param {Array} skillIds 功法ID数组
   * @returns {Object|null} 组合效果或null
   */
  getSkillCombinationEffect(skillIds) {
    if (!skillIds || !Array.isArray(skillIds) || skillIds.length < 2) {
      return null;
    }

    // 排序功法ID，便于匹配
    const sortedSkillIds = [...skillIds].sort();

    // 检查是否匹配任何组合
    for (const key in this.skillCombinations) {
      const combination = this.skillCombinations[key];

      // 检查是否包含组合中的所有功法
      const hasAllSkills = combination.skills.every(id => sortedSkillIds.includes(id));

      if (hasAllSkills) {
        return combination;
      }
    }

    return null;
  }

  /**
   * 获取所有可能的功法组合
   * @returns {Array} 功法组合列表
   */
  getAllSkillCombinations() {
    return Object.values(this.skillCombinations);
  }

  /**
   * 从游戏状态加载数据
   * @param {Object} gameState 游戏状态对象
   */
  loadFromGameState(gameState) {
    if (!gameState) return;

    // 加载材料数据
    if (gameState.skillMaterials) {
      this.materials = { ...gameState.skillMaterials };
    }

    // 加载碎片数据
    if (gameState.skillFragments) {
      this.fragments = { ...gameState.skillFragments };
    }
  }
}

// 导出SkillManager类
export default SkillManager;