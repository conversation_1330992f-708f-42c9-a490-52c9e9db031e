/**
 * 按钮组件
 */
class Button {
  constructor(ctx, x, y, width, height, text, normalImg, pressedImg, onClick) {
    this.ctx = ctx;
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.text = text;
    this.normalImg = normalImg;
    this.pressedImg = pressedImg || normalImg;
    this.onClick = onClick;

    // 检查pressedImg是否为颜色字符串
    this.pressedImgIsColor = typeof this.pressedImg === 'string' && this.pressedImg.startsWith('rgba(');
    this.normalImgIsColor = typeof this.normalImg === 'string' && this.normalImg.startsWith('rgba(');

    // 按钮状态
    this.isPressed = false;
    this.isHovered = false;
    this.isEnabled = true;

    // 文字颜色
    this.textColor = '#ffffff';
  }

  // 绘制按钮
  render() {
    if (!this.isEnabled) {
      // 如果按钮禁用，绘制禁用状态
      this.ctx.globalAlpha = 0.5;
    }

    // 绘制按钮背景
    const buttonImg = this.isPressed ? this.pressedImg : this.normalImg;
    const isColorString = this.isPressed ? this.pressedImgIsColor : this.normalImgIsColor;

    // 检查是否为颜色字符串
    if (isColorString) {
      // 如果是颜色字符串，使用fillStyle绘制
      this.ctx.fillStyle = buttonImg;
      this.ctx.fillRect(this.x, this.y, this.width, this.height);

      // 绘制边框
      this.ctx.strokeStyle = '#ffffff';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(this.x, this.y, this.width, this.height);
    }
    // 检查图片是否存在，如果不存在则绘制默认背景
    else if (buttonImg) {
      // 如果是字符串但不是颜色，则使用默认背景
      if (typeof buttonImg === 'string' && !buttonImg.startsWith('#') && !buttonImg.startsWith('rgb')) {
        this.drawDefaultBackground();
      } else {
        try {
          // 如果是对象，尝试绘制图像
          if (typeof buttonImg === 'object') {
            // 在微信小游戏环境中，图像对象可能不兼容
            // 为安全起见，使用默认背景
            this.drawDefaultBackground();
          } else {
            // 如果是颜色字符串，使用填充色
            this.drawDefaultBackground();
          }
        } catch (error) {
          console.error('按钮图片绘制失败', error);
          this.drawDefaultBackground();
        }
      }
    } else {
      this.drawDefaultBackground();
    }

    // 绘制按钮文本
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = this.textColor;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(this.text, this.x + this.width / 2, this.y + this.height / 2);

    // 恢复透明度
    if (!this.isEnabled) {
      this.ctx.globalAlpha = 1.0;
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    // 绘制默认的按钮背景
    this.ctx.fillStyle = this.isPressed ? '#666666' : '#444444';
    this.ctx.fillRect(this.x, this.y, this.width, this.height);

    // 绘制边框
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(this.x, this.y, this.width, this.height);
  }

  // 更新按钮状态
  update() {
    // 暂无需要更新的内容
  }

  // 检查点击是否在按钮范围内
  containsPoint(x, y) {
    return (
      this.isEnabled &&
      x >= this.x &&
      x <= this.x + this.width &&
      y >= this.y &&
      y <= this.y + this.height
    );
  }

  isPointInside(x, y) {
    return x >= this.x &&
           x <= this.x + this.width &&
           y >= this.y &&
           y <= this.y + this.height;
  }
  // 处理按下事件
  onTouchStart(x, y) {
    if (this.isPointInside(x, y) && this.isEnabled) {
      console.log(`按钮被按下: ${this.text}`, x, y);
      this.isPressed = true;
      return true;
    }
    return false;
  }

  // 处理松开事件
  onTouchEnd(x, y) {
    console.log(`按钮点击结束: ${this.text}`, x, y, `按钮状态: ${this.isPressed}`);

    if (this.isPressed) {
      this.isPressed = false;
      if (this.isPointInside(x, y) && this.isEnabled && this.onClick) {
        console.log(`执行按钮事件: ${this.text}`);
        try {
          this.onClick();
        } catch (e) {
          console.error(`按钮点击事件出错:`, e);
        }
      }
      return true;
    }
    return false;
  }

  // 启用按钮
  enable() {
    this.isEnabled = true;
  }

  // 禁用按钮
  disable() {
    this.isEnabled = false;
    this.isPressed = false;
  }

  // 设置按钮位置
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }

  // 设置按钮尺寸
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }

  // 设置按钮文本
  setText(text) {
    this.text = text;
  }

  // 处理触摸移动事件
  onTouchMove(x, y) {
    if (this.isPressed) {
      // 如果按钮已经被按下，检查触摸点是否仍在按钮内
      // 如果不在，可以取消按下状态
      if (!this.isPointInside(x, y)) {
        this.isPressed = false;
      }
      return true;
    }
    return false;
  }
}

export default Button;