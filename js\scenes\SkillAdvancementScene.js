/**
 * 功法进阶场景
 * 用于处理功法进阶操作
 */
import BaseScene from './BaseScene';
import AppContext from '../utils/AppContext';

class SkillAdvancementScene extends BaseScene {
  constructor(canvas, sceneManager) {
    super(canvas, sceneManager);
    
    // 场景名称
    this.name = 'skillAdvancement';
    this.pageTitle = '功法进阶';
    
    // 当前选中的功法
    this.skill = null;
    this.skillId = null;
    
    // 进阶材料信息
    this.advancementMaterials = null;
    
    // 进阶结果
    this.advancementResult = null;
    
    // 按钮区域
    this.buttons = {
      back: { x: 20, y: 50, width: 80, height: 40, text: '返回' },
      advance: { x: this.screenWidth / 2 - 100, y: this.screenHeight - 100, width: 200, height: 60, text: '进阶' }
    };
  }
  
  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("SkillAdvancementScene onShow", params);
      this.visible = true;
      
      // 保存传入的参数
      this.params = params || {};
      
      // 重置进阶结果
      this.advancementResult = null;
      
      // 处理两种情况：直接传递了skill对象或者只传递了skillId
      if (params.skill) {
        this.skill = params.skill;
        this.skillId = params.skill.id;
        console.log(`接收到功法对象，ID: ${this.skillId}`);
      } else if (params.skillId) {
        this.skillId = params.skillId;
        
        // 尝试从游戏上下文中获取skillManager实例
        if (AppContext && AppContext.game && AppContext.game.skillManager) {
          // 获取当前要进阶的功法
          this.skill = AppContext.game.skillManager.getSkillById(this.skillId);
          
          if (!this.skill) {
            console.error(`找不到ID为${this.skillId}的功法`);
            return;
          }
        } else {
          console.warn("skillManager未初始化或不可用");
          // 设置为空避免报错
          this.skill = null;
          return;
        }
      } else {
        console.error('进阶功法场景缺少必要参数：skill或skillId');
        return;
      }
      
      // 获取进阶所需材料
      if (AppContext && AppContext.game && AppContext.game.skillManager) {
        this.advancementMaterials = AppContext.game.skillManager.getAdvancementMaterials(this.skill);
      }
      
      console.log(`功法进阶场景初始化完成，功法: ${this.skill.name}`);
    } catch (error) {
      console.error('初始化功法进阶场景时出错:', error);
    }
  }
  
  // 处理点击事件
  handleClick(x, y) {
    // 检查是否点击了返回按钮
    if (this.isPointInRect(x, y, this.buttons.back)) {
      this.goBack();
      return;
    }
    
    // 检查是否点击了进阶按钮
    if (this.isPointInRect(x, y, this.buttons.advance)) {
      this.advanceSkill();
      return;
    }
  }
  
  // 返回上一个场景
  goBack() {
    if (this.params && this.params.returnScene) {
      this.sceneManager.showScene(this.params.returnScene, this.params.returnParams || {});
    } else {
      this.sceneManager.showScene('skillUpgrade', { skill: this.skill });
    }
  }
  
  // 进阶功法
  advanceSkill() {
    try {
      // 检查功法是否有效
      if (!this.skill) {
        console.error('没有选择功法或功法无效');
        return;
      }
      
      // 获取skillManager
      if (!AppContext || !AppContext.game || !AppContext.game.skillManager) {
        console.error('skillManager未初始化');
        return;
      }
      
      const skillManager = AppContext.game.skillManager;
      
      // 进行功法进阶
      this.advancementResult = skillManager.advanceSkill(this.skill);
      
      if (this.advancementResult.success) {
        console.log(`${this.skill.name} 进阶成功，当前进阶等级: ${this.skill.advancementLevel}`);
        
        // 更新进阶材料信息
        this.advancementMaterials = skillManager.getAdvancementMaterials(this.skill);
        
        // 保存游戏状态
        skillManager.saveToGameState(AppContext.game.gameStateManager.gameState);
        AppContext.game.gameStateManager.saveGameState();
      } else {
        console.log(`进阶失败: ${this.advancementResult.reason}`);
      }
    } catch (error) {
      console.error('进阶功法时出错:', error);
    }
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    if (!this.skill) {
      return;
    }
    
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制顶部导航栏
    this.drawHeader();
    
    // 绘制功法信息
    this.drawSkillInfo();
    
    // 绘制进阶材料信息
    this.drawAdvancementMaterials();
    
    // 绘制进阶效果预览
    this.drawAdvancementEffects();
    
    // 绘制进阶按钮
    this.drawAdvanceButton();
    
    // 如果有进阶结果，绘制结果提示
    if (this.advancementResult) {
      this.drawAdvancementResult();
    }
  }
  
  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    
    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.pageTitle, this.screenWidth / 2, headerHeight / 2 + 8);
    
    // 绘制返回按钮
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
    this.ctx.fillRect(this.buttons.back.x, this.buttons.back.y, this.buttons.back.width, this.buttons.back.height);
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      this.buttons.back.text,
      this.buttons.back.x + this.buttons.back.width / 2,
      this.buttons.back.y + this.buttons.back.height / 2 + 5
    );
  }
  
  // 绘制功法信息
  drawSkillInfo() {
    const startY = 120;
    const padding = 20;
    
    // 绘制功法名称和品质
    this.ctx.font = 'bold 22px Arial';
    this.ctx.fillStyle = this.skill.getQualityColor();
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${this.skill.name} [${this.skill.getQualityName()}]`, this.screenWidth / 2, startY);
    
    // 绘制功法等级和星级
    this.ctx.font = '18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    
    let starsText = '';
    for (let i = 0; i < this.skill.stars; i++) {
      starsText += '★';
    }
    for (let i = this.skill.stars; i < this.skill.maxStars; i++) {
      starsText += '☆';
    }
    
    this.ctx.fillText(`等级: ${this.skill.level} | 星级: ${starsText}`, this.screenWidth / 2, startY + 30);
    
    // 绘制当前进阶等级
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#ffcc00';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `当前进阶等级: ${this.skill.advancementLevel}/${this.skill.maxAdvancementLevel}`,
      this.screenWidth / 2,
      startY + 60
    );
    
    // 绘制功法描述
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.skill.description, this.screenWidth / 2, startY + 90);
    
    // 绘制功法战力
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ff9900';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`战力: ${this.skill.power}`, this.screenWidth / 2, startY + 120);
  }
  
  // 绘制进阶材料信息
  drawAdvancementMaterials() {
    if (!this.advancementMaterials) {
      return;
    }
    
    const startY = 280;
    const padding = 20;
    
    // 绘制材料标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('进阶所需材料', this.screenWidth / 2, startY);
    
    // 获取材料列表
    const materials = Object.entries(this.advancementMaterials);
    const skillManager = AppContext.game.skillManager;
    
    // 绘制材料列表
    let y = startY + 30;
    for (const [materialId, count] of materials) {
      const material = skillManager.materials[materialId] || skillManager.fragments[materialId];
      if (!material) continue;
      
      const owned = material.count || 0;
      const isEnough = owned >= count;
      
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = isEnough ? '#00cc00' : '#ff0000';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        `${material.name}: ${owned}/${count}`,
        this.screenWidth / 2,
        y
      );
      
      y += 25;
    }
  }
  
  // 绘制进阶效果预览
  drawAdvancementEffects() {
    if (!this.skill.advancementEffects || this.skill.advancementEffects.length === 0) {
      return;
    }
    
    const startY = 380;
    const padding = 20;
    
    // 绘制效果标题
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('进阶效果预览', this.screenWidth / 2, startY);
    
    // 获取下一个进阶等级
    const nextLevel = this.skill.advancementLevel + 1;
    if (nextLevel > this.skill.maxAdvancementLevel || nextLevel > this.skill.advancementEffects.length) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#ff9900';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('已达到最大进阶等级', this.screenWidth / 2, startY + 30);
      return;
    }
    
    // 获取下一个进阶效果
    const nextEffect = this.skill.advancementEffects[nextLevel - 1];
    if (!nextEffect) {
      return;
    }
    
    // 绘制效果描述
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffcc00';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `进阶${nextLevel}效果: ${nextEffect.description}`,
      this.screenWidth / 2,
      startY + 30
    );
    
    // 绘制属性加成
    if (nextEffect.attributes) {
      let y = startY + 60;
      for (const [attr, value] of Object.entries(nextEffect.attributes)) {
        let attrName = attr;
        switch (attr) {
          case 'hp': attrName = '生命值'; break;
          case 'hpPercent': attrName = '生命值百分比'; break;
          case 'attack': attrName = '攻击力'; break;
          case 'attackPercent': attrName = '攻击力百分比'; break;
          case 'defense': attrName = '防御力'; break;
          case 'defensePercent': attrName = '防御力百分比'; break;
          case 'speed': attrName = '速度'; break;
          case 'critical': attrName = '暴击率'; break;
          case 'critDamage': attrName = '暴击伤害'; break;
        }
        
        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = '#cccccc';
        this.ctx.textAlign = 'center';
        
        const valueText = attr.includes('Percent') ? `${value}%` : value;
        this.ctx.fillText(
          `${attrName}: +${valueText}`,
          this.screenWidth / 2,
          y
        );
        
        y += 20;
      }
    }
    
    // 绘制特殊效果
    if (nextEffect.specialEffect) {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#00ccff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        `特殊效果: ${nextEffect.specialEffect.name}`,
        this.screenWidth / 2,
        startY + 120
      );
      
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        nextEffect.specialEffect.description,
        this.screenWidth / 2,
        startY + 140
      );
    }
  }
  
  // 绘制进阶按钮
  drawAdvanceButton() {
    const button = this.buttons.advance;
    
    // 检查是否可以进阶
    const canAdvance = this.skill.advancementLevel < this.skill.maxAdvancementLevel;
    
    // 绘制按钮背景
    this.ctx.fillStyle = canAdvance ? 'rgba(0, 100, 200, 0.8)' : 'rgba(100, 100, 100, 0.8)';
    this.ctx.fillRect(button.x, button.y, button.width, button.height);
    
    // 绘制按钮文本
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      button.text,
      button.x + button.width / 2,
      button.y + button.height / 2 + 7
    );
  }
  
  // 绘制进阶结果
  drawAdvancementResult() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制结果框
    const boxWidth = 300;
    const boxHeight = 200;
    const boxX = (this.screenWidth - boxWidth) / 2;
    const boxY = (this.screenHeight - boxHeight) / 2;
    
    this.ctx.fillStyle = 'rgba(50, 50, 50, 0.9)';
    this.ctx.fillRect(boxX, boxY, boxWidth, boxHeight);
    
    // 绘制边框
    this.ctx.strokeStyle = this.advancementResult.success ? '#00cc00' : '#ff0000';
    this.ctx.lineWidth = 3;
    this.ctx.strokeRect(boxX, boxY, boxWidth, boxHeight);
    
    // 绘制标题
    this.ctx.font = 'bold 22px Arial';
    this.ctx.fillStyle = this.advancementResult.success ? '#00cc00' : '#ff0000';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      this.advancementResult.success ? '进阶成功' : '进阶失败',
      this.screenWidth / 2,
      boxY + 40
    );
    
    // 绘制详细信息
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    
    if (this.advancementResult.success) {
      this.ctx.fillText(
        `${this.skill.name} 进阶到 ${this.skill.advancementLevel} 级`,
        this.screenWidth / 2,
        boxY + 80
      );
      
      // 如果有解锁的效果，显示效果信息
      if (this.advancementResult.unlockedEffect) {
        this.ctx.fillText(
          `解锁效果: ${this.advancementResult.unlockedEffect.description}`,
          this.screenWidth / 2,
          boxY + 110
        );
      }
      
      // 显示战力提升
      this.ctx.fillStyle = '#ffcc00';
      this.ctx.fillText(
        `战力提升至: ${this.skill.power}`,
        this.screenWidth / 2,
        boxY + 140
      );
    } else {
      this.ctx.fillText(
        this.advancementResult.reason || '未知原因',
        this.screenWidth / 2,
        boxY + 100
      );
    }
    
    // 绘制确认按钮
    const buttonWidth = 100;
    const buttonHeight = 40;
    const buttonX = (this.screenWidth - buttonWidth) / 2;
    const buttonY = boxY + boxHeight - buttonHeight - 20;
    
    this.ctx.fillStyle = 'rgba(0, 100, 200, 0.8)';
    this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
    
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      '确定',
      buttonX + buttonWidth / 2,
      buttonY + buttonHeight / 2 + 5
    );
    
    // 添加点击事件处理
    this.addClickHandler(buttonX, buttonY, buttonWidth, buttonHeight, () => {
      this.advancementResult = null;
    });
  }
  
  // 添加临时点击处理器
  addClickHandler(x, y, width, height, callback) {
    this.tempClickHandlers = this.tempClickHandlers || [];
    this.tempClickHandlers.push({ x, y, width, height, callback });
  }
  
  // 重写handleClick方法，处理临时点击处理器
  handleClick(x, y) {
    // 检查临时点击处理器
    if (this.tempClickHandlers && this.tempClickHandlers.length > 0) {
      for (const handler of this.tempClickHandlers) {
        if (this.isPointInRect(x, y, handler)) {
          handler.callback();
          this.tempClickHandlers = [];
          return;
        }
      }
    }
    
    // 如果有进阶结果显示，点击任何地方都关闭它
    if (this.advancementResult) {
      this.advancementResult = null;
      return;
    }
    
    // 调用原始的点击处理逻辑
    super.handleClick(x, y);
  }
}

export default SkillAdvancementScene;
