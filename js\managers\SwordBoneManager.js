/**
 * 剑骨管理器
 * 负责管理剑骨的等级、品阶和属性计算
 */
import EventEmitter from '../utils/EventEmitter.js';
import { SWORD_BONE_LEVEL_CONFIG, SWORD_BONE_RANK_CONFIG, SWORD_BONE_EFFECTS } from '../config/SwordBoneConfig.js';

class SwordBoneManager extends EventEmitter {
  constructor() {
    super();
    
    // 剑骨等级（默认为0级）
    this.level = 0;
    
    // 剑骨品阶（默认为0阶）
    this.rank = 0;
    
    // 剑骨解锁状态（默认未解锁）
    this.unlocked = false;
  }
  
  /**
   * 解锁剑骨
   * @returns {boolean} 是否成功解锁
   */
  unlock() {
    if (this.unlocked) {
      return false;
    }
    
    this.unlocked = true;
    this.emit('swordBoneUnlocked');
    return true;
  }
  
  /**
   * 提升剑骨等级
   * @param {number} lingshi 消耗的灵石数量
   * @returns {boolean} 是否成功提升等级
   */
  levelUp(lingshi) {
    // 检查剑骨是否已解锁
    if (!this.unlocked) {
      return false;
    }
    
    // 获取当前等级配置
    const currentLevelConfig = this.getLevelConfig(this.level);
    if (!currentLevelConfig) {
      return false;
    }
    
    // 检查是否已达到最高等级
    if (currentLevelConfig.upgradeCost === null) {
      return false;
    }
    
    // 检查灵石是否足够
    if (lingshi < currentLevelConfig.upgradeCost) {
      return false;
    }
    
    // 提升等级
    this.level += 1;
    
    // 触发等级提升事件
    this.emit('swordBoneLevelUp', this.level);
    
    return true;
  }
  
  /**
   * 提升剑骨品阶
   * @param {number} xianyu 消耗的仙玉数量
   * @returns {boolean} 是否成功提升品阶
   */
  rankUp(xianyu) {
    // 检查剑骨是否已解锁
    if (!this.unlocked) {
      return false;
    }
    
    // 获取当前品阶配置
    const currentRankConfig = this.getRankConfig(this.rank);
    if (!currentRankConfig) {
      return false;
    }
    
    // 检查是否已达到最高品阶
    if (currentRankConfig.upgradeCost === null) {
      return false;
    }
    
    // 检查仙玉是否足够
    if (xianyu < currentRankConfig.upgradeCost) {
      return false;
    }
    
    // 提升品阶
    this.rank += 1;
    
    // 触发品阶提升事件
    this.emit('swordBoneRankUp', this.rank);
    
    return true;
  }
  
  /**
   * 获取剑骨等级配置
   * @param {number} level 等级
   * @returns {Object|null} 等级配置
   */
  getLevelConfig(level) {
    return SWORD_BONE_LEVEL_CONFIG.find(config => config.level === level) || null;
  }
  
  /**
   * 获取剑骨品阶配置
   * @param {number} rank 品阶
   * @returns {Object|null} 品阶配置
   */
  getRankConfig(rank) {
    return SWORD_BONE_RANK_CONFIG.find(config => config.rank === rank) || null;
  }
  
  /**
   * 获取剑骨当前等级的属性
   * @returns {Object} 属性对象
   */
  getLevelAttributes() {
    const levelConfig = this.getLevelConfig(this.level);
    return levelConfig ? levelConfig.attributes : {
      attack: 0,
      critRate: 0,
      critDamage: 0,
      penetration: 0
    };
  }
  
  /**
   * 获取剑骨当前品阶的倍率
   * @returns {number} 属性倍率
   */
  getRankMultiplier() {
    const rankConfig = this.getRankConfig(this.rank);
    return rankConfig ? rankConfig.multiplier : 1.0;
  }
  
  /**
   * 获取剑骨当前解锁的特殊效果
   * @returns {Array} 特殊效果列表
   */
  getUnlockedEffects() {
    return SWORD_BONE_EFFECTS.filter(effect => effect.rank <= this.rank);
  }
  
  /**
   * 获取剑骨当前的最终属性（包含等级属性和品阶倍率）
   * @returns {Object} 最终属性对象
   */
  getFinalAttributes() {
    // 获取基础属性
    const baseAttributes = this.getLevelAttributes();
    
    // 获取品阶倍率
    const multiplier = this.getRankMultiplier();
    
    // 计算最终属性
    const finalAttributes = {
      attack: Math.floor(baseAttributes.attack * multiplier),
      critRate: parseFloat((baseAttributes.critRate * multiplier).toFixed(4)),
      critDamage: parseFloat((baseAttributes.critDamage * multiplier).toFixed(4)),
      penetration: Math.floor(baseAttributes.penetration * multiplier)
    };
    
    // 添加特殊效果加成
    const effects = this.getUnlockedEffects();
    effects.forEach(effect => {
      if (effect.effect.critRateBonus) {
        finalAttributes.critRate += effect.effect.critRateBonus;
      }
      if (effect.effect.critDamageBonus) {
        finalAttributes.critDamage += effect.effect.critDamageBonus;
      }
      if (effect.effect.penetrationBonus) {
        finalAttributes.penetration = Math.floor(finalAttributes.penetration * (1 + effect.effect.penetrationBonus));
      }
      if (effect.effect.attackBonus) {
        finalAttributes.attack = Math.floor(finalAttributes.attack * (1 + effect.effect.attackBonus));
      }
      if (effect.effect.damageBonus) {
        finalAttributes.damageBonus = effect.effect.damageBonus;
      }
    });
    
    return finalAttributes;
  }
  
  /**
   * 获取升级所需灵石
   * @returns {number|null} 升级所需灵石，如果已达到最高等级则返回null
   */
  getUpgradeCost() {
    const levelConfig = this.getLevelConfig(this.level);
    return levelConfig ? levelConfig.upgradeCost : null;
  }
  
  /**
   * 获取升阶所需仙玉
   * @returns {number|null} 升阶所需仙玉，如果已达到最高品阶则返回null
   */
  getRankUpCost() {
    const rankConfig = this.getRankConfig(this.rank);
    return rankConfig ? rankConfig.upgradeCost : null;
  }
  
  /**
   * 获取剑骨名称
   * @returns {string} 剑骨名称
   */
  getName() {
    const rankConfig = this.getRankConfig(this.rank);
    return rankConfig ? rankConfig.name : "未知剑骨";
  }
  
  /**
   * 获取剑骨描述
   * @returns {string} 剑骨描述
   */
  getDescription() {
    const rankConfig = this.getRankConfig(this.rank);
    return rankConfig ? rankConfig.description : "未知剑骨描述";
  }
  
  /**
   * 将剑骨数据转换为JSON格式
   * @returns {Object} JSON对象
   */
  toJSON() {
    return {
      level: this.level,
      rank: this.rank,
      unlocked: this.unlocked
    };
  }
  
  /**
   * 从JSON对象加载剑骨数据
   * @param {Object} json JSON对象
   */
  fromJSON(json) {
    if (!json) return;
    
    this.level = json.level || 0;
    this.rank = json.rank || 0;
    this.unlocked = json.unlocked || false;
  }
}

export default SwordBoneManager;
