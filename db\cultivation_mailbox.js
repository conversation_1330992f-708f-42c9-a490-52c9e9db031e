/**
 * 修仙六道 - 邮件系统数据库表结构
 * 表名：cultivation_mailbox
 * 用途：存储玩家邮件信息，包括系统奖励、活动礼包和补偿邮件
 */

// 表结构定义
const schema = {
  // 主键
  _id: {
    type: 'ObjectId',
    required: true,
    description: '文档唯一标识'
  },
  
  // 玩家标识
  player_id: {
    type: 'string',
    required: true,
    description: '玩家OpenID',
    index: true
  },
  
  // 邮件类型
  mail_type: {
    type: 'string',
    required: true,
    description: '邮件类型',
    enum: ['system_reward', 'activity_gift', 'compensation'],
    default: 'system_reward'
  },
  
  // 邮件标题
  title: {
    type: 'string',
    required: true,
    description: '邮件标题',
    maxLength: 50
  },
  
  // 邮件内容
  content: {
    type: 'string',
    required: true,
    description: '邮件内容',
    maxLength: 500
  },
  
  // 奖励物品
  rewards: {
    type: 'array',
    required: false,
    description: '奖励物品列表',
    items: {
      type: 'object',
      properties: {
        item_id: {
          type: 'string',
          required: true,
          description: '物品ID'
        },
        count: {
          type: 'number',
          required: true,
          description: '物品数量',
          minimum: 1
        }
      }
    }
  },
  
  // 发送时间
  send_time: {
    type: 'timestamp',
    required: true,
    description: '邮件发送时间',
    default: 'now()',
    index: true
  },
  
  // 有效期天数
  expire_days: {
    type: 'number',
    required: true,
    description: '邮件有效期天数',
    default: 7,
    minimum: 1,
    maximum: 30
  },
  
  // 是否已读
  is_read: {
    type: 'boolean',
    required: true,
    description: '是否已读',
    default: false
  },
  
  // 是否已领取
  is_claimed: {
    type: 'boolean',
    required: true,
    description: '是否已领取奖励',
    default: false
  }
};

// 索引定义
const indexes = [
  {
    name: 'idx_player_id',
    fields: {
      player_id: 1
    },
    description: '玩家邮件查询索引'
  },
  {
    name: 'idx_send_time',
    fields: {
      send_time: -1
    },
    description: '发送时间倒序索引'
  },
  {
    name: 'idx_player_time',
    fields: {
      player_id: 1,
      send_time: -1
    },
    description: '玩家邮件时间复合索引'
  }
];

// 示例数据
const exampleData = {
  _id: '507f1f77bcf86cd799439011',
  player_id: 'oWx123456789',
  mail_type: 'activity_gift',
  title: '蟠桃会庆典奖励',
  content: '道友，请领取你的千年灵芝x3',
  rewards: [
    {
      item_id: 'item_1001',
      count: 3
    }
  ],
  send_time: new Date(),
  expire_days: 7,
  is_read: false,
  is_claimed: false
};

module.exports = {
  schema,
  indexes,
  exampleData
}; 