/**
 * 单人战斗场景
 * 显示1v1战斗界面，包括角色状态、技能按钮和战斗动画
 */
import BaseScene from '../../scenes/BaseScene';
import Button from '../../ui/Button';

class SingleBattleScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 战斗数据
    this.playerCharacter = null;
    this.enemyCharacter = null;
    this.stageData = null;
    this.battleSystem = null;
    this.onBattleEnd = null;

    // UI元素
    this.skillButtons = [];

    // 动画相关
    this.animations = [];
    this.lastUpdateTime = 0;

    // 战斗UI配置
    this.playerPosition = { x: screenWidth * 0.2, y: screenHeight * 0.6 };
    this.enemyPosition = { x: screenWidth * 0.8, y: screenHeight * 0.6 };
    this.characterSize = 80;
  }

  /**
   * 场景显示时的回调
   */
  onShow(params) {
    try {
      console.log("SingleBattleScene onShow", params);
      this.visible = true;

      // 获取战斗数据
      this.playerCharacter = params.playerCharacter;
      this.enemyCharacter = params.enemyCharacter;
      this.stageData = params.stageData;
      this.battleSystem = params.battleSystem;
      this.onBattleEnd = params.onBattleEnd;

      // 初始化UI
      this.initUI();

      // 开始战斗循环
      this.lastUpdateTime = Date.now();
      this.startBattleLoop();

      console.log('单人战斗场景初始化完成');
    } catch (error) {
      console.error("SingleBattleScene.onShow 出错:", error);
    }
  }

  /**
   * 初始化UI
   */
  initUI() {
    this.skillButtons = [];

    // 创建技能按钮
    this.createSkillButtons();
  }

  /**
   * 创建技能按钮
   */
  createSkillButtons() {
    if (!this.playerCharacter) return;

    const buttonSize = 60;
    const buttonSpacing = 10;
    const startX = 20;
    const startY = this.screenHeight - buttonSize - 20;

    // 创建主动技能按钮（槽位1-5）
    for (let i = 1; i <= 5; i++) {
      const skill = this.playerCharacter.getSkillInSlot(i);
      if (skill) {
        const button = new Button(
          this.ctx,
          startX + (i - 1) * (buttonSize + buttonSpacing),
          startY,
          buttonSize,
          buttonSize,
          skill.name.charAt(0),
          null,
          null,
          () => {
            this.useSkill(skill);
          }
        );

        // 设置技能按钮样式
        button.backgroundColor = '#4ECDC4';
        button.textColor = '#FFFFFF';
        button.fontSize = '18px';

        this.skillButtons.push({
          button: button,
          skill: skill,
          slotIndex: i
        });
      }
    }

    // 添加按钮到UI元素列表
    this.skillButtons.forEach(skillButton => {
      this.addUIElement(skillButton.button);
    });
  }

  /**
   * 使用技能
   * @param {Object} skill 技能对象
   */
  useSkill(skill) {
    if (!this.battleSystem || !this.battleSystem.isInBattle) return;

    // 检查技能冷却
    const skillId = skill.id;
    const cooldownRemaining = this.battleSystem.skillCooldowns[skillId] || 0;

    if (cooldownRemaining > 0) {
      console.log(`技能 ${skill.name} 还在冷却中，剩余 ${Math.ceil(cooldownRemaining / 1000)} 秒`);
      return;
    }

    // 执行技能
    this.battleSystem.executeSkill(this.playerCharacter, this.enemyCharacter, skill);

    // 设置技能冷却
    this.battleSystem.skillCooldowns[skillId] = skill.cooldown || 0;

    console.log(`使用技能: ${skill.name}`);
  }

  /**
   * 开始战斗循环
   */
  startBattleLoop() {
    const update = () => {
      if (!this.visible || !this.battleSystem || !this.battleSystem.isInBattle) {
        return;
      }

      const currentTime = Date.now();
      const deltaTime = currentTime - this.lastUpdateTime;
      this.lastUpdateTime = currentTime;

      // 更新战斗系统
      this.battleSystem.updateTimeline(deltaTime);

      // 更新动画
      this.updateAnimations(deltaTime);

      // 继续循环
      requestAnimationFrame(update);
    };

    requestAnimationFrame(update);
  }

  /**
   * 更新动画
   * @param {number} deltaTime 时间差
   */
  updateAnimations(deltaTime) {
    // 更新所有动画
    this.animations = this.animations.filter(animation => {
      animation.update(deltaTime);
      return !animation.isFinished();
    });
  }

  /**
   * 播放攻击动画
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   * @param {number} damage 伤害值
   */
  playAttackAnimation(attacker, target, damage) {
    // 创建攻击动画
    const animation = {
      type: 'attack',
      attacker: attacker,
      target: target,
      damage: damage,
      duration: 1000, // 1秒
      elapsed: 0,

      update: function(deltaTime) {
        this.elapsed += deltaTime;
      },

      isFinished: function() {
        return this.elapsed >= this.duration;
      }
    };

    this.animations.push(animation);
  }

  /**
   * 播放技能动画
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   * @param {Object} skill 技能
   * @param {number} damage 伤害值
   */
  playSkillAnimation(caster, target, skill, damage) {
    // 创建技能动画
    const animation = {
      type: 'skill',
      caster: caster,
      target: target,
      skill: skill,
      damage: damage,
      duration: 1500, // 1.5秒
      elapsed: 0,

      update: function(deltaTime) {
        this.elapsed += deltaTime;
      },

      isFinished: function() {
        return this.elapsed >= this.duration;
      }
    };

    this.animations.push(animation);
  }

  /**
   * 处理触摸事件
   */
  handleTouchStart(x, y) {
    console.log(`SingleBattleScene handleTouchStart: ${x},${y}`);

    // 检查技能按钮点击
    for (const skillButton of this.skillButtons) {
      if (skillButton.button.isPointInside(x, y)) {
        skillButton.button.onClick();
        return true;
      }
    }

    return false;
  }

  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制角色
    this.drawCharacters();

    // 绘制UI
    this.drawUI();

    // 绘制动画
    this.drawAnimations();
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, 'rgba(135, 206, 235, 0.8)'); // 天蓝色
    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)'); // 白色
    gradient.addColorStop(1, 'rgba(34, 139, 34, 0.8)'); // 森林绿

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制地面线
    this.ctx.strokeStyle = 'rgba(139, 69, 19, 0.6)'; // 棕色
    this.ctx.lineWidth = 3;
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.screenHeight * 0.8);
    this.ctx.lineTo(this.screenWidth, this.screenHeight * 0.8);
    this.ctx.stroke();
  }

  /**
   * 绘制角色
   */
  drawCharacters() {
    // 绘制玩家角色
    if (this.playerCharacter) {
      this.drawCharacter(this.playerCharacter, this.playerPosition, true);
    }

    // 绘制敌方角色
    if (this.enemyCharacter) {
      this.drawCharacter(this.enemyCharacter, this.enemyPosition, false);
    }
  }

  /**
   * 绘制单个角色
   * @param {Object} character 角色对象
   * @param {Object} position 位置
   * @param {boolean} isPlayer 是否为玩家角色
   */
  drawCharacter(character, position, isPlayer) {
    const x = position.x;
    const y = position.y;
    const size = this.characterSize;

    // 绘制角色圆形
    this.ctx.fillStyle = isPlayer ? '#4CAF50' : '#F44336'; // 玩家绿色，敌人红色
    this.ctx.beginPath();
    this.ctx.arc(x, y, size / 2, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制角色边框
    this.ctx.strokeStyle = '#FFFFFF';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // 绘制角色名称
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(character.name, x, y - size / 2 - 10);

    // 绘制血条
    this.drawHealthBar(character, x, y + size / 2 + 10);
  }

  /**
   * 绘制血条
   * @param {Object} character 角色对象
   * @param {number} x X坐标
   * @param {number} y Y坐标
   */
  drawHealthBar(character, x, y) {
    const barWidth = 100;
    const barHeight = 8;
    const hpPercentage = character.currentHp / character.maxHp;

    // 绘制血条背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(x - barWidth / 2, y, barWidth, barHeight);

    // 绘制血条
    this.ctx.fillStyle = hpPercentage > 0.5 ? '#4CAF50' : hpPercentage > 0.2 ? '#FF9800' : '#F44336';
    this.ctx.fillRect(x - barWidth / 2, y, barWidth * hpPercentage, barHeight);

    // 绘制血条边框
    this.ctx.strokeStyle = '#FFFFFF';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x - barWidth / 2, y, barWidth, barHeight);

    // 绘制血量文字
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`${character.currentHp}/${character.maxHp}`, x, y + barHeight + 15);
  }

  /**
   * 绘制UI
   */
  drawUI() {
    // 绘制技能按钮背景
    this.drawSkillPanel();

    // 绘制技能按钮
    this.drawSkillButtons();
  }

  /**
   * 绘制技能面板
   */
  drawSkillPanel() {
    const panelHeight = 100;
    const panelY = this.screenHeight - panelHeight;

    // 绘制面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, panelY, this.screenWidth, panelHeight);

    // 绘制面板标题
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('技能', 20, panelY + 25);
  }

  /**
   * 绘制技能按钮
   */
  drawSkillButtons() {
    this.skillButtons.forEach(skillButton => {
      const button = skillButton.button;
      const skill = skillButton.skill;

      // 检查技能冷却
      const cooldownRemaining = this.battleSystem ? (this.battleSystem.skillCooldowns[skill.id] || 0) : 0;
      const isOnCooldown = cooldownRemaining > 0;

      // 设置按钮状态
      if (isOnCooldown) {
        button.backgroundColor = '#666666';
        button.textColor = '#CCCCCC';
      } else {
        button.backgroundColor = '#4ECDC4';
        button.textColor = '#FFFFFF';
      }

      // 绘制按钮
      button.render();

      // 绘制冷却时间
      if (isOnCooldown) {
        const cooldownSeconds = Math.ceil(cooldownRemaining / 1000);
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          cooldownSeconds.toString(),
          button.x + button.width / 2,
          button.y + button.height / 2 + 20
        );
      }
    });
  }

  /**
   * 绘制动画
   */
  drawAnimations() {
    this.animations.forEach(animation => {
      if (animation.type === 'attack') {
        this.drawAttackAnimation(animation);
      } else if (animation.type === 'skill') {
        this.drawSkillAnimation(animation);
      }
    });
  }

  /**
   * 绘制攻击动画
   * @param {Object} animation 动画对象
   */
  drawAttackAnimation(animation) {
    const progress = animation.elapsed / animation.duration;

    // 简单的伤害数字动画
    if (animation.target) {
      const targetPos = animation.target === this.playerCharacter ? this.playerPosition : this.enemyPosition;
      const y = targetPos.y - 50 - progress * 30; // 向上飘动
      const alpha = 1 - progress; // 逐渐透明

      this.ctx.save();
      this.ctx.globalAlpha = alpha;
      this.ctx.font = 'bold 24px Arial';
      this.ctx.fillStyle = '#FF4444';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`-${animation.damage}`, targetPos.x, y);
      this.ctx.restore();
    }
  }

  /**
   * 绘制技能动画
   * @param {Object} animation 动画对象
   */
  drawSkillAnimation(animation) {
    const progress = animation.elapsed / animation.duration;

    // 技能特效动画
    if (animation.caster && animation.target) {
      const casterPos = animation.caster === this.playerCharacter ? this.playerPosition : this.enemyPosition;
      const targetPos = animation.target === this.playerCharacter ? this.playerPosition : this.enemyPosition;

      // 绘制技能轨迹（如果是远程技能）
      if (animation.skill.animationType === 'ranged' && progress < 0.5) {
        const trajectoryProgress = progress * 2; // 前半段时间用于轨迹
        const x = casterPos.x + (targetPos.x - casterPos.x) * trajectoryProgress;
        const y = casterPos.y + (targetPos.y - casterPos.y) * trajectoryProgress - Math.sin(trajectoryProgress * Math.PI) * 20;

        this.ctx.save();
        this.ctx.fillStyle = '#FFD700';
        this.ctx.beginPath();
        this.ctx.arc(x, y, 8, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      }

      // 绘制技能伤害数字（后半段时间）
      if (progress >= 0.5) {
        const damageProgress = (progress - 0.5) * 2;
        const y = targetPos.y - 50 - damageProgress * 40;
        const alpha = 1 - damageProgress;

        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.font = 'bold 28px Arial';
        this.ctx.fillStyle = '#FF6600'; // 技能伤害用橙色
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`-${animation.damage}`, targetPos.x, y);

        // 绘制技能名称
        this.ctx.font = 'bold 16px Arial';
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillText(animation.skill.name, targetPos.x, y - 35);
        this.ctx.restore();
      }
    }
  }
}

export default SingleBattleScene;
