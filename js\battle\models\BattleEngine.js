/**
 * 战斗引擎
 * 负责处理战斗逻辑和模拟战斗过程
 */
class BattleEngine {
  constructor() {
    // 战斗单位
    this.playerUnits = [];
    this.enemyUnits = [];
    
    // 战斗状态
    this.currentTurn = 0;
    this.battleFinished = false;
    this.actionCost = 100; // 行动需要的点数
    this.maxTurns = 30; // 战斗最大回合数
  }
  
  /**
   * 初始化战斗引擎
   * @param {Array} playerUnits 玩家单位
   * @param {Array} enemyUnits 敌方单位
   */
  init(playerUnits, enemyUnits) {
    this.playerUnits = playerUnits;
    this.enemyUnits = enemyUnits;
    this.currentTurn = 0;
    this.battleFinished = false;
  }
  
  /**
   * 生成战斗动作序列
   * @returns {Array} 战斗动作序列
   */
  generateBattleActions() {
    const actions = [];
    
    // 模拟战斗过程
    this.currentTurn = 0;
    this.battleFinished = false;
    
    // 所有单位初始化行动点数
    [...this.playerUnits, ...this.enemyUnits].forEach(unit => {
      unit.actionPoints = 0;
    });
    
    // 开始模拟战斗
    while (!this.battleFinished && this.currentTurn < this.maxTurns) {
      // 新回合开始
      this.currentTurn++;
      
      // 添加回合开始动作
      actions.push({ type: 'turn', turn: this.currentTurn });
      
      // 回合开始时更新所有单位
      [...this.playerUnits, ...this.enemyUnits].forEach(unit => {
        if (unit.isAlive()) {
          unit.onTurnStart();
        }
      });
      
      // 处理本回合的所有行动
      let actionTaken;
      do {
        actionTaken = false;
        
        // 找出可以行动的单位
        const actableUnits = [...this.playerUnits, ...this.enemyUnits].filter(unit => 
          unit.isAlive() && unit.canAct(this.actionCost)
        );
        
        // 按速度排序决定行动顺序
        actableUnits.sort((a, b) => b.actionPoints - a.actionPoints);
        
        // 如果有单位可以行动
        if (actableUnits.length > 0) {
          // 处理一个单位的行动
          const unit = actableUnits[0];
          unit.spendActionPoints(this.actionCost);
          
          // 获取行动
          const action = this.getUnitAction(unit);
          
          // 执行行动
          this.executeAction(action);
          
          // 添加到动作序列
          actions.push(action);
          
          actionTaken = true;
          
          // 检查战斗是否结束
          if (this.checkBattleEnd()) {
            // 添加战斗结束动作
            actions.push({
              type: 'end',
              result: {
                victory: this.playerUnits.some(unit => unit.isAlive()),
                turn: this.currentTurn
              }
            });
            
            this.battleFinished = true;
            break;
          }
        }
      } while (actionTaken);
      
      // 如果战斗已经结束，跳出循环
      if (this.battleFinished) {
        break;
      }
    }
    
    // 如果达到最大回合数还未结束，强制结束战斗
    if (!this.battleFinished) {
      // 添加战斗结束动作
      actions.push({
        type: 'end',
        result: {
          victory: this.playerUnits.some(unit => unit.isAlive()),
          turn: this.currentTurn,
          timeout: true
        }
      });
    }
    
    return actions;
  }
  
  /**
   * 获取单位的行动
   * @param {Object} unit 战斗单位
   * @returns {Object} 行动数据
   */
  getUnitAction(unit) {
    // 简单AI：根据单位类型选择行动目标
    let targets;
    if (unit.type === 'player') {
      // 玩家单位攻击敌方单位
      targets = this.enemyUnits.filter(enemy => enemy.isAlive());
    } else {
      // 敌方单位攻击玩家单位
      targets = this.playerUnits.filter(player => player.isAlive());
    }
    
    // 如果没有可攻击目标，返回空闲动作
    if (targets.length === 0) {
      return {
        type: 'idle',
        source: {
          type: unit.type,
          index: unit.index,
          unit: unit
        }
      };
    }
    
    // 选择一个目标（可以根据战略调整目标选择逻辑）
    const target = targets[Math.floor(Math.random() * targets.length)];
    
    // 计算伤害
    const damageInfo = unit.calculateDamage(target);
    
    // 创建攻击动作
    return {
      type: 'attack',
      source: {
        type: unit.type,
        index: unit.index,
        unit: unit
      },
      target: {
        type: target.type,
        index: target.index,
        unit: target
      },
      damage: damageInfo.damage,
      isCritical: damageInfo.isCritical
    };
  }
  
  /**
   * 执行战斗动作
   * @param {Object} action 动作数据
   */
  executeAction(action) {
    // 根据动作类型执行相应的逻辑
    switch (action.type) {
      case 'attack':
        // 攻击动作，目标受到伤害
        if (action.target && action.target.unit && action.target.unit.isAlive()) {
          action.target.unit.takeDamage(action.damage, action.isCritical);
        }
        break;
        
      case 'skill':
        // 技能动作，根据技能效果处理
        if (action.skill && action.skill.execute) {
          action.skill.execute(action.source.unit, action.targets, this);
        }
        break;
        
      case 'item':
        // 使用道具
        if (action.item && action.item.execute) {
          action.item.execute(action.source.unit, action.targets, this);
        }
        break;
    }
  }
  
  /**
   * 检查战斗是否结束
   * @returns {boolean} 战斗是否结束
   */
  checkBattleEnd() {
    // 检查玩家单位是否全部阵亡
    const allPlayersDead = this.playerUnits.every(unit => !unit.isAlive());
    
    // 检查敌方单位是否全部阵亡
    const allEnemiesDead = this.enemyUnits.every(unit => !unit.isAlive());
    
    // 如果任一方全部阵亡，战斗结束
    return allPlayersDead || allEnemiesDead;
  }
  
  /**
   * 模拟整个战斗过程，直接返回结果
   * @returns {Object} 战斗结果
   */
  simulateBattle() {
    // 复制单位状态以不影响原始数据
    const playerUnitsCopy = this.playerUnits.map(unit => unit.clone());
    const enemyUnitsCopy = this.enemyUnits.map(unit => unit.clone());
    
    // 创建临时战斗引擎
    const tempEngine = new BattleEngine();
    tempEngine.init(playerUnitsCopy, enemyUnitsCopy);
    
    // 生成并执行所有战斗动作
    const actions = tempEngine.generateBattleActions();
    
    // 查找结束动作
    const endAction = actions.find(action => action.type === 'end');
    
    // 返回战斗结果
    return endAction ? endAction.result : { 
      victory: false, 
      turn: tempEngine.currentTurn,
      error: '战斗模拟失败'
    };
  }
}

export default BattleEngine; 