/**
 * 背包场景类
 * 管理玩家的物品和装备
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import AppContext from '../utils/AppContext';
import TitleBar from '../ui/TitleBar';

class BackpackScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 4; // 背包对应的导航索引

    // 背包类别
    this.categories = [
      { id: 'all', name: '全部' },
      { id: 'consumable', name: '消耗品' },
      { id: 'material', name: '材料' },
      { id: 'equipment', name: '装备' }
    ];

    // 当前选中的类别
    this.selectedCategory = this.categories[0];

    // 页面状态
    this.items = [];
    this.equipments = [];
    this.displayItems = [];
    this.currentPage = 0;
    this.itemsPerPage = 12;

    // 顶部导航栏
    this.titleBar = null;
  }

  // 初始化UI
  initUI() {
    // 获取资源

    // 创建顶部标题栏
    const headerHeight = 80;
    this.titleBar = new TitleBar(
      this.ctx,
      0,
      0,
      this.screenWidth,
      headerHeight,
      this.resources
    );
    this.addUIElement(this.titleBar);

    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建底部导航栏按钮
    this.tabButtons = [
      this.createTabButton(0 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '主页', 0),
      this.createTabButton(1 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '角色', 1),
      this.createTabButton(2 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '洞府', 2),
      this.createTabButton(3 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '试炼', 3),
      this.createTabButton(4 * tabButtonWidth, tabBarY, tabButtonWidth, tabBarHeight, '背包', 4)
    ];

    // 将按钮添加到UI元素列表
    this.tabButtons.forEach(button => {
      this.addUIElement(button);
    });

    // 创建分类按钮
    const categoryButtonWidth = this.screenWidth / this.categories.length;
    const categoryButtonHeight = 40;
    const categoryButtonY = 80; // 顶部导航栏下方

    this.categoryButtons = this.categories.map((category, index) => {
      const button = new Button(
        this.ctx,
        index * categoryButtonWidth,
        categoryButtonY,
        categoryButtonWidth,
        categoryButtonHeight,
        category.name,
        null,
        null,
        () => {
          console.log(`点击分类按钮: ${category.name}, id: ${category.id}`);
          this.selectCategory(category);
        }
      );

      this.addUIElement(button);
      return button;
    });

    // 创建翻页按钮
    const pageButtonWidth = 100;
    const pageButtonHeight = 40;
    const pageButtonY = this.screenHeight - tabBarHeight - pageButtonHeight - 10;

    // 上一页按钮
    this.prevPageButton = new Button(
      this.ctx,
      10,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '上一页',
      null,
      null,
      () => {
        this.prevPage();
      }
    );

    this.addUIElement(this.prevPageButton);

    // 下一页按钮
    this.nextPageButton = new Button(
      this.ctx,
      this.screenWidth - pageButtonWidth - 10,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '下一页',
      null,
      null,
      () => {
        this.nextPage();
      }
    );

    this.addUIElement(this.nextPageButton);
  }

  // 创建底部导航栏按钮
  createTabButton(x, y, width, height, text, index) {
    return new Button(
      this.ctx,
      x,
      y,
      width,
      height,
      text,
      null,
      null,
      () => {
        this.onTabSelected(index);
      }
    );
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main');
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面，已经在背包页面，不需要切换
        break;
    }
  }

  // 选择分类
  selectCategory(category) {
    console.log(`选择分类: ${category.name}, id: ${category.id}`);
    this.selectedCategory = category;
    this.currentPage = 0;
    this.updateDisplayItems();
  }

  // 上一页
  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updateDisplayItems();
    }
  }

  // 下一页
  nextPage() {
    const totalPages = Math.ceil(this.filterItemsByCategory().length / this.itemsPerPage);
    if (this.currentPage < totalPages - 1) {
      this.currentPage++;
      this.updateDisplayItems();
    }
  }

  // 按类别筛选物品
  filterItemsByCategory() {
    let filteredItems = [];

    if (this.selectedCategory.id === 'all') {
      // 全部物品
      filteredItems = [...this.items, ...this.equipments];
      console.log('筛选全部物品和装备:', filteredItems.length);
    } else if (this.selectedCategory.id === 'equipment') {
      // 仅装备
      // 1. 从装备专用数组中获取装备
      const equipmentsFromArray = this.equipments;

      // 2. 从物品数组中筛选出装备类型的物品
      const equipmentsFromItems = this.items.filter(item => this.isEquipment(item));

      // 3. 合并两个数组
      filteredItems = [...equipmentsFromArray, ...equipmentsFromItems];

      console.log('筛选仅装备:', filteredItems.length);
      console.log('- 来自equipments数组:', equipmentsFromArray.length);
      console.log('- 来自items数组的装备:', equipmentsFromItems.length);

      // 输出装备详情进行调试
      if (filteredItems.length > 0) {
        console.log('装备列表中的第一件装备:', JSON.stringify(filteredItems[0]));

        // 打印所有装备以检查
        console.log('所有装备详情:', filteredItems.map(equip =>
          `${equip.name} (${equip.type}, ${equip.quality})`
        ).join(', '));
      } else {
        console.log('没有找到任何装备，请检查GameStateManager中的getEquipments方法和forgeScene锻造方法');
      }
    } else {
      // 按类型筛选消耗品和材料
      filteredItems = this.items.filter(item => item.type === this.selectedCategory.id);
      console.log(`筛选${this.selectedCategory.id}类别:`, filteredItems.length);
    }

    return filteredItems;
  }

  // 更新显示的物品列表
  updateDisplayItems() {
    // 重新从GameStateManager获取最新数据
    this.items = game.gameStateManager.getItems() || [];
    this.equipments = game.gameStateManager.getEquipments() || [];

    console.log('更新显示物品 - 当前分类:', this.selectedCategory.name);
    console.log('物品数量:', this.items.length, '装备数量:', this.equipments.length);

    const filteredItems = this.filterItemsByCategory();
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, filteredItems.length);

    this.displayItems = filteredItems.slice(startIndex, endIndex);
    console.log(`当前页面(${this.currentPage + 1})显示物品数量:`, this.displayItems.length);

    // 更新翻页按钮状态
    this.prevPageButton.isEnabled = this.currentPage > 0;

    const totalPages = Math.ceil(filteredItems.length / this.itemsPerPage);
    this.nextPageButton.isEnabled = this.currentPage < totalPages - 1;
  }

  // 场景显示时的回调
  onShow(params) {
    console.log('BackpackScene.onShow() 打开背包页面');

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 4;

    // 每次显示时重新获取物品和装备
    this.items = game.gameStateManager.getItems() || [];
    this.equipments = game.gameStateManager.getEquipments() || [];

    // 记录日志，用于调试
    console.log('背包页面获取到物品数量:', this.items.length);
    console.log('背包页面获取到装备数量:', this.equipments.length);

    // 确保默认选择第一个分类（全部）
    this.selectedCategory = this.categories[0];
    this.currentPage = 0;

    // 如果传入了指定分类，则选择该分类
    if (params && params.category) {
      const categoryToSelect = this.categories.find(c => c.id === params.category);
      if (categoryToSelect) {
        console.log('选中指定分类:', categoryToSelect.name);
        this.selectedCategory = categoryToSelect;
      }
    }

    // 更新显示的物品
    this.updateDisplayItems();

    // 设置为可见
    this.visible = true;
  }

  // 场景隐藏时的回调
  onHide() {
    console.log('BackpackScene.onHide() 关闭背包页面');

    // 清空UI元素
    this.clearUIElements();

    // 设置为不可见
    this.visible = false;
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    // 检查是否点击了物品
    const item = this.getItemAtPosition(x, y);
    if (item) {
      this.showItemDetail(item);
      return true;
    }

    return false;
  }

  // 获取指定位置的物品
  getItemAtPosition(x, y) {
    // 顶部导航栏和分类按钮的高度
    const headerHeight = 120;

    // 底部导航栏高度
    const tabBarHeight = 60;

    // 翻页按钮高度
    const pageButtonHeight = 40;

    // 可用高度
    const availableHeight = this.screenHeight - headerHeight - tabBarHeight - pageButtonHeight - 20;

    // 物品格子尺寸
    const gridSize = Math.min(this.screenWidth / 4, availableHeight / 3);

    // 计算行列
    const row = Math.floor((y - headerHeight) / gridSize);
    const col = Math.floor(x / gridSize);

    // 计算索引
    const index = row * 4 + col;

    // 检查索引是否有效
    if (index >= 0 && index < this.displayItems.length) {
      return this.displayItems[index];
    }

    return null;
  }

  // 显示物品详情
  showItemDetail(item) {
    console.log('显示物品详情:', item.name);

    // 构建显示的详情内容
    let title = item.name;
    let content = '';

    // 显示装备信息
    if (this.isEquipment(item)) {
      // 显示品质和类型
      const qualityNames = {
        'common': '普通',
        'uncommon': '优秀',
        'rare': '精良',
        'epic': '史诗',
        'legendary': '传说',
        0: '普通',
        1: '优秀',
        2: '精良',
        3: '史诗',
        4: '传说'
      };

      const typeName = this.getEquipmentTypeName(item.type);
      const qualityName = qualityNames[item.quality] || '未知';

      content += `品质: ${qualityName}\n`;
      content += `类型: ${typeName}\n`;
      content += `等级: ${item.level || 1}\n\n`;

      // 显示装备属性
      content += `【装备属性】\n`;
      if (item.attributes) {
        for (const [attr, value] of Object.entries(item.attributes)) {
          switch(attr) {
            case 'hp':
              content += `生命值: +${value}\n`;
              break;
            case 'attack':
              content += `攻击力: +${value}\n`;
              break;
            case 'defense':
              content += `防御力: +${value}\n`;
              break;
            case 'speed':
              content += `速度: +${value}\n`;
              break;
            case 'critRate':
              content += `暴击率: +${(value * 100).toFixed(1)}%\n`;
              break;
            case 'critDamage':
              content += `暴击伤害: +${(value * 100).toFixed(1)}%\n`;
              break;
            case 'penetration':
              content += `穿透: +${value}\n`;
              break;
            case 'daoRule':
              content += `道韵: +${value}\n`;
              break;
            default:
              content += `${attr}: +${value}\n`;
          }
        }
      } else {
        content += "无属性加成\n";
      }

      // 显示装备描述
      if (item.description) {
        content += `\n【描述】\n${item.description}\n`;
      }
    }
    // 显示普通物品信息
    else {
      content += `类型: ${item.type || '普通物品'}\n`;
      if (item.count && item.count > 1) {
        content += `数量: ${item.count}\n`;
      }

      if (item.description) {
        content += `\n【描述】\n${item.description}\n`;
      }

      if (item.effect) {
        content += `\n【效果】\n`;
        for (const [effect, value] of Object.entries(item.effect)) {
          content += `${effect}: ${value}\n`;
        }
      }
    }

    // 显示物品详情对话框
    this.showDialog(title, content);
  }

  // 显示对话框
  showDialog(title, message) {
    // 设置对话框状态
    this.dialogTitle = title;
    this.dialogMessage = message;
    this.showingDialog = true;

    // 备份现有UI元素
    this.backupUIElements = [...this.uiElements];

    // 清除当前UI
    this.clearUIElements();

    // 创建确定按钮
    const okButton = new Button(
      this.ctx,
      this.screenWidth / 2 - 50,
      this.screenHeight - 100,
      100,
      40,
      '确定',
      null,
      null,
      () => {
        // 关闭对话框
        this.showingDialog = false;
        this.dialogTitle = null;
        this.dialogMessage = null;

        // 恢复UI元素
        this.uiElements = this.backupUIElements;
        this.backupUIElements = null;
      }
    );

    // 添加确定按钮
    this.addUIElement(okButton);
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 如果显示对话框，绘制对话框
    if (this.showingDialog && this.dialogTitle && this.dialogMessage) {
      this.drawDialog();
      return; // 对话框显示时不绘制其他内容
    }

    // 绘制分类按钮
    this.drawCategories();

    // 绘制物品列表
    this.drawItems();

    // 绘制底部导航栏
    this.drawTabBar();

    // 注意：顶部导航栏现在由TitleBar组件处理
  }

  // 绘制背景
  drawBackground() {
    // 使用渐变色背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#2c3e50');
    gradient.addColorStop(1, '#1a1a1a');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    // 该方法已被TitleBar组件替代，不再需要执行任何操作
  }

  // 绘制分类按钮
  drawCategories() {
    const categoryButtonWidth = this.screenWidth / this.categories.length;
    const categoryButtonHeight = 40;
    const categoryButtonY = 80; // 顶部导航栏下方

    // 绘制分类按钮背景
    this.ctx.fillStyle = 'rgba(30, 30, 30, 0.8)';
    this.ctx.fillRect(0, categoryButtonY, this.screenWidth, categoryButtonHeight);

    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;

    for (let i = 1; i < this.categories.length; i++) {
      const x = categoryButtonWidth * i;
      this.ctx.beginPath();
      this.ctx.moveTo(x, categoryButtonY);
      this.ctx.lineTo(x, categoryButtonY + categoryButtonHeight);
      this.ctx.stroke();
    }

    // 绘制选中分类的高亮背景
    const selectedIndex = this.categories.findIndex(c => c.id === this.selectedCategory.id);
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(
      selectedIndex * categoryButtonWidth,
      categoryButtonY,
      categoryButtonWidth,
      categoryButtonHeight
    );

    // 绘制分类名称
    this.categories.forEach((category, index) => {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = index === selectedIndex ? '#ffffff' : '#a0a0a0';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(
        category.name,
        index * categoryButtonWidth + categoryButtonWidth / 2,
        categoryButtonY + categoryButtonHeight / 2
      );
    });
  }

  // 绘制物品列表
  drawItems() {
    // 顶部导航栏和分类按钮的高度
    const headerHeight = 120;

    // 底部导航栏高度
    const tabBarHeight = 60;

    // 翻页按钮高度
    const pageButtonHeight = 40;

    // 可用高度
    const availableHeight = this.screenHeight - headerHeight - tabBarHeight - pageButtonHeight - 20;

    // 物品格子尺寸
    const gridSize = Math.min(this.screenWidth / 4, availableHeight / 3);

    // 没有物品时显示提示
    if (this.displayItems.length === 0) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(
        '没有物品',
        this.screenWidth / 2,
        headerHeight + availableHeight / 2
      );
      return;
    }

    // 绘制物品格子
    this.displayItems.forEach((item, index) => {
      // 计算行列
      const col = index % 4;
      const row = Math.floor(index / 4);

      // 计算位置
      const x = col * gridSize;
      const y = headerHeight + row * gridSize;

      // 绘制物品格子背景
      this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
      this.ctx.fillRect(x + 5, y + 5, gridSize - 10, gridSize - 10);

      // 绘制物品边框
      this.ctx.strokeStyle = this.getItemBorderColor(item);
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(x + 5, y + 5, gridSize - 10, gridSize - 10);

      // 绘制物品图标（如果有）
      // 这里简单用颜色块表示不同类型的物品
      this.ctx.fillStyle = this.getItemColor(item);
      this.ctx.fillRect(x + 15, y + 15, gridSize - 30, gridSize - 30);

      // 绘制物品名称
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(
        item.name,
        x + gridSize / 2,
        y + gridSize - 40
      );

      // 如果是装备，绘制装备等级和类型
      if (this.isEquipment(item)) {
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#cccccc';
        this.ctx.fillText(
          `Lv.${item.level} ${this.getEquipmentTypeName(item.type)}`,
          x + gridSize / 2,
          y + gridSize - 20
        );
      }

      // 如果是可堆叠物品，绘制数量
      if (item.count && item.count > 1) {
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(
          `x${item.count}`,
          x + gridSize - 10,
          y + gridSize - 10
        );
      }
    });

    // 绘制翻页信息
    const totalItems = this.filterItemsByCategory().length;
    const totalPages = Math.ceil(totalItems / this.itemsPerPage);

    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(
      `${this.currentPage + 1}/${totalPages} 页`,
      this.screenWidth / 2,
      this.screenHeight - tabBarHeight - pageButtonHeight / 2 - 10
    );
  }

  // 判断物品是否为装备
  isEquipment(item) {
    return item && (
      item.type === 'weapon' ||
      item.type === 'armor' ||
      item.type === 'helmet' ||
      item.type === 'necklace' ||
      item.type === 'ring' ||
      item.type === 'boots'
    );
  }

  // 获取装备类型名称
  getEquipmentTypeName(type) {
    const typeNames = {
      'weapon': '武器',
      'armor': '护甲',
      'helmet': '头盔',
      'necklace': '项链',
      'ring': '戒指',
      'boots': '靴子'
    };

    return typeNames[type] || type;
  }

  // 获取物品边框颜色
  getItemBorderColor(item) {
    // 如果是装备类物品
    if (item.quality) {
      switch (item.quality) {
        case 'common':
          return '#ffffff';
        case 'uncommon':
          return '#1eff00';
        case 'rare':
          return '#0070dd';
        case 'epic':
          return '#a335ee';
        case 'legendary':
          return '#ff8000';
        default:
          return '#ffffff';
      }
    }

    // 普通物品
    return '#ffffff';
  }

  // 获取物品填充颜色
  getItemColor(item) {
    // 如果是装备类物品
    if (this.isEquipment(item)) {
      // 根据装备类型返回不同的颜色
      switch (item.type) {
        case 'weapon':
          return '#ff4500'; // 武器 - 红色
        case 'armor':
          return '#4169e1'; // 护甲 - 蓝色
        case 'helmet':
          return '#9932cc'; // 头盔 - 紫色
        case 'necklace':
          return '#ffd700'; // 项链 - 金色
        case 'ring':
          return '#00bfff'; // 戒指 - 天蓝色
        case 'boots':
          return '#32cd32'; // 靴子 - 绿色
        default:
          return '#6495ed'; // 默认装备 - 蓝色
      }
    }

    // 根据物品类型返回不同颜色
    switch (item.type) {
      case 'consumable':
        return '#ff6347'; // 红色
      case 'material':
        return '#32cd32'; // 绿色
      case 'quest':
        return '#ffd700'; // 金色
      case 'currency':
        return '#ff8c00'; // 橙色
      default:
        return '#a0a0a0'; // 灰色
    }
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;

    // 绘制底部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, tabBarY, this.screenWidth, tabBarHeight);

    // 绘制分割线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;

    for (let i = 1; i < 5; i++) {
      const x = this.screenWidth * i / 5;
      this.ctx.beginPath();
      this.ctx.moveTo(x, tabBarY);
      this.ctx.lineTo(x, this.screenHeight);
      this.ctx.stroke();
    }

    // 绘制选中项的高亮背景
    const tabWidth = this.screenWidth / 5;
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    this.ctx.fillRect(this.selectedTabIndex * tabWidth, tabBarY, tabWidth, tabBarHeight);

    // 导航栏文字
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];

    // 绘制导航栏图标和文字
    for (let i = 0; i < 5; i++) {
      const tabX = i * tabWidth + tabWidth / 2;

      // 绘制图标
      const iconSize = 30;
      const iconY = tabBarY + 10;

      // 如果有图标资源，绘制图标
      const iconKey = `tabIcon${i + 1}`;
      if (this.resources && this.resources[iconKey]) {
        this.ctx.drawImage(
          this.resources[iconKey],
          tabX - iconSize / 2,
          iconY,
          iconSize,
          iconSize
        );
      } else {
        // 如果没有图标资源，绘制简单的占位符
        this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
        this.ctx.beginPath();
        this.ctx.arc(tabX, iconY + iconSize / 2, iconSize / 2, 0, Math.PI * 2);
        this.ctx.fill();
      }

      // 绘制文字
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = i === this.selectedTabIndex ? '#ffffff' : '#a0a0a0';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tabTexts[i], tabX, tabBarY + 50);
    }
  }

  // 绘制对话框
  drawDialog() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 对话框尺寸
    const dialogWidth = this.screenWidth * 0.8;
    const dialogHeight = this.screenHeight * 0.7;
    const dialogX = (this.screenWidth - dialogWidth) / 2;
    const dialogY = (this.screenHeight - dialogHeight) / 2;

    // 绘制对话框背景
    this.ctx.fillStyle = '#2c3e50';
    this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);

    // 绘制对话框边框
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);

    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(this.dialogTitle, this.screenWidth / 2, dialogY + 20);

    // 绘制分隔线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(dialogX + 20, dialogY + 60);
    this.ctx.lineTo(dialogX + dialogWidth - 20, dialogY + 60);
    this.ctx.stroke();

    // 绘制内容
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';

    // 多行文本渲染
    const lines = this.dialogMessage.split('\n');
    const lineHeight = 20;
    let y = dialogY + 80;

    for (let i = 0; i < lines.length; i++) {
      if (y + lineHeight > dialogY + dialogHeight - 60) break; // 防止超出对话框
      this.ctx.fillText(lines[i], dialogX + 30, y);
      y += lineHeight;
    }
  }
}

export default BackpackScene;