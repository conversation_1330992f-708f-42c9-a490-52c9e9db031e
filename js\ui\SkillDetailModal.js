/**
 * 技能详情弹窗组件
 * 显示技能的详细信息，包括升级和装备功能
 */
import Button from './Button';
import game from '../../game';

class SkillDetailModal {
  constructor(ctx, screenWidth, screenHeight) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;

    // 弹窗状态
    this.visible = false;
    this.skill = null;
    this.character = null;
    this.onClose = null;
    this.onEquip = null;
    this.onUpgrade = null;

    // UI元素
    this.buttons = [];

    // 弹窗尺寸
    this.modalWidth = Math.min(350, screenWidth - 40);
    this.modalHeight = Math.min(500, screenHeight - 100);
    this.modalX = (screenWidth - this.modalWidth) / 2;
    this.modalY = (screenHeight - this.modalHeight) / 2;
  }

  /**
   * 显示技能详情弹窗
   * @param {Object} skill 技能对象
   * @param {Object} character 角色对象
   * @param {Function} onClose 关闭回调
   * @param {Function} onEquip 装备回调
   * @param {Function} onUpgrade 升级回调
   */
  show(skill, character, onClose, onEquip, onUpgrade) {
    this.skill = skill;
    this.character = character;
    this.onClose = onClose;
    this.onEquip = onEquip;
    this.onUpgrade = onUpgrade;
    this.visible = true;

    // 创建按钮
    this.createButtons();
  }

  /**
   * 隐藏弹窗
   */
  hide() {
    this.visible = false;
    this.skill = null;
    this.character = null;
    this.buttons = [];
  }

  /**
   * 创建按钮
   */
  createButtons() {
    this.buttons = [];

    const buttonWidth = 80;
    const buttonHeight = 35;
    const buttonSpacing = 10;
    const buttonsY = this.modalY + this.modalHeight - 60;

    // 关闭按钮
    this.buttons.push(new Button(
      this.ctx,
      this.modalX + 20,
      buttonsY,
      buttonWidth,
      buttonHeight,
      '关闭',
      null,
      null,
      () => {
        this.hide();
        if (this.onClose) this.onClose();
      }
    ));

    // 装备按钮
    if (this.skill && (this.skill.type === 'normalAttack' || this.skill.type === 'activeSkill')) {
      this.buttons.push(new Button(
        this.ctx,
        this.modalX + 20 + buttonWidth + buttonSpacing,
        buttonsY,
        buttonWidth,
        buttonHeight,
        '装备',
        null,
        null,
        () => {
          if (this.onEquip) this.onEquip(this.skill);
        }
      ));
    }

    // 升级按钮
    if (this.skill && this.skill.level < this.skill.maxLevel) {
      this.buttons.push(new Button(
        this.ctx,
        this.modalX + this.modalWidth - buttonWidth - 20,
        buttonsY,
        buttonWidth,
        buttonHeight,
        '升级',
        null,
        null,
        () => {
          this.upgradeSkill();
        }
      ));
    }
  }

  /**
   * 升级技能
   */
  upgradeSkill() {
    if (!this.skill) return;

    const player = game.gameStateManager.getPlayer();
    const availableLianlidian = player.resources.lianlidian || 0;

    // 检查是否可以升级
    const upgradeCheck = this.skill.canUpgradeBattleSkill(availableLianlidian, false);
    
    if (!upgradeCheck.canUpgrade) {
      alert(upgradeCheck.reason);
      return;
    }

    // 扣除历练点
    const success = game.gameStateManager.addResource('lianlidian', -upgradeCheck.cost);
    if (!success) {
      alert('历练点不足');
      return;
    }

    // 升级技能
    const upgraded = this.skill.upgradeBattleSkill();
    if (upgraded) {
      // 保存游戏状态
      game.gameStateManager.saveGameState();
      
      // 重新创建按钮（可能升级按钮需要更新）
      this.createButtons();
      
      // 调用升级回调
      if (this.onUpgrade) this.onUpgrade(this.skill);
      
      alert(`技能升级成功！当前等级：${this.skill.level}`);
    } else {
      // 升级失败，退还历练点
      game.gameStateManager.addResource('lianlidian', upgradeCheck.cost);
      alert('技能升级失败');
    }
  }

  /**
   * 处理触摸事件
   * @param {string} eventType 事件类型
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @returns {boolean} 是否处理了事件
   */
  handleTouch(eventType, x, y) {
    if (!this.visible) return false;

    if (eventType === 'touchstart') {
      // 检查是否点击了按钮
      for (const button of this.buttons) {
        if (button.isPointInside(x, y)) {
          button.onClick();
          return true;
        }
      }

      // 检查是否点击了弹窗外部（关闭弹窗）
      if (x < this.modalX || x > this.modalX + this.modalWidth ||
          y < this.modalY || y > this.modalY + this.modalHeight) {
        this.hide();
        if (this.onClose) this.onClose();
        return true;
      }
    }

    return true; // 阻止事件传播
  }

  /**
   * 绘制弹窗
   */
  render() {
    if (!this.visible || !this.skill) return;

    // 绘制遮罩层
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制弹窗背景
    this.drawModalBackground();

    // 绘制技能信息
    this.drawSkillInfo();

    // 绘制按钮
    this.drawButtons();
  }

  /**
   * 绘制弹窗背景
   */
  drawModalBackground() {
    // 绘制阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.drawRoundedRect(this.modalX + 3, this.modalY + 3, this.modalWidth, this.modalHeight, 15);
    this.ctx.fill();

    // 绘制背景渐变
    const gradient = this.ctx.createLinearGradient(this.modalX, this.modalY, this.modalX, this.modalY + this.modalHeight);
    gradient.addColorStop(0, 'rgba(40, 40, 80, 0.95)');
    gradient.addColorStop(1, 'rgba(20, 20, 40, 0.95)');
    
    this.ctx.fillStyle = gradient;
    this.drawRoundedRect(this.modalX, this.modalY, this.modalWidth, this.modalHeight, 15);
    this.ctx.fill();

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.8)';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
  }

  /**
   * 绘制技能信息
   */
  drawSkillInfo() {
    const padding = 20;
    const startX = this.modalX + padding;
    const startY = this.modalY + padding;
    const contentWidth = this.modalWidth - padding * 2;

    // 绘制技能名称
    this.ctx.font = 'bold 20px Arial';
    this.ctx.fillStyle = this.skill.getQualityColor();
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.skill.name, this.modalX + this.modalWidth / 2, startY + 25);

    // 绘制技能等级
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillText(`等级 ${this.skill.level}/${this.skill.maxLevel}`, this.modalX + this.modalWidth / 2, startY + 50);

    // 绘制技能类型
    this.ctx.font = '14px Arial';
    this.ctx.fillStyle = '#87CEEB';
    const typeText = this.skill.type === 'normalAttack' ? '普通攻击' : 
                    this.skill.type === 'activeSkill' ? '主动技能' : '被动技能';
    this.ctx.fillText(typeText, this.modalX + this.modalWidth / 2, startY + 70);

    // 绘制技能描述
    const description = this.skill.getBattleSkillDetailedDescription();
    this.drawMultilineText(description, startX, startY + 100, contentWidth, 16, '#FFFFFF');

    // 绘制历练点信息
    const player = game.gameStateManager.getPlayer();
    const availableLianlidian = player.resources.lianlidian || 0;
    
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillStyle = '#90EE90';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`当前历练点: ${availableLianlidian}`, this.modalX + this.modalWidth / 2, this.modalY + this.modalHeight - 90);
  }

  /**
   * 绘制按钮
   */
  drawButtons() {
    for (const button of this.buttons) {
      button.render();
    }
  }

  /**
   * 绘制多行文本
   */
  drawMultilineText(text, x, y, maxWidth, lineHeight, color) {
    this.ctx.font = '12px Arial';
    this.ctx.fillStyle = color;
    this.ctx.textAlign = 'left';

    const lines = text.split('\n');
    let currentY = y;

    for (const line of lines) {
      if (currentY > this.modalY + this.modalHeight - 120) break; // 避免超出弹窗
      
      this.ctx.fillText(line, x, currentY);
      currentY += lineHeight;
    }
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
  }
}

export default SkillDetailModal;
