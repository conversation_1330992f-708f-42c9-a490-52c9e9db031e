/**
 * 标题栏组件
 * 显示玩家信息、资源状态，支持头像点击交互
 */
import AppContext from '../utils/AppContext.js';

class TitleBar {
  constructor(ctx, x, y, width, height, resources) {
    this.ctx = ctx;
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.resources = resources || {};
    
    // 头像区域
    this.avatarSize = Math.min(60, this.height - 10);
    this.avatarX = this.x + 10;
    this.avatarY = this.y + (this.height - this.avatarSize) / 2;
    
    // 是否启用交互
    this.interactive = true;
    
    // 加载头像
    this.avatarImage = null;
    this.triedLoadingAvatar = false; // 添加标记，防止重复尝试加载
    
    // 不在构造函数中立即加载头像，而是等待页面显示时加载
    // this.loadAvatarImage(); - 移除此行
  }
  
  /**
   * 加载玩家微信头像 - 只加载已有的头像，不主动请求
   */
  loadAvatarImage() {
    // 如果已经尝试过加载，不再重复尝试
    if (this.triedLoadingAvatar) {
      return;
    }
    
    this.triedLoadingAvatar = true;
    console.log('TitleBar尝试加载本地存储的玩家头像');
    
    try {
      // 获取玩家信息
      const player = AppContext.game.gameStateManager.getPlayer();
      
      if (player) {
        // 尝试从不同可能的字段获取头像URL
        const avatarUrl = player.avatarUrl || player.avatar || 
                         (player.userInfo ? player.userInfo.avatarUrl : null);
        
        if (avatarUrl) {
          // 创建图片对象
          this.avatarImage = wx.createImage();
          this.avatarImage.src = avatarUrl;
          console.log('开始加载头像:', avatarUrl);
          
          // 添加加载事件处理
          this.avatarImage.onload = () => {
            console.log('头像加载成功');
          };
          
          this.avatarImage.onerror = (e) => {
            console.error('头像加载失败:', e);
            this.avatarImage = null; // 重置图像，使用默认头像
            // 不自动尝试获取微信头像
            // this.tryGetWechatUserInfo(); - 移除此行
          };
        } else {
          console.log('未找到本地存储的玩家头像URL，将使用默认头像');
          // 不自动尝试获取微信头像
          // this.tryGetWechatUserInfo(); - 移除此行
        }
      } else {
        console.warn('无法获取玩家信息，将使用默认头像');
      }
    } catch (error) {
      console.error('加载头像失败:', error);
    }
  }
  
  /**
   * 主动更新用户信息，用于登录成功后调用
   * @param {Object} userInfo 用户信息对象
   */
  updateUserInfo(userInfo) {
    if (!userInfo) return;
    
    console.log('更新TitleBar用户信息:', userInfo);
    
    // 创建图片对象
    if (userInfo.avatarUrl) {
      this.avatarImage = wx.createImage();
      this.avatarImage.src = userInfo.avatarUrl;
      
      // 添加加载事件处理
      this.avatarImage.onload = () => {
        console.log('用户新头像加载成功');
      };
      
      this.avatarImage.onerror = (e) => {
        console.error('用户新头像加载失败:', e);
        this.avatarImage = null;
      };
    }
    
    // 更新玩家信息到GameStateManager
    const player = AppContext.game.gameStateManager.getPlayer();
    if (player) {
      player.avatarUrl = userInfo.avatarUrl || player.avatarUrl;
      player.nickname = userInfo.nickName || player.nickname;
      player.userInfo = userInfo;
      
      // 保存更新
      AppContext.game.gameStateManager.setPlayer(player);
      console.log('玩家信息已更新:', player.nickname);
    }
  }
  
  /**
   * 尝试从微信API获取用户信息（只在用户点击登录时调用）
   */
  tryGetWechatUserInfo() {
    if (typeof wx === 'undefined' || !wx.getUserInfo) {
      console.warn('微信API不可用，无法获取用户信息');
      return;
    }
    
    console.log('尝试从微信API获取用户信息');
    
    // 尝试直接获取用户信息
    wx.getUserInfo({
      success: (res) => {
        console.log('获取微信用户信息成功:', res);
        if (res.userInfo) {
          this.updateUserInfo(res.userInfo);
        }
      },
      fail: (err) => {
        console.warn('获取微信用户信息失败:', err);
        this.showAuthButton();
      }
    });
  }
  
  /**
   * 显示授权按钮
   */
  showAuthButton() {
    if (typeof wx === 'undefined' || !wx.createUserInfoButton) {
      console.warn('微信API不可用，无法创建授权按钮');
      return;
    }
    
    console.log('创建用户信息授权按钮');
    
    // 创建获取用户信息按钮
    const button = wx.createUserInfoButton({
      type: 'text',
      text: '点击获取头像',
      style: {
        left: this.avatarX,
        top: this.avatarY,
        width: this.avatarSize,
        height: this.avatarSize,
        backgroundColor: '#3498db',
        color: '#ffffff',
        textAlign: 'center',
        fontSize: 12,
        borderRadius: this.avatarSize / 2
      }
    });
    
    button.onTap((res) => {
      if (res.userInfo) {
        console.log('用户授权成功，获取到用户信息:', res.userInfo);
        this.updateUserInfo(res.userInfo);
        
        // 隐藏按钮
        button.destroy();
      } else {
        console.warn('用户拒绝授权');
      }
    });
  }
  
  /**
   * 检查点是否在组件内
   * @param {number} x 点的X坐标
   * @param {number} y 点的Y坐标
   * @returns {boolean} 是否在组件内
   */
  isPointInside(x, y) {
    // 检查是否在头像区域内（用于弹出修改昵称对话框）
    if (x >= this.avatarX && x <= this.avatarX + this.avatarSize &&
        y >= this.avatarY && y <= this.avatarY + this.avatarSize) {
      return true;
    }
    
    // 检查是否在整个标题栏内
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.height;
  }
  
  /**
   * 触摸开始事件
   * @param {number} x 触摸点X坐标
   * @param {number} y 触摸点Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchStart(x, y) {
    return false; // 不消费事件
  }
  
  /**
   * 触摸移动事件
   * @param {number} x 触摸点X坐标
   * @param {number} y 触摸点Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchMove(x, y) {
    return false; // 不消费事件
  }
  
  /**
   * 触摸结束事件
   * @param {number} x 触摸点X坐标
   * @param {number} y 触摸点Y坐标
   * @returns {boolean} 是否处理了事件
   */
  onTouchEnd(x, y) {
    // 检查是否点击了头像区域
    if (this.interactive && 
        x >= this.avatarX && x <= this.avatarX + this.avatarSize &&
        y >= this.avatarY && y <= this.avatarY + this.avatarSize) {
      // 弹出修改昵称对话框
      this.showEditNicknameDialog();
      return true;
    }
    
    return false;
  }
  
  /**
   * 显示修改昵称对话框
   */
  showEditNicknameDialog() {
    if (!wx || !wx.showModal) {
      console.error('wx.showModal不可用，无法显示对话框');
      return;
    }
    
    const player = AppContext.game.gameStateManager.getPlayer();
    
    // 弹出修改昵称对话框
    wx.showModal({
      title: '修改昵称',
      content: '请输入新的游戏昵称',
      editable: true,
      placeholderText: player ? (player.nickname || '修仙者') : '修仙者',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，获取输入的新昵称
          const newNickname = res.content || '修仙者';
          if (newNickname.trim()) {
            if (!player) {
              console.error('无法获取玩家信息，昵称修改失败');
              return;
            }
            
            // 更新昵称
            player.nickname = newNickname;
            
            // 保存玩家数据
            AppContext.game.gameStateManager.setPlayer(player);
            
            // 同步到云端
            if (AppContext.game.updateUserData) {
              AppContext.game.updateUserData();
            }
            
            console.log('昵称已更新为:', newNickname);
          }
        }
      }
    });
  }
  
  /**
   * 渲染组件
   */
  render() {
    // 获取玩家数据
    const player = AppContext.game.gameStateManager.getPlayer();
    
    // 绘制背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // 绘制头像框
    this.drawAvatarFrame();
    
    // 绘制玩家信息
    this.drawPlayerInfo(player || {});
    
    // 绘制资源信息
    this.drawResourceInfo(player || {});
  }
  
  /**
   * 绘制头像框和头像
   */
  drawAvatarFrame() {
    // 绘制头像框背景
    if (this.resources.avatarFrame) {
      try {
        this.ctx.drawImage(
          this.resources.avatarFrame,
          this.avatarX,
          this.avatarY,
          this.avatarSize,
          this.avatarSize
        );
      } catch (error) {
        console.error('绘制头像框失败:', error);
        this.drawDefaultAvatarFrame();
      }
    } else {
      this.drawDefaultAvatarFrame();
    }
    
    // 绘制玩家头像
    if (this.avatarImage) {
      try {
        // 使用圆形裁剪绘制头像
        this.ctx.save();
        this.ctx.beginPath();
        const centerX = this.avatarX + this.avatarSize / 2;
        const centerY = this.avatarY + this.avatarSize / 2;
        const radius = this.avatarSize / 2 - 5;
        this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        this.ctx.closePath();
        this.ctx.clip();
        
        this.ctx.drawImage(
          this.avatarImage,
          this.avatarX + 5,
          this.avatarY + 5,
          this.avatarSize - 10,
          this.avatarSize - 10
        );
        
        this.ctx.restore();
      } catch (error) {
        console.error('绘制头像失败:', error);
        this.drawDefaultAvatar();
      }
    } else {
      this.drawDefaultAvatar();
    }
  }
  
  /**
   * 绘制默认头像框
   */
  drawDefaultAvatarFrame() {
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    const centerX = this.avatarX + this.avatarSize / 2;
    const centerY = this.avatarY + this.avatarSize / 2;
    const radius = this.avatarSize / 2;
    this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    this.ctx.stroke();
  }
  
  /**
   * 绘制默认头像
   */
  drawDefaultAvatar() {
    // 获取玩家昵称首字
    const player = AppContext.game.gameStateManager.getPlayer();
    const nickname = player.nickname || '修';
    const firstChar = nickname.charAt(0);
    
    // 绘制圆形背景
    this.ctx.fillStyle = '#3498db';
    this.ctx.beginPath();
    const centerX = this.avatarX + this.avatarSize / 2;
    const centerY = this.avatarY + this.avatarSize / 2;
    const radius = this.avatarSize / 2 - 5;
    this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    this.ctx.fill();
    
    // 绘制文字
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = `bold ${Math.floor(this.avatarSize / 2)}px Arial`;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(firstChar, centerX, centerY);
  }
  
  /**
   * 绘制玩家信息
   * @param {Object} player 玩家数据
   */
  drawPlayerInfo(player) {
    const textX = this.avatarX + this.avatarSize + 10;
    const nameY = this.avatarY + 20;
    const levelY = this.avatarY + 45;
    
    // 绘制玩家昵称
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(player.nickname || '修仙者', textX, nameY);
    
    // 绘制洞府等级
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.fillText(`洞府等级: ${player.dongfuLevel || 1}`, textX, levelY);
  }
  
  /**
   * 绘制资源信息
   * @param {Object} player 玩家数据
   */
  drawResourceInfo(player) {
    const resources = player.resources || { xianyu: 0, lingshi: 0 };
    const iconSize = 20;
    const resourceY = this.y + this.height / 2;
    
    // 资源起始位置（屏幕中部）
    const startX = this.x + this.width / 2;
    
    // 绘制仙玉
    if (this.resources.iconXianyu) {
      try {
        this.ctx.drawImage(
          this.resources.iconXianyu,
          startX,
          resourceY - iconSize / 2,
          iconSize,
          iconSize
        );
      } catch (error) {
        console.error('绘制仙玉图标失败:', error);
        this.drawDefaultResourceIcon(startX, resourceY, iconSize, 'gold');
      }
    } else {
      // 如果没有图标资源，绘制简单的圆形
      this.drawDefaultResourceIcon(startX, resourceY, iconSize, 'gold');
    }
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${resources.xianyu || 0}`, startX + iconSize + 5, resourceY + 5);
    
    // 绘制灵石
    const lingshiX = startX + 120;
    
    if (this.resources.iconLingshi) {
      try {
        this.ctx.drawImage(
          this.resources.iconLingshi,
          lingshiX,
          resourceY - iconSize / 2,
          iconSize,
          iconSize
        );
      } catch (error) {
        console.error('绘制灵石图标失败:', error);
        this.drawDefaultResourceIcon(lingshiX, resourceY, iconSize, 'silver');
      }
    } else {
      // 如果没有图标资源，绘制简单的方形
      this.drawDefaultResourceIcon(lingshiX, resourceY, iconSize, 'silver');
    }
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${resources.lingshi || 0}`, lingshiX + iconSize + 5, resourceY + 5);
  }
  
  /**
   * 绘制默认资源图标
   */
  drawDefaultResourceIcon(x, y, size, type) {
    if (type === 'gold') {
      this.ctx.fillStyle = '#ffd700';
      this.ctx.beginPath();
      this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
      this.ctx.fill();
    } else {
      this.ctx.fillStyle = '#c0c0c0';
      this.ctx.fillRect(x, y - size / 2, size, size);
    }
  }
  
  /**
   * 更新组件
   */
  update() {
    // 如果还没有尝试加载头像，现在加载
    if (!this.triedLoadingAvatar) {
      this.loadAvatarImage();
    }
  }
}

export default TitleBar; 