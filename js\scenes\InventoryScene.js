/**
 * 背包场景类
 * 展示所有道具
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class InventoryScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 道具列表
    this.items = [];
    
    // 页面状态
    this.pageTitle = '背包';
  }
  
  // 初始化UI
  initUI() {
    // 尝试获取资源
    this.resources = game.resourceLoader.resources;
    
    // 创建返回按钮
    const backButtonSize = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      this.screenWidth - backButtonSize - margin,
      this.screenHeight - backButtonSize - margin,
      backButtonSize,
      backButtonSize,
      '返回',
      null,
      null,
      () => {
        // 返回主场景
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 初始化UI
    this.initUI();
    
    // 获取道具列表
    this.items = game.gameStateManager.getInventory();
    
    // 设置场景为可见状态
    this.visible = true;
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
  }
  
  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    // 检查是否点击了道具
    const item = this.getItemAtPosition(x, y);
    if (item) {
      // 显示道具详情
      this.showItemDetail(item);
      return true;
    }
    
    return false;
  }
  
  // 获取指定位置的道具
  getItemAtPosition(x, y) {
    // 顶部导航栏高度
    const headerHeight = 80;
    
    // 道具卡片尺寸
    const cardWidth = this.screenWidth / 4;
    const cardHeight = 120;
    
    // 计算行列
    const row = Math.floor((y - headerHeight) / cardHeight);
    const col = Math.floor(x / cardWidth);
    
    // 计算索引
    const index = row * 4 + col;
    
    // 检查索引是否有效
    if (index >= 0 && index < this.items.length) {
      return this.items[index];
    }
    
    return null;
  }
  
  // 显示道具详情
  showItemDetail(item) {
    // 切换到道具详情场景
    this.sceneManager.showScene('itemDetail', { itemId: item.id });
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制顶部导航栏
    this.drawHeader();
    
    // 绘制道具列表
    this.drawItemList();
  }
  
  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    
    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);
    
    // 绘制页面标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 8);
    
    // 绘制资源信息
    const player = game.gameStateManager.getPlayer();
    const resourceY = headerHeight / 2;
    const iconSize = 20;
    
    // 绘制仙玉
    this.drawResourceIcon('iconXianyu', this.screenWidth / 2, resourceY, iconSize, '#ffd700');
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth / 2 + iconSize + 5, resourceY + 5);
    
    // 绘制灵石
    const lingshiX = this.screenWidth / 2 + 120;
    this.drawResourceIcon('iconLingshi', lingshiX, resourceY, iconSize, '#c0c0c0');
    
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`灵石: ${player.resources.lingshi}`, lingshiX + iconSize + 5, resourceY + 5);
  }
  
  // 绘制资源图标
  drawResourceIcon(iconKey, x, y, size, fallbackColor) {
    if (this.resources && this.resources[iconKey]) {
      try {
        this.ctx.drawImage(
          this.resources[iconKey],
          x,
          y - size / 2,
          size,
          size
        );
      } catch (error) {
        console.error(`绘制图标 ${iconKey} 失败`, error);
        this.drawDefaultIcon(x, y, size, fallbackColor);
      }
    } else {
      this.drawDefaultIcon(x, y, size, fallbackColor);
    }
  }
  
  // 绘制默认图标
  drawDefaultIcon(x, y, size, color) {
    // 如果是圆形图标
    if (color === '#ffd700') { // 仙玉用圆形
      this.ctx.fillStyle = color;
      this.ctx.beginPath();
      this.ctx.arc(x + size / 2, y, size / 2, 0, Math.PI * 2);
      this.ctx.fill();
    } else { // 灵石用方形
      this.ctx.fillStyle = color;
      this.ctx.fillRect(x, y - size / 2, size, size);
    }
  }
  
  // 绘制道具列表
  drawItemList() {
    // 顶部导航栏高度
    const headerHeight = 80;
    
    // 道具卡片尺寸
    const cardWidth = this.screenWidth / 4;
    const cardHeight = 120;
    const iconSize = 60;
    
    // 没有道具时显示提示
    if (!this.items.length) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('背包为空', this.screenWidth / 2, headerHeight + 100);
      return;
    }
    
    // 绘制道具卡片
    this.items.forEach((item, index) => {
      // 计算行列
      const row = Math.floor(index / 4);
      const col = index % 4;
      
      // 计算位置
      const x = col * cardWidth;
      const y = headerHeight + row * cardHeight;
      
      // 绘制卡片背景
      this.ctx.fillStyle = 'rgba(50, 50, 50, 0.7)';
      this.ctx.fillRect(x + 5, y + 5, cardWidth - 10, cardHeight - 10);
      
      // 绘制道具图标
      const iconX = x + (cardWidth - iconSize) / 2;
      const iconY = y + 15;
      
      // 如果有道具图片资源，绘制道具图片
      const itemImgKey = `item${item.id}`;
      if (this.resources && this.resources[itemImgKey]) {
        try {
          this.ctx.drawImage(
            this.resources[itemImgKey],
            iconX,
            iconY,
            iconSize,
            iconSize
          );
        } catch (error) {
          console.error(`绘制道具图标 ${itemImgKey} 失败`, error);
          this.drawDefaultItemIcon(item, iconX, iconY, iconSize);
        }
      } else {
        // 绘制默认道具图标
        this.drawDefaultItemIcon(item, iconX, iconY, iconSize);
      }
      
      // 绘制道具名称
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(
        this.truncateText(item.name, 8),
        x + cardWidth / 2,
        iconY + iconSize + 5
      );
      
      // 如果道具可以堆叠，绘制数量
      if (item.stackable && item.quantity > 1) {
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = '#ffff00';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(
          `x${item.quantity}`,
          iconX + iconSize - 5,
          iconY + iconSize - 5
        );
      }
    });
  }
  
  // 绘制默认道具图标
  drawDefaultItemIcon(item, x, y, size) {
    // 根据道具类型绘制不同颜色的方形
    let color = '#808080'; // 默认灰色
    
    switch (item.type) {
      case 'weapon':
        color = '#ff4500'; // 武器是红色
        break;
      case 'armor':
        color = '#4169e1'; // 防具是蓝色
        break;
      case 'consumable':
        color = '#32cd32'; // 消耗品是绿色
        break;
      case 'material':
        color = '#ffd700'; // 材料是金色
        break;
    }
    
    // 绘制方形背景
    this.ctx.fillStyle = color;
    this.ctx.fillRect(x, y, size, size);
    
    // 绘制道具类型首字母
    this.ctx.font = 'bold 30px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(
      item.type.charAt(0).toUpperCase(),
      x + size / 2,
      y + size / 2
    );
  }
  
  // 截断文本
  truncateText(text, maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 1) + '…';
  }
}

export default InventoryScene; 