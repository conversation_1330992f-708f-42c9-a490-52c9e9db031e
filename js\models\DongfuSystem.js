/**
 * 洞府系统模型
 * 管理洞府灵气和修炼相关逻辑
 */


class DongfuSystem {
  constructor() {
    // 洞府等级配置
    this.dongfuLevels = [
      {
        level: 1,
        name: '茅草屋',
        lingqiAmount: 20,
        description: '简陋的茅草屋，勉强能遮风挡雨。'
      },
      {
        level: 2,
        name: '木屋',
        lingqiAmount: 40,
        description: '稍微结实的木屋，能安心休息了。'
      },
      {
        level: 3,
        name: '石屋',
        lingqiAmount: 80,
        description: '坚固的石屋，不惧风雨侵袭。'
      },
      {
        level: 4,
        name: '小院',
        lingqiAmount: 160,
        description: '有了自己的小院子，可以种植一些灵草。'
      },
      {
        level: 5,
        name: '大院',
        lingqiAmount: 320,
        description: '宽敞的大院，开始聚集一些灵气。'
      },
      {
        level: 6,
        name: '小洞府',
        lingqiAmount: 640,
        description: '初具规模的洞府，有了稳定的灵气供应。'
      },
      {
        level: 7,
        name: '中型洞府',
        lingqiAmount: 1280,
        description: '洞府内灵气充沛，适合长期修炼。'
      },
      {
        level: 8,
        name: '大型洞府',
        lingqiAmount: 2560,
        description: '洞府已成规模，灵气浓度较高。'
      },
      {
        level: 9,
        name: '灵气洞府',
        lingqiAmount: 5120,
        description: '洞府中灵气如云，修炼事半功倍。'
      },
      {
        level: 10,
        name: '仙气洞府',
        lingqiAmount: 10240,
        description: '仙气缭绕的洞府，已接近仙家福地。'
      }
    ];

    // 修炼境界灵气吸收率配置
    this.cultivationAbsorptionRates = {};


    // 灵力丹配置
    this.lingliDanConfig = [
      { id: 'linglidan_small', name: '小灵力丹', lingliBonus: 100 },
      { id: 'linglidan_medium', name: '中灵力丹', lingliBonus: 500 },
      { id: 'linglidan_large', name: '大灵力丹', lingliBonus: 2000 }
    ];
  }

  /**
   * 初始化境界吸收率
   */
  

  /**
   * 获取洞府等级信息
   * @param {number} level 洞府等级
   * @returns {Object} 洞府等级信息
   */
  getDongfuLevelInfo(level) {
    return this.dongfuLevels.find(dongfu => dongfu.level === level) || this.dongfuLevels[0];
  }

  /**
   * 获取洞府灵气数量
   * @param {number} dongfuLevel 洞府等级
   * @returns {number} 洞府灵气数量
   */
  getDongfuLingqiAmount(dongfuLevel) {
    const dongfuInfo = this.getDongfuLevelInfo(dongfuLevel);
    return dongfuInfo.lingqiAmount;
  }

  /**
   * 获取修炼境界灵气吸收率
   * @param {string} cultivation 修炼境界
   * @returns {number} 灵气吸收率
   */
  getCultivationAbsorptionRate(cultivation) {
    return this.cultivationAbsorptionRates[cultivation] || 0.5;
  }

  /**
   * 计算修炼获得的灵力
   * @param {number} dongfuLevel 洞府等级
   * @param {string} cultivation 修炼境界
   * @param {number} vipBonus VIP加成
   * @returns {number} 获得的灵力
   */
  calculateLingliGain(dongfuLevel, cultivation, vipBonus = 0) {
    // 获取洞府灵气数量
    const lingqiAmount = this.getDongfuLingqiAmount(dongfuLevel);

    // 获取修炼境界灵气吸收率
    const absorptionRate = this.getCultivationAbsorptionRate(cultivation);

    // 计算基础灵力获得量
    const baseLingliGain = lingqiAmount * absorptionRate;

    // 应用VIP加成
    const finalLingliGain = Math.floor(baseLingliGain * (1 + vipBonus));

    return finalLingliGain;
  }

  /**
   * 获取升级洞府所需灵石
   * @param {number} currentLevel 当前洞府等级
   * @returns {number} 升级所需灵石
   */
  getUpgradeCost(currentLevel) {
    // 升级公式：当前等级 * 1000
    return currentLevel * 1000;
  }

  /**
   * 获取灵力丹信息
   * @param {string} danId 灵力丹ID
   * @returns {Object} 灵力丹信息
   */
  getLingliDanInfo(danId) {
    return this.lingliDanConfig.find(dan => dan.id === danId);
  }

  /**
   * 获取所有灵力丹信息
   * @returns {Array} 所有灵力丹信息
   */
  getAllLingliDan() {
    return this.lingliDanConfig;
  }

  
}

export default DongfuSystem;
