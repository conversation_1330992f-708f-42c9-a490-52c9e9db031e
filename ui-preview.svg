<?xml version="1.0" encoding="UTF-8"?>
<svg width="2250" height="812" viewBox="0 0 2250 812" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义全局样式 -->
  <defs>
    <!-- 定义阴影滤镜 -->
    <filter id="cardShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="2"/>
      <feComposite in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0"/>
    </filter>
    
    <!-- 定义图标系统 -->
    <symbol id="icon-home" viewBox="0 0 24 24">
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M9 22V12h6v10" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-character" viewBox="0 0 24 24">
      <circle cx="12" cy="8" r="4" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-dongfu" viewBox="0 0 24 24">
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M9 22V12h6v10" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-battle" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-bag" viewBox="0 0 24 24">
      <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" fill="none" stroke="currentColor" stroke-width="2"/>
      <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
      <path d="M16 10a4 4 0 0 1-8 0" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-skill" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>

    <!-- 定义渐变 -->
    <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
    </linearGradient>

    <!-- 定义装备图标 -->
    <symbol id="icon-weapon" viewBox="0 0 24 24">
      <path d="M7 2v2h3v2H7l-2 2v3h3v3H5v2h3v2h2v-2h3v2h2v-2h3v-2h-3v-3h3V8l-2-2h-3V4h3V2H7z" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-armor" viewBox="0 0 24 24">
      <path d="M12 2L2 7v10l10 5 10-5V7L12 2z" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-accessory" viewBox="0 0 24 24">
      <circle cx="12" cy="12" r="8" fill="none" stroke="currentColor" stroke-width="2"/>
      <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>

    <!-- 定义洞府图标 -->
    <symbol id="icon-meditation" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M12 6v6l4 2" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-resource" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>

    <!-- 定义试炼图标 -->
    <symbol id="icon-level" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
    
    <symbol id="icon-reward" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>

    <!-- 定义物品图标 -->
    <symbol id="icon-item" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>

    <!-- 定义功法图标 -->
    <symbol id="icon-gongfa" viewBox="0 0 24 24">
      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" fill="none" stroke="currentColor" stroke-width="2"/>
    </symbol>
  </defs>

  <!-- 背景 -->
  <rect width="2250" height="812" fill="#f5f5f5"/>

  <!-- 6个页面预览 -->
  <g transform="translate(0,0)">
    <!-- 主场景 -->
    <g transform="translate(0,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">修仙六道</text>
      
      <!-- 角色信息卡片 -->
      <g transform="translate(20,80)" filter="url(#cardShadow)">
        <rect width="334" height="120" rx="8" fill="white" stroke="#e0e0e0"/>
        <image x="20" y="20" width="80" height="80" href="https://source.unsplash.com/random/80x80/?avatar" clip-path="circle(40px at 40px 40px)"/>
        <text x="120" y="45" font-size="18" fill="#333" font-weight="bold">道号：玄天</text>
        <text x="120" y="75" font-size="16" fill="#666">境界：筑基期</text>
        <text x="120" y="105" font-size="16" fill="#666">战力：9999</text>
      </g>
      
      <!-- 修炼进度条 -->
      <g transform="translate(20,220)">
        <text x="0" y="20" font-size="16" fill="#333">修炼进度</text>
        <rect x="0" y="30" width="334" height="20" rx="10" fill="#e0e0e0"/>
        <rect x="0" y="30" width="234" height="20" rx="10" fill="url(#progressGradient)"/>
        <text x="167" y="45" text-anchor="middle" font-size="14" fill="white">70%</text>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#2196F3"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#666"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#666"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#666"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#666"/>
        </g>
      </g>
    </g>

    <!-- 角色场景 -->
    <g transform="translate(375,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">角色信息</text>
      
      <!-- 角色属性面板 -->
      <g transform="translate(20,80)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">基础属性</text>
        
        <!-- 属性列表 -->
        <g transform="translate(20,50)">
          <text x="0" y="0" font-size="16" fill="#666">生命值：1000/1000</text>
          <text x="0" y="30" font-size="16" fill="#666">灵力值：500/500</text>
          <text x="0" y="60" font-size="16" fill="#666">攻击力：100</text>
          <text x="0" y="90" font-size="16" fill="#666">防御力：50</text>
          <text x="0" y="120" font-size="16" fill="#666">速度：80</text>
        </g>
      </g>
      
      <!-- 装备栏 -->
      <g transform="translate(20,300)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">装备</text>
        
        <!-- 装备格子 -->
        <g transform="translate(20,50)">
          <!-- 武器 -->
          <g transform="translate(0,0)">
            <rect width="60" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-weapon" x="18" y="18" width="24" height="24" fill="#666"/>
          </g>
          
          <!-- 防具 -->
          <g transform="translate(70,0)">
            <rect width="60" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-armor" x="18" y="18" width="24" height="24" fill="#666"/>
          </g>
          
          <!-- 饰品 -->
          <g transform="translate(140,0)">
            <rect width="60" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-accessory" x="18" y="18" width="24" height="24" fill="#666"/>
          </g>
        </g>
      </g>
      
      <!-- 技能列表 -->
      <g transform="translate(20,520)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">技能</text>
        
        <!-- 技能列表 -->
        <g transform="translate(20,50)">
          <!-- 技能1 -->
          <g transform="translate(0,0)">
            <rect width="294" height="40" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <text x="50" y="25" font-size="16" fill="#333">御剑术</text>
            <text x="200" y="25" font-size="14" fill="#666">Lv.3</text>
          </g>
          
          <!-- 技能2 -->
          <g transform="translate(0,50)">
            <rect width="294" height="40" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <text x="50" y="25" font-size="16" fill="#333">五行遁术</text>
            <text x="200" y="25" font-size="14" fill="#666">Lv.2</text>
          </g>
          
          <!-- 技能3 -->
          <g transform="translate(0,100)">
            <rect width="294" height="40" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <text x="50" y="25" font-size="16" fill="#333">御风术</text>
            <text x="200" y="25" font-size="14" fill="#666">Lv.1</text>
          </g>
        </g>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#666"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#2196F3"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#666"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#666"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#666"/>
        </g>
      </g>
    </g>

    <!-- 洞府场景 -->
    <g transform="translate(750,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">洞府</text>
      
      <!-- 洞府等级信息 -->
      <g transform="translate(20,80)" filter="url(#cardShadow)">
        <rect width="334" height="120" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">洞府等级</text>
        <text x="20" y="60" font-size="24" fill="#2196F3" font-weight="bold">Lv.3</text>
        <text x="20" y="90" font-size="16" fill="#666">升级所需：1000灵石</text>
      </g>
      
      <!-- 修炼室状态 -->
      <g transform="translate(20,220)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">修炼室</text>
        
        <!-- 修炼室状态 -->
        <g transform="translate(20,50)">
          <use href="#icon-meditation" x="0" y="0" width="40" height="40" fill="#666"/>
          <text x="50" y="25" font-size="16" fill="#333">修炼中</text>
          <text x="50" y="50" font-size="14" fill="#666">剩余时间：2小时30分</text>
          
          <!-- 修炼进度条 -->
          <rect x="0" y="70" width="294" height="20" rx="10" fill="#e0e0e0"/>
          <rect x="0" y="70" width="147" height="20" rx="10" fill="url(#progressGradient)"/>
          <text x="147" y="85" text-anchor="middle" font-size="14" fill="white">50%</text>
        </g>
      </g>
      
      <!-- 资源产出 -->
      <g transform="translate(20,440)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">资源产出</text>
        
        <!-- 资源列表 -->
        <g transform="translate(20,50)">
          <!-- 灵石 -->
          <g transform="translate(0,0)">
            <use href="#icon-resource" x="0" y="0" width="40" height="40" fill="#666"/>
            <text x="50" y="25" font-size="16" fill="#333">灵石</text>
            <text x="50" y="50" font-size="14" fill="#666">产出：100/小时</text>
          </g>
          
          <!-- 丹药 -->
          <g transform="translate(0,70)">
            <use href="#icon-resource" x="0" y="0" width="40" height="40" fill="#666"/>
            <text x="50" y="25" font-size="16" fill="#333">丹药</text>
            <text x="50" y="50" font-size="14" fill="#666">产出：10/小时</text>
          </g>
        </g>
      </g>
      
      <!-- 升级按钮 -->
      <g transform="translate(20,660)">
        <rect width="334" height="50" rx="25" fill="#2196F3" filter="url(#cardShadow)"/>
        <text x="167" y="32" text-anchor="middle" font-size="18" fill="white" font-weight="bold">升级洞府</text>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#666"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#666"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#2196F3"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#666"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#666"/>
        </g>
      </g>
    </g>

    <!-- 试炼场景 -->
    <g transform="translate(1125,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">试炼</text>
      
      <!-- 试炼关卡列表 -->
      <g transform="translate(20,80)" filter="url(#cardShadow)">
        <rect width="334" height="300" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">试炼关卡</text>
        
        <!-- 关卡列表 -->
        <g transform="translate(20,50)">
          <!-- 关卡1 -->
          <g transform="translate(0,0)">
            <rect width="294" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-level" x="10" y="18" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">第一关：筑基试炼</text>
            <text x="50" y="50" font-size="14" fill="#666">推荐战力：1000</text>
            <text x="250" y="40" text-anchor="end" font-size="14" fill="#4CAF50">已完成</text>
          </g>
          
          <!-- 关卡2 -->
          <g transform="translate(0,70)">
            <rect width="294" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-level" x="10" y="18" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">第二关：金丹试炼</text>
            <text x="50" y="50" font-size="14" fill="#666">推荐战力：5000</text>
            <text x="250" y="40" text-anchor="end" font-size="14" fill="#FFC107">进行中</text>
          </g>
          
          <!-- 关卡3 -->
          <g transform="translate(0,140)">
            <rect width="294" height="60" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-level" x="10" y="18" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">第三关：元婴试炼</text>
            <text x="50" y="50" font-size="14" fill="#666">推荐战力：10000</text>
            <text x="250" y="40" text-anchor="end" font-size="14" fill="#9E9E9E">未开启</text>
          </g>
        </g>
      </g>
      
      <!-- 战斗状态 -->
      <g transform="translate(20,400)" filter="url(#cardShadow)">
        <rect width="334" height="120" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">战斗状态</text>
        
        <!-- 战斗状态信息 -->
        <g transform="translate(20,50)">
          <text x="0" y="0" font-size="16" fill="#333">当前进度：2/3</text>
          <text x="0" y="30" font-size="16" fill="#333">剩余时间：10:00</text>
          <text x="0" y="60" font-size="16" fill="#333">当前战力：8000</text>
        </g>
      </g>
      
      <!-- 奖励预览 -->
      <g transform="translate(20,540)" filter="url(#cardShadow)">
        <rect width="334" height="120" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">奖励预览</text>
        
        <!-- 奖励列表 -->
        <g transform="translate(20,50)">
          <use href="#icon-reward" x="0" y="0" width="40" height="40" fill="#666"/>
          <text x="50" y="25" font-size="16" fill="#333">灵石 x 1000</text>
          <text x="50" y="50" font-size="16" fill="#333">丹药 x 100</text>
          <text x="50" y="75" font-size="16" fill="#333">功法残页 x 1</text>
        </g>
      </g>
      
      <!-- 开始战斗按钮 -->
      <g transform="translate(20,680)">
        <rect width="334" height="50" rx="25" fill="#2196F3" filter="url(#cardShadow)"/>
        <text x="167" y="32" text-anchor="middle" font-size="18" fill="white" font-weight="bold">开始战斗</text>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#666"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#666"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#666"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#2196F3"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#666"/>
        </g>
      </g>
    </g>

    <!-- 背包场景 -->
    <g transform="translate(1500,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">背包</text>
      
      <!-- 物品分类标签 -->
      <g transform="translate(0,60)">
        <rect width="374" height="50" fill="white" stroke="#e0e0e0"/>
        <!-- 全部 -->
        <g transform="translate(20,25)">
          <text x="0" y="0" text-anchor="middle" font-size="16" fill="#2196F3" font-weight="bold">全部</text>
          <line x1="-10" y1="15" x2="10" y2="15" stroke="#2196F3" stroke-width="2"/>
        </g>
        <!-- 装备 -->
        <g transform="translate(80,25)">
          <text x="0" y="0" text-anchor="middle" font-size="16" fill="#666">装备</text>
        </g>
        <!-- 丹药 -->
        <g transform="translate(140,25)">
          <text x="0" y="0" text-anchor="middle" font-size="16" fill="#666">丹药</text>
        </g>
        <!-- 功法 -->
        <g transform="translate(200,25)">
          <text x="0" y="0" text-anchor="middle" font-size="16" fill="#666">功法</text>
        </g>
        <!-- 材料 -->
        <g transform="translate(260,25)">
          <text x="0" y="0" text-anchor="middle" font-size="16" fill="#666">材料</text>
        </g>
      </g>
      
      <!-- 物品列表 -->
      <g transform="translate(20,120)" filter="url(#cardShadow)">
        <rect width="334" height="400" rx="8" fill="white" stroke="#e0e0e0"/>
        
        <!-- 物品列表 -->
        <g transform="translate(20,20)">
          <!-- 物品1 -->
          <g transform="translate(0,0)">
            <rect width="294" height="80" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-item" x="10" y="28" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">筑基丹</text>
            <text x="50" y="50" font-size="14" fill="#666">品质：上品</text>
            <text x="50" y="70" font-size="14" fill="#666">数量：x5</text>
            <text x="250" y="50" text-anchor="end" font-size="14" fill="#666">使用</text>
          </g>
          
          <!-- 物品2 -->
          <g transform="translate(0,90)">
            <rect width="294" height="80" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-item" x="10" y="28" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">灵石</text>
            <text x="50" y="50" font-size="14" fill="#666">品质：普通</text>
            <text x="50" y="70" font-size="14" fill="#666">数量：x1000</text>
            <text x="250" y="50" text-anchor="end" font-size="14" fill="#666">使用</text>
          </g>
          
          <!-- 物品3 -->
          <g transform="translate(0,180)">
            <rect width="294" height="80" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-item" x="10" y="28" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">功法残页</text>
            <text x="50" y="50" font-size="14" fill="#666">品质：稀有</text>
            <text x="50" y="70" font-size="14" fill="#666">数量：x1</text>
            <text x="250" y="50" text-anchor="end" font-size="14" fill="#666">使用</text>
          </g>
        </g>
      </g>
      
      <!-- 物品详情 -->
      <g transform="translate(20,540)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">物品详情</text>
        
        <!-- 物品详情信息 -->
        <g transform="translate(20,50)">
          <text x="0" y="0" font-size="16" fill="#333">名称：筑基丹</text>
          <text x="0" y="30" font-size="16" fill="#333">品质：上品</text>
          <text x="0" y="60" font-size="16" fill="#333">效果：提升筑基成功率20%</text>
          <text x="0" y="90" font-size="16" fill="#333">描述：服用后可提升筑基成功率，品质越高效果越好。</text>
        </g>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#666"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#666"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#666"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#666"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#2196F3"/>
        </g>
      </g>
    </g>

    <!-- 功法场景 -->
    <g transform="translate(1875,0)">
      <rect x="0.5" y="0.5" width="374" height="811" fill="white" stroke="#e0e0e0"/>
      
      <!-- 顶部标题栏 -->
      <rect x="0" y="0" width="374" height="60" fill="#2196F3"/>
      <text x="187" y="38" text-anchor="middle" font-size="20" fill="white" font-weight="bold">功法</text>
      
      <!-- 功法列表 -->
      <g transform="translate(20,80)" filter="url(#cardShadow)">
        <rect width="334" height="400" rx="8" fill="white" stroke="#e0e0e0"/>
        
        <!-- 功法列表 -->
        <g transform="translate(20,20)">
          <!-- 功法1 -->
          <g transform="translate(0,0)">
            <rect width="294" height="100" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-gongfa" x="10" y="38" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">御剑术</text>
            <text x="50" y="50" font-size="14" fill="#666">等级：3/9</text>
            <text x="50" y="70" font-size="14" fill="#666">效果：提升攻击力30%</text>
            <text x="50" y="90" font-size="14" fill="#666">修炼进度：70%</text>
            <!-- 进度条 -->
            <rect x="120" y="85" width="150" height="4" rx="2" fill="#e0e0e0"/>
            <rect x="120" y="85" width="105" height="4" rx="2" fill="url(#progressGradient)"/>
          </g>
          
          <!-- 功法2 -->
          <g transform="translate(0,110)">
            <rect width="294" height="100" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-gongfa" x="10" y="38" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">五行遁术</text>
            <text x="50" y="50" font-size="14" fill="#666">等级：2/9</text>
            <text x="50" y="70" font-size="14" fill="#666">效果：提升闪避率20%</text>
            <text x="50" y="90" font-size="14" fill="#666">修炼进度：40%</text>
            <!-- 进度条 -->
            <rect x="120" y="85" width="150" height="4" rx="2" fill="#e0e0e0"/>
            <rect x="120" y="85" width="60" height="4" rx="2" fill="url(#progressGradient)"/>
          </g>
          
          <!-- 功法3 -->
          <g transform="translate(0,220)">
            <rect width="294" height="100" rx="4" fill="#f5f5f5" stroke="#e0e0e0"/>
            <use href="#icon-gongfa" x="10" y="38" width="24" height="24" fill="#666"/>
            <text x="50" y="30" font-size="16" fill="#333">御风术</text>
            <text x="50" y="50" font-size="14" fill="#666">等级：1/9</text>
            <text x="50" y="70" font-size="14" fill="#666">效果：提升速度15%</text>
            <text x="50" y="90" font-size="14" fill="#666">修炼进度：20%</text>
            <!-- 进度条 -->
            <rect x="120" y="85" width="150" height="4" rx="2" fill="#e0e0e0"/>
            <rect x="120" y="85" width="30" height="4" rx="2" fill="url(#progressGradient)"/>
          </g>
        </g>
      </g>
      
      <!-- 功法详情 -->
      <g transform="translate(20,500)" filter="url(#cardShadow)">
        <rect width="334" height="200" rx="8" fill="white" stroke="#e0e0e0"/>
        <text x="20" y="30" font-size="18" fill="#333" font-weight="bold">功法详情</text>
        
        <!-- 功法详情信息 -->
        <g transform="translate(20,50)">
          <text x="0" y="0" font-size="16" fill="#333">名称：御剑术</text>
          <text x="0" y="30" font-size="16" fill="#333">等级：3/9</text>
          <text x="0" y="60" font-size="16" fill="#333">效果：提升攻击力30%</text>
          <text x="0" y="90" font-size="16" fill="#333">修炼要求：筑基期</text>
          <text x="0" y="120" font-size="16" fill="#333">描述：御剑飞行，可提升攻击力，等级越高效果越强。</text>
        </g>
      </g>
      
      <!-- 升级按钮 -->
      <g transform="translate(20,720)">
        <rect width="334" height="50" rx="25" fill="#2196F3" filter="url(#cardShadow)"/>
        <text x="167" y="32" text-anchor="middle" font-size="18" fill="white" font-weight="bold">升级功法</text>
      </g>
      
      <!-- 底部导航栏 -->
      <g transform="translate(0,751)">
        <rect width="374" height="60" fill="white" stroke="#e0e0e0"/>
        <!-- 主页图标 -->
        <g transform="translate(37,30)">
          <use href="#icon-home" width="24" height="24" fill="#666"/>
        </g>
        <!-- 角色图标 -->
        <g transform="translate(112,30)">
          <use href="#icon-character" width="24" height="24" fill="#666"/>
        </g>
        <!-- 洞府图标 -->
        <g transform="translate(187,30)">
          <use href="#icon-dongfu" width="24" height="24" fill="#666"/>
        </g>
        <!-- 试炼图标 -->
        <g transform="translate(262,30)">
          <use href="#icon-battle" width="24" height="24" fill="#666"/>
        </g>
        <!-- 背包图标 -->
        <g transform="translate(337,30)">
          <use href="#icon-bag" width="24" height="24" fill="#666"/>
        </g>
      </g>
    </g>
  </g>
</svg> 