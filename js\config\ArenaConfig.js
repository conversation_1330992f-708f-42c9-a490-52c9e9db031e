/**
 * PVP竞技场配置文件
 * 包含竞技场排名奖励和相关规则
 */

// 竞技场每日奖励配置
const ARENA_DAILY_REWARDS = [
  { minRank: 1, maxRank: 1, xianyu: 1000, description: "竞技场冠军" },
  { minRank: 2, maxRank: 3, xianyu: 800, description: "竞技场亚军" },
  { minRank: 4, maxRank: 10, xianyu: 500, description: "竞技场前十" },
  { minRank: 11, maxRank: 50, xianyu: 300, description: "竞技场前五十" },
  { minRank: 51, maxRank: 100, xianyu: 200, description: "竞技场前一百" },
  { minRank: 101, maxRank: 500, xianyu: 100, description: "竞技场前五百" },
  { minRank: 501, maxRank: 1000, xianyu: 50, description: "竞技场前一千" },
  { minRank: 1001, maxRank: Number.MAX_SAFE_INTEGER, xianyu: 10, description: "竞技场参与奖" }
];

// 竞技场NPC对手名称库
const ARENA_NPC_NAMES = [
  // 修仙门派名
  "青云门", "蜀山派", "昆仑派", "天音寺", "魔教", "星宫", "天机阁", 
  "逍遥谷", "百花谷", "万剑门", "紫霄宫", "太虚观", "灵剑山", "天元宗",
  "玄天宗", "七星阁", "碧海宗", "丹鼎宗", "青莲剑宗", "御剑宗", "天剑宗",
  
  // 修仙者名
  "张三丰", "李逍遥", "赵灵儿", "林月如", "慕容紫英", "独孤求败", "东方不败",
  "风清扬", "任我行", "令狐冲", "岳不群", "左冷禅", "向问天", "莫声谷",
  "田伯光", "余沧海", "木婉清", "段誉", "虚竹", "鸠摩智", "丁春秋",
  "李秋水", "天山童姥", "无崖子", "逍遥子", "慕容复", "阿紫", "阿朱",
  "王语嫣", "阿碧", "阿洪", "游坦之", "公孙止", "梅超风", "陈玄风",
  "黄蓉", "洪七公", "欧阳锋", "周伯通", "黄药师", "郭靖", "杨过",
  "小龙女", "金轮法王", "郭襄", "张无忌", "赵敏", "周芷若", "殷离",
  "谢逊", "殷素素", "张翠山", "殷天正", "韦一笑", "宋青书", "杨不悔"
];

// 竞技场NPC对手称号库
const ARENA_NPC_TITLES = [
  "剑仙", "剑侠", "剑客", "剑圣", "剑痴", "剑魔", "剑神",
  "道长", "道士", "道尊", "道君", "道祖", "道子", "道童",
  "仙子", "仙姑", "仙女", "仙童", "仙人", "仙帝", "仙尊",
  "真人", "真君", "真仙", "真武", "真圣", "真魔", "真神",
  "散仙", "散修", "散人", "散客", "散侠", "散魔", "散神",
  "魔头", "魔王", "魔尊", "魔帝", "魔神", "魔君", "魔子",
  "妖王", "妖帝", "妖仙", "妖圣", "妖神", "妖君", "妖女"
];

// 竞技场NPC战力计算公式
// 根据排名计算NPC战力
function calculateNpcPower(rank) {
  if (rank <= 10) {
    // 前10名战力特别高
    return 10000 - (rank - 1) * 5000;
  } else if (rank <= 100) {
    // 前100名战力较高
    return 5000 - (rank - 10) * 300;
  } else if (rank <= 500) {
    // 前500名战力中等
    return 2500 - (rank - 100) * 30;
  } else if (rank <= 1000) {
    // 前1000名战力较低
    return 1500 - (rank - 500) * 10;
  } else {
    // 1000名以后战力很低
    return 1000 - Math.min(5000, (rank - 1000) * 2);
  }
}

// 生成随机NPC对手
function generateNpcOpponent(rank) {
  // 随机选择名字和称号
  const nameIndex = Math.floor(Math.random() * ARENA_NPC_NAMES.length);
  const titleIndex = Math.floor(Math.random() * ARENA_NPC_TITLES.length);
  
  const name = ARENA_NPC_NAMES[nameIndex];
  const title = ARENA_NPC_TITLES[titleIndex];
  
  // 计算战力
  const power = calculateNpcPower(rank);
  
  // 根据排名确定境界
  let cultivation;
  if (rank <= 10) {
    cultivation = "大乘期后期";
  } else if (rank <= 50) {
    cultivation = "大乘期中期";
  } else if (rank <= 100) {
    cultivation = "大乘期初期";
  } else if (rank <= 200) {
    cultivation = "渡劫期后期";
  } else if (rank <= 300) {
    cultivation = "渡劫期中期";
  } else if (rank <= 400) {
    cultivation = "渡劫期初期";
  } else if (rank <= 500) {
    cultivation = "合道期后期";
  } else if (rank <= 600) {
    cultivation = "合道期中期";
  } else if (rank <= 700) {
    cultivation = "合道期初期";
  } else if (rank <= 800) {
    cultivation = "返虚期后期";
  } else if (rank <= 900) {
    cultivation = "返虚期中期";
  } else if (rank <= 1000) {
    cultivation = "返虚期初期";
  } else {
    cultivation = "化神期后期";
  }
  
  return {
    id: `npc_${rank}`,
    name: `${name}${title}`,
    power,
    cultivation,
    rank,
    isNpc: true
  };
}

// 导出配置
export {
  ARENA_DAILY_REWARDS,
  ARENA_NPC_NAMES,
  ARENA_NPC_TITLES,
  calculateNpcPower,
  generateNpcOpponent
};
