/**
 * 剑心配置文件
 * 定义所有剑心的基础属性和进阶加成
 */

// 剑心基础配置
const SWORD_HEART_CONFIG = [
  {
    id: 'qingfeng',
    name: '青锋剑心',
    description: '初窥门径者的锐利剑气',
    unlocked: true, // 初始解锁
    attributes: {
      attack: 5,
      critRate: 0.01
    },
    color: '#38b2ac', // 青色
    // 每级提升的属性
    levelUpAttributes: {
      attack: 3,
      critRate: 0.005
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_qingfeng', count: 1 }], // 1阶
      [{ id: 'swordheart_qingfeng', count: 2 }], // 2阶
      [{ id: 'swordheart_qingfeng', count: 3 }], // 3阶
      [{ id: 'swordheart_qingfeng', count: 4 }], // 4阶
      [{ id: 'swordheart_qingfeng', count: 5 }], // 5阶
      [{ id: 'swordheart_qingfeng', count: 6 }], // 6阶
      [{ id: 'swordheart_qingfeng', count: 7 }], // 7阶
      [{ id: 'swordheart_qingfeng', count: 8 }], // 8阶
      [{ id: 'swordheart_qingfeng', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'hantie',
    name: '寒铁剑心',
    description: '寒冰属性的基础剑魄',
    attributes: {
      attack: 4,
      defense: 3,
      damageBonus: 0.01
    },
    color: '#90cdf4', // 淡蓝色
    // 每级提升的属性
    levelUpAttributes: {
      attack: 2,
      defense: 2,
      damageBonus: 0.005
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_hantie', count: 1 }], // 1阶
      [{ id: 'swordheart_hantie', count: 2 }], // 2阶
      [{ id: 'swordheart_hantie', count: 3 }], // 3阶
      [{ id: 'swordheart_hantie', count: 4 }], // 4阶
      [{ id: 'swordheart_hantie', count: 5 }], // 5阶
      [{ id: 'swordheart_hantie', count: 6 }], // 6阶
      [{ id: 'swordheart_hantie', count: 7 }], // 7阶
      [{ id: 'swordheart_hantie', count: 8 }], // 8阶
      [{ id: 'swordheart_hantie', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'liuyun',
    name: '流云剑心',
    description: '缥缈不定的清风剑意',
    attributes: {
      speed: 3,
      dodgeRate: 0.02,
      attack: 3
    },
    color: '#e2e8f0', // 淡灰色
    // 每级提升的属性
    levelUpAttributes: {
      speed: 2,
      dodgeRate: 0.01,
      attack: 1
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_liuyun', count: 1 }], // 1阶
      [{ id: 'swordheart_liuyun', count: 2 }], // 2阶
      [{ id: 'swordheart_liuyun', count: 3 }], // 3阶
      [{ id: 'swordheart_liuyun', count: 4 }], // 4阶
      [{ id: 'swordheart_liuyun', count: 5 }], // 5阶
      [{ id: 'swordheart_liuyun', count: 6 }], // 6阶
      [{ id: 'swordheart_liuyun', count: 7 }], // 7阶
      [{ id: 'swordheart_liuyun', count: 8 }], // 8阶
      [{ id: 'swordheart_liuyun', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'panshi',
    name: '磐石剑心',
    description: '稳如泰山的防御型剑魂',
    attributes: {
      defense: 8,
      hp: 50,
      damageReduction: 0.02
    },
    color: '#a0aec0', // 灰色
    // 每级提升的属性
    levelUpAttributes: {
      defense: 5,
      hp: 30,
      damageReduction: 0.01
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_panshi', count: 1 }], // 1阶
      [{ id: 'swordheart_panshi', count: 2 }], // 2阶
      [{ id: 'swordheart_panshi', count: 3 }], // 3阶
      [{ id: 'swordheart_panshi', count: 4 }], // 4阶
      [{ id: 'swordheart_panshi', count: 5 }], // 5阶
      [{ id: 'swordheart_panshi', count: 6 }], // 6阶
      [{ id: 'swordheart_panshi', count: 7 }], // 7阶
      [{ id: 'swordheart_panshi', count: 8 }], // 8阶
      [{ id: 'swordheart_panshi', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'chiyan',
    name: '赤炎剑心',
    description: '普通火属性剑魄',
    attributes: {
      attack: 7,
      critDamage: 0.05,
      penetration: 2
    },
    color: '#f56565', // 红色
    // 每级提升的属性
    levelUpAttributes: {
      attack: 4,
      critDamage: 0.03,
      penetration: 1
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_chiyan', count: 1 }], // 1阶
      [{ id: 'swordheart_chiyan', count: 2 }], // 2阶
      [{ id: 'swordheart_chiyan', count: 3 }], // 3阶
      [{ id: 'swordheart_chiyan', count: 4 }], // 4阶
      [{ id: 'swordheart_chiyan', count: 5 }], // 5阶
      [{ id: 'swordheart_chiyan', count: 6 }], // 6阶
      [{ id: 'swordheart_chiyan', count: 7 }], // 7阶
      [{ id: 'swordheart_chiyan', count: 8 }], // 8阶
      [{ id: 'swordheart_chiyan', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'duanlang',
    name: '断浪剑心',
    description: '克制水系的基础剑意',
    attributes: {
      attack: 6,
      speed: 2,
      critRate: 0.02
    },
    color: '#4299e1', // 蓝色
    // 每级提升的属性
    levelUpAttributes: {
      attack: 3,
      speed: 1,
      critRate: 0.01
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_duanlang', count: 1 }], // 1阶
      [{ id: 'swordheart_duanlang', count: 2 }], // 2阶
      [{ id: 'swordheart_duanlang', count: 3 }], // 3阶
      [{ id: 'swordheart_duanlang', count: 4 }], // 4阶
      [{ id: 'swordheart_duanlang', count: 5 }], // 5阶
      [{ id: 'swordheart_duanlang', count: 6 }], // 6阶
      [{ id: 'swordheart_duanlang', count: 7 }], // 7阶
      [{ id: 'swordheart_duanlang', count: 8 }], // 8阶
      [{ id: 'swordheart_duanlang', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'guwu',
    name: '孤鹜剑心',
    description: '偏向速度的飞鸟剑魄',
    attributes: {
      speed: 5,
      attack: 4,
      hitRate: 0.03
    },
    color: '#48bb78', // 绿色
    // 每级提升的属性
    levelUpAttributes: {
      speed: 3,
      attack: 2,
      hitRate: 0.01
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_guwu', count: 1 }], // 1阶
      [{ id: 'swordheart_guwu', count: 2 }], // 2阶
      [{ id: 'swordheart_guwu', count: 3 }], // 3阶
      [{ id: 'swordheart_guwu', count: 4 }], // 4阶
      [{ id: 'swordheart_guwu', count: 5 }], // 5阶
      [{ id: 'swordheart_guwu', count: 6 }], // 6阶
      [{ id: 'swordheart_guwu', count: 7 }], // 7阶
      [{ id: 'swordheart_guwu', count: 8 }], // 8阶
      [{ id: 'swordheart_guwu', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'huangsha',
    name: '黄沙剑心',
    description: '沙尘属性的粗糙剑魂',
    attributes: {
      defense: 6,
      attack: 3,
      hp: 30
    },
    color: '#ecc94b', // 黄色
    // 每级提升的属性
    levelUpAttributes: {
      defense: 3,
      attack: 2,
      hp: 20
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_huangsha', count: 1 }], // 1阶
      [{ id: 'swordheart_huangsha', count: 2 }], // 2阶
      [{ id: 'swordheart_huangsha', count: 3 }], // 3阶
      [{ id: 'swordheart_huangsha', count: 4 }], // 4阶
      [{ id: 'swordheart_huangsha', count: 5 }], // 5阶
      [{ id: 'swordheart_huangsha', count: 6 }], // 6阶
      [{ id: 'swordheart_huangsha', count: 7 }], // 7阶
      [{ id: 'swordheart_huangsha', count: 8 }], // 8阶
      [{ id: 'swordheart_huangsha', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'canyue',
    name: '残月剑心',
    description: '残缺不全的月华剑气',
    attributes: {
      attack: 5,
      critRate: 0.015,
      critDamage: 0.03
    },
    color: '#d6bcfa', // 淡紫色
    // 每级提升的属性
    levelUpAttributes: {
      attack: 3,
      critRate: 0.01,
      critDamage: 0.02
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_canyue', count: 1 }], // 1阶
      [{ id: 'swordheart_canyue', count: 2 }], // 2阶
      [{ id: 'swordheart_canyue', count: 3 }], // 3阶
      [{ id: 'swordheart_canyue', count: 4 }], // 4阶
      [{ id: 'swordheart_canyue', count: 5 }], // 5阶
      [{ id: 'swordheart_canyue', count: 6 }], // 6阶
      [{ id: 'swordheart_canyue', count: 7 }], // 7阶
      [{ id: 'swordheart_canyue', count: 8 }], // 8阶
      [{ id: 'swordheart_canyue', count: 9 }]  // 9阶
    ]
  },
  {
    id: 'kumu',
    name: '枯木剑心',
    description: '生机微弱的木系剑魄',
    attributes: {
      hp: 40,
      defense: 4,
      healEffect: 0.02
    },
    color: '#9c4221', // 棕色
    // 每级提升的属性
    levelUpAttributes: {
      hp: 25,
      defense: 2,
      healEffect: 0.01
    },
    // 进阶所需材料（同名剑心）
    advancementRequirements: [
      [{ id: 'swordheart_kumu', count: 1 }], // 1阶
      [{ id: 'swordheart_kumu', count: 2 }], // 2阶
      [{ id: 'swordheart_kumu', count: 3 }], // 3阶
      [{ id: 'swordheart_kumu', count: 4 }], // 4阶
      [{ id: 'swordheart_kumu', count: 5 }], // 5阶
      [{ id: 'swordheart_kumu', count: 6 }], // 6阶
      [{ id: 'swordheart_kumu', count: 7 }], // 7阶
      [{ id: 'swordheart_kumu', count: 8 }], // 8阶
      [{ id: 'swordheart_kumu', count: 9 }]  // 9阶
    ]
  }
];

// 剑心进阶加成配置
const SWORD_HEART_ADVANCEMENT_BONUS = [
  // 0阶（未进阶）
  {
    multiplier: 1.0, // 基础属性倍率
    description: '未进阶'
  },
  // 1阶
  {
    multiplier: 1.2, // 属性提升20%
    description: '一阶剑心'
  },
  // 2阶
  {
    multiplier: 1.5, // 属性提升50%
    description: '二阶剑心'
  },
  // 3阶
  {
    multiplier: 1.8, // 属性提升80%
    description: '三阶剑心'
  },
  // 4阶
  {
    multiplier: 2.2, // 属性提升120%
    description: '四阶剑心'
  },
  // 5阶
  {
    multiplier: 2.6, // 属性提升160%
    description: '五阶剑心'
  },
  // 6阶
  {
    multiplier: 3.0, // 属性提升200%
    description: '六阶剑心'
  },
  // 7阶
  {
    multiplier: 3.5, // 属性提升250%
    description: '七阶剑心'
  },
  // 8阶
  {
    multiplier: 4.0, // 属性提升300%
    description: '八阶剑心'
  },
  // 9阶
  {
    multiplier: 5.0, // 属性提升400%
    description: '九阶剑心'
  }
];

export { SWORD_HEART_CONFIG, SWORD_HEART_ADVANCEMENT_BONUS };
