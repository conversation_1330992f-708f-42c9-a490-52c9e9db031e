/**
 * VIP系统模型
 * 管理VIP等级和特权
 */
class VIPSystem {
  constructor() {
    // VIP等级配置
    this.vipLevels = [
      { 
        level: 0, 
        name: '普通玩家', 
        requiredRecharge: 0,
        benefits: {
          dongfuSpeedBonus: 0,      // 洞府修炼速度加成
          idleRewardBonus: 0,       // 游历收益加成
          dailyLingshi: 0,          // 每日灵石
          dailyXianyu: 0,           // 每日仙玉
          maxEnergy: 100,           // 最大体力
          energyRecoveryRate: 1,    // 体力恢复速率
          extraDailyTrials: 0       // 额外每日试炼次数
        }
      },
      { 
        level: 1, 
        name: 'VIP 1', 
        requiredRecharge: 6,
        benefits: {
          dongfuSpeedBonus: 0.1,    // 洞府修炼速度+10%
          idleRewardBonus: 0.1,     // 游历收益+10%
          dailyLingshi: 1000,       // 每日1000灵石
          dailyXianyu: 10,          // 每日10仙玉
          maxEnergy: 120,           // 最大体力120
          energyRecoveryRate: 1.1,  // 体力恢复速率1.1倍
          extraDailyTrials: 1       // 额外1次每日试炼
        }
      },
      { 
        level: 2, 
        name: 'VIP 2', 
        requiredRecharge: 30,
        benefits: {
          dongfuSpeedBonus: 0.2,    // 洞府修炼速度+20%
          idleRewardBonus: 0.2,     // 游历收益+20%
          dailyLingshi: 2000,       // 每日2000灵石
          dailyXianyu: 20,          // 每日20仙玉
          maxEnergy: 140,           // 最大体力140
          energyRecoveryRate: 1.2,  // 体力恢复速率1.2倍
          extraDailyTrials: 2       // 额外2次每日试炼
        }
      },
      { 
        level: 3, 
        name: 'VIP 3', 
        requiredRecharge: 128,
        benefits: {
          dongfuSpeedBonus: 0.3,    // 洞府修炼速度+30%
          idleRewardBonus: 0.3,     // 游历收益+30%
          dailyLingshi: 3000,       // 每日3000灵石
          dailyXianyu: 30,          // 每日30仙玉
          maxEnergy: 160,           // 最大体力160
          energyRecoveryRate: 1.3,  // 体力恢复速率1.3倍
          extraDailyTrials: 3       // 额外3次每日试炼
        }
      },
      { 
        level: 4, 
        name: 'VIP 4', 
        requiredRecharge: 328,
        benefits: {
          dongfuSpeedBonus: 0.4,    // 洞府修炼速度+40%
          idleRewardBonus: 0.4,     // 游历收益+40%
          dailyLingshi: 4000,       // 每日4000灵石
          dailyXianyu: 40,          // 每日40仙玉
          maxEnergy: 180,           // 最大体力180
          energyRecoveryRate: 1.4,  // 体力恢复速率1.4倍
          extraDailyTrials: 4       // 额外4次每日试炼
        }
      },
      { 
        level: 5, 
        name: 'VIP 5', 
        requiredRecharge: 648,
        benefits: {
          dongfuSpeedBonus: 0.5,    // 洞府修炼速度+50%
          idleRewardBonus: 0.5,     // 游历收益+50%
          dailyLingshi: 5000,       // 每日5000灵石
          dailyXianyu: 50,          // 每日50仙玉
          maxEnergy: 200,           // 最大体力200
          energyRecoveryRate: 1.5,  // 体力恢复速率1.5倍
          extraDailyTrials: 5       // 额外5次每日试炼
        }
      },
      { 
        level: 6, 
        name: 'VIP 6', 
        requiredRecharge: 1280,
        benefits: {
          dongfuSpeedBonus: 0.6,    // 洞府修炼速度+60%
          idleRewardBonus: 0.6,     // 游历收益+60%
          dailyLingshi: 6000,       // 每日6000灵石
          dailyXianyu: 60,          // 每日60仙玉
          maxEnergy: 220,           // 最大体力220
          energyRecoveryRate: 1.6,  // 体力恢复速率1.6倍
          extraDailyTrials: 6       // 额外6次每日试炼
        }
      },
      { 
        level: 7, 
        name: 'VIP 7', 
        requiredRecharge: 2580,
        benefits: {
          dongfuSpeedBonus: 0.7,    // 洞府修炼速度+70%
          idleRewardBonus: 0.7,     // 游历收益+70%
          dailyLingshi: 7000,       // 每日7000灵石
          dailyXianyu: 70,          // 每日70仙玉
          maxEnergy: 240,           // 最大体力240
          energyRecoveryRate: 1.7,  // 体力恢复速率1.7倍
          extraDailyTrials: 7       // 额外7次每日试炼
        }
      },
      { 
        level: 8, 
        name: 'VIP 8', 
        requiredRecharge: 5180,
        benefits: {
          dongfuSpeedBonus: 0.8,    // 洞府修炼速度+80%
          idleRewardBonus: 0.8,     // 游历收益+80%
          dailyLingshi: 8000,       // 每日8000灵石
          dailyXianyu: 80,          // 每日80仙玉
          maxEnergy: 260,           // 最大体力260
          energyRecoveryRate: 1.8,  // 体力恢复速率1.8倍
          extraDailyTrials: 8       // 额外8次每日试炼
        }
      },
      { 
        level: 9, 
        name: 'VIP 9', 
        requiredRecharge: 10000,
        benefits: {
          dongfuSpeedBonus: 1.0,    // 洞府修炼速度+100%
          idleRewardBonus: 1.0,     // 游历收益+100%
          dailyLingshi: 10000,      // 每日10000灵石
          dailyXianyu: 100,         // 每日100仙玉
          maxEnergy: 300,           // 最大体力300
          energyRecoveryRate: 2.0,  // 体力恢复速率2.0倍
          extraDailyTrials: 10      // 额外10次每日试炼
        }
      }
    ];
    
    // 充值档位配置
    this.rechargeTiers = [
      { id: 'recharge_6', name: '6元充值', amount: 6, xianyu: 60 },
      { id: 'recharge_30', name: '30元充值', amount: 30, xianyu: 300 },
      { id: 'recharge_68', name: '68元充值', amount: 68, xianyu: 680 },
      { id: 'recharge_128', name: '128元充值', amount: 128, xianyu: 1280 },
      { id: 'recharge_328', name: '328元充值', amount: 328, xianyu: 3280 },
      { id: 'recharge_648', name: '648元充值', amount: 648, xianyu: 6480 }
    ];
  }
  
  /**
   * 获取VIP等级信息
   * @param {number} level VIP等级
   * @returns {Object} VIP等级信息
   */
  getVIPLevelInfo(level) {
    return this.vipLevels.find(vip => vip.level === level) || this.vipLevels[0];
  }
  
  /**
   * 获取所有VIP等级信息
   * @returns {Array} 所有VIP等级信息
   */
  getAllVIPLevels() {
    return this.vipLevels;
  }
  
  /**
   * 获取所有充值档位
   * @returns {Array} 所有充值档位
   */
  getAllRechargeTiers() {
    return this.rechargeTiers;
  }
  
  /**
   * 根据充值金额计算VIP等级
   * @param {number} totalRecharge 总充值金额
   * @returns {number} VIP等级
   */
  calculateVIPLevel(totalRecharge) {
    // 从高到低检查每个VIP等级的要求
    for (let i = this.vipLevels.length - 1; i >= 0; i--) {
      if (totalRecharge >= this.vipLevels[i].requiredRecharge) {
        return this.vipLevels[i].level;
      }
    }
    return 0; // 默认为普通玩家
  }
  
  /**
   * 获取下一个VIP等级所需的充值金额
   * @param {number} currentVIPLevel 当前VIP等级
   * @param {number} totalRecharge 总充值金额
   * @returns {Object} 下一级VIP信息和所需金额
   */
  getNextVIPLevelRequirement(currentVIPLevel, totalRecharge) {
    // 如果已经是最高VIP等级
    if (currentVIPLevel >= this.vipLevels.length - 1) {
      return { 
        nextLevel: null, 
        requiredAmount: 0,
        currentAmount: totalRecharge
      };
    }
    
    const nextVIPLevel = this.vipLevels[currentVIPLevel + 1];
    return {
      nextLevel: nextVIPLevel,
      requiredAmount: nextVIPLevel.requiredRecharge - totalRecharge,
      currentAmount: totalRecharge
    };
  }
  
  /**
   * 获取VIP特权加成
   * @param {number} vipLevel VIP等级
   * @param {string} benefitKey 特权键名
   * @returns {number} 特权加成值
   */
  getVIPBenefit(vipLevel, benefitKey) {
    const vipInfo = this.getVIPLevelInfo(vipLevel);
    if (!vipInfo || !vipInfo.benefits || typeof vipInfo.benefits[benefitKey] === 'undefined') {
      return 0;
    }
    return vipInfo.benefits[benefitKey];
  }
  
  /**
   * 检查是否应该发放每日VIP奖励
   * @param {Object} player 玩家数据
   * @returns {boolean} 是否应该发放奖励
   */
  shouldGiveDailyReward(player) {
    if (!player) return false;
    
    const now = new Date();
    const lastRewardTime = player.lastVIPRewardTime ? new Date(player.lastVIPRewardTime) : null;
    
    // 如果没有领取记录，或者上次领取时间不是今天，则可以领取
    if (!lastRewardTime) return true;
    
    return !this.isSameDay(now, lastRewardTime);
  }
  
  /**
   * 检查两个日期是否是同一天
   * @param {Date} date1 日期1
   * @param {Date} date2 日期2
   * @returns {boolean} 是否是同一天
   */
  isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }
  
  /**
   * 发放每日VIP奖励
   * @param {Object} player 玩家数据
   * @returns {Object} 奖励内容
   */
  giveDailyVIPReward(player) {
    if (!player || !player.vipLevel) return null;
    
    const vipInfo = this.getVIPLevelInfo(player.vipLevel);
    if (!vipInfo) return null;
    
    // 准备奖励内容
    const rewards = {
      lingshi: vipInfo.benefits.dailyLingshi || 0,
      xianyu: vipInfo.benefits.dailyXianyu || 0
    };
    
    // 更新领取时间
    player.lastVIPRewardTime = new Date().toISOString();
    
    return rewards;
  }
}

export default VIPSystem;
