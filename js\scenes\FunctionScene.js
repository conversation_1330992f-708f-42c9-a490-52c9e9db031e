/**
 * 功法场景类
 * 展示不同境界的功法列表
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';

class FunctionScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的境界索引
    this.selectedRealmIndex = 0;

    // 功法数据 - 每个境界3本功法
    this.functionData = [
      // 第一阶 - 炼气期
      {
        realm: '炼气期',
        functions: [
          { id: 'function_1_1', name: '聚气诀', level: 1, description: '炼气初期入门功法', unlockPhase: '初期' },
          { id: 'function_1_2', name: '引气诀', level: 1, description: '炼气中期修炼功法', unlockPhase: '中期' },
          { id: 'function_1_3', name: '凝气诀', level: 1, description: '炼气后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第二阶 - 筑基期
      {
        realm: '筑基期',
        functions: [
          { id: 'function_2_1', name: '固本培元', level: 2, description: '筑基初期修炼功法', unlockPhase: '初期' },
          { id: 'function_2_2', name: '灵气归元', level: 2, description: '筑基中期修炼功法', unlockPhase: '中期' },
          { id: 'function_2_3', name: '筑基诀', level: 2, description: '筑基后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第三阶 - 金丹期
      {
        realm: '金丹期',
        functions: [
          { id: 'function_3_1', name: '金丹初诀', level: 3, description: '金丹初期修炼功法', unlockPhase: '初期' },
          { id: 'function_3_2', name: '金丹凝结', level: 3, description: '金丹中期修炼功法', unlockPhase: '中期' },
          { id: 'function_3_3', name: '丹火纯青', level: 3, description: '金丹后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第四阶 - 元婴期
      {
        realm: '元婴期',
        functions: [
          { id: 'function_4_1', name: '元婴初成', level: 4, description: '元婴初期修炼功法', unlockPhase: '初期' },
          { id: 'function_4_2', name: '元婴凝结', level: 4, description: '元婴中期修炼功法', unlockPhase: '中期' },
          { id: 'function_4_3', name: '元婴圆满', level: 4, description: '元婴后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第五阶 - 化神期
      {
        realm: '化神期',
        functions: [
          { id: 'function_5_1', name: '化神初诀', level: 5, description: '化神初期修炼功法', unlockPhase: '初期' },
          { id: 'function_5_2', name: '神形凝结', level: 5, description: '化神中期修炼功法', unlockPhase: '中期' },
          { id: 'function_5_3', name: '化神圆满', level: 5, description: '化神后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第六阶 - 炼虚期
      {
        realm: '炼虚期',
        functions: [
          { id: 'function_6_1', name: '炼虚初诀', level: 6, description: '炼虚初期修炼功法', unlockPhase: '初期' },
          { id: 'function_6_2', name: '虚实相生', level: 6, description: '炼虚中期修炼功法', unlockPhase: '中期' },
          { id: 'function_6_3', name: '返虚圆满', level: 6, description: '炼虚后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第七阶 - 合体期
      {
        realm: '合体期',
        functions: [
          { id: 'function_7_1', name: '合体初诀', level: 7, description: '合体初期修炼功法', unlockPhase: '初期' },
          { id: 'function_7_2', name: '天人合一', level: 7, description: '合体中期修炼功法', unlockPhase: '中期' },
          { id: 'function_7_3', name: '大道同体', level: 7, description: '合体后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第八阶 - 大乘期
      {
        realm: '大乘期',
        functions: [
          { id: 'function_8_1', name: '大乘初诀', level: 8, description: '大乘初期修炼功法', unlockPhase: '初期' },
          { id: 'function_8_2', name: '乘风破法', level: 8, description: '大乘中期修炼功法', unlockPhase: '中期' },
          { id: 'function_8_3', name: '大乘圆满', level: 8, description: '大乘后期修炼功法', unlockPhase: '后期' }
        ]
      },
      // 第九阶 - 渡劫期
      {
        realm: '渡劫期',
        functions: [
          { id: 'function_9_1', name: '渡劫初诀', level: 9, description: '渡劫初期修炼功法', unlockPhase: '初期' },
          { id: 'function_9_2', name: '天劫不灭', level: 9, description: '渡劫中期修炼功法', unlockPhase: '中期' },
          { id: 'function_9_3', name: '虚空渡劫', level: 9, description: '渡劫后期修炼功法', unlockPhase: '后期' }
        ]
      }
    ];
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        // 返回到主页
        this.sceneManager.showScene('main');
      }
    );

    this.addUIElement(this.backButton);

    // 创建境界选择按钮
    this.createRealmButtons();

    // 创建功法按钮
    this.createFunctionButtons();
  }

  // 创建境界选择按钮
  createRealmButtons() {
    const headerHeight = 80;
    const buttonWidth = (this.screenWidth - 20) / 3; // 每行显示三个按钮
    const buttonHeight = 40;
    const margin = 10;

    // 获取玩家当前修炼境界
    const mainCharacter = this.getMainCharacter();
    const currentRealm = mainCharacter ? mainCharacter.cultivation : '炼气期';
    const realmIndex = this.getRealmIndex(currentRealm);

    // 设置当前选中的境界索引
    if (this.selectedRealmIndex === 0) {
      this.selectedRealmIndex = realmIndex;
    }

    // 创建境界选择按钮
    for (let i = 0; i < this.functionData.length; i++) {
      const data = this.functionData[i];
      const row = Math.floor(i / 3);
      const col = i % 3;

      const x = 10 + col * buttonWidth;
      const y = headerHeight + 10 + row * (buttonHeight + 10);

      // 判断该境界是否已解锁
      const isUnlocked = i <= realmIndex;

      this.realmButtons = new Button(
        this.ctx,
        x,
        y,
        buttonWidth - 10,
        buttonHeight,
        data.realm,
        null,
        isUnlocked ? (i === this.selectedRealmIndex ? '#4CAF50' : null) : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            this.selectedRealmIndex = i;
            // 重新创建UI以反映选择变化
            this.initUI();
          } else {
            wx.showToast({
              title: '境界尚未达到，无法解锁',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.addUIElement(this.realmButtons);
    }
  }

  // 创建功法按钮
  createFunctionButtons() {
    const headerHeight = 80;
    const realmButtonsHeight = Math.ceil(this.functionData.length / 3) * 50;
    const startY = headerHeight + realmButtonsHeight + 30;
    const buttonWidth = this.screenWidth - 60;
    const buttonHeight = 60;
    const margin = 15;

    // 获取当前境界的功法
    const currentRealmData = this.functionData[this.selectedRealmIndex];
    if (!currentRealmData) return;

    // 获取玩家当前修炼境界和阶段
    const mainCharacter = this.getMainCharacter();
    const currentRealm = mainCharacter ? mainCharacter.cultivation : '炼气期';
    const currentPhase = this.getCultivationPhase(mainCharacter);

    // 创建功法按钮
    for (let i = 0; i < currentRealmData.functions.length; i++) {
      const functionData = currentRealmData.functions[i];

      // 判断功法是否已解锁
      const isUnlocked =
        currentRealm !== currentRealmData.realm || // 如果已经超过这个境界，全部解锁
        this.isPhaseUnlocked(currentPhase, functionData.unlockPhase); // 否则根据境界阶段判断

      this.functionButtons = new Button(
        this.ctx,
        30,
        startY + i * (buttonHeight + margin),
        buttonWidth,
        buttonHeight,
        functionData.name,
        null,
        isUnlocked ? null : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            // 进入功法详情页面
            this.sceneManager.showScene('functionDetail', { functionId: functionData.id });
          } else {
            wx.showToast({
              title: `需要达到${currentRealmData.realm}${functionData.unlockPhase}才能解锁`,
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.addUIElement(this.functionButtons);
    }
  }

  // 获取主角色
  getMainCharacter() {
    const characters = game.gameStateManager.getCharacters();
    if (!characters || characters.length === 0) return null;

    return characters.find(char => char.id === 1);
  }

  // 获取境界对应的索引
  getRealmIndex(realm) {
    // 处理新的境界命名规范
    let baseRealm = realm;

    // 如果境界包含层数或重数，只取基础境界名称
    if (realm.includes('练气期')) {
      baseRealm = '炼气期';
    } else if (realm.includes('筑基')) {
      baseRealm = '筑基期';
    } else if (realm.includes('金丹')) {
      baseRealm = '金丹期';
    } else if (realm.includes('元婴')) {
      baseRealm = '元婴期';
    } else if (realm.includes('化神')) {
      baseRealm = '化神期';
    } else if (realm.includes('返虚') || realm.includes('炼虚')) {
      baseRealm = '炼虚期';
    } else if (realm.includes('合道') || realm.includes('合体')) {
      baseRealm = '合体期';
    } else if (realm.includes('大乘')) {
      baseRealm = '大乘期';
    } else if (realm.includes('渡劫')) {
      baseRealm = '渡劫期';
    }

    const realms = [
      '炼气期', '筑基期', '金丹期', '元婴期',
      '化神期', '炼虚期', '合体期', '大乘期', '渡劫期'
    ];

    return realms.indexOf(baseRealm);
  }

  // 获取当前修炼阶段（初期、中期、后期）
  getCultivationPhase(character) {
    if (!character) return '初期';

    // 如果境界名称中包含阶段信息，直接提取
    const cultivation = character.cultivation || '';

    if (cultivation.includes('初期')) {
      return '初期';
    } else if (cultivation.includes('中期')) {
      return '中期';
    } else if (cultivation.includes('后期')) {
      return '后期';
    }

    // 如果境界名称中不包含阶段信息，则根据层数判断
    if (cultivation.includes('练气期')) {
      const layer = parseInt(cultivation.replace(/[^0-9]/g, '')) || 1;
      if (layer <= 4) return '初期';
      if (layer <= 8) return '中期';
      return '后期';
    }

    // 如果以上方法都无法判断，则使用等级判断（兼容旧版本）
    const level = character.level || 1;

    // 每个境界分为30级，10级一个阶段
    const phaseLevel = level % 30;

    if (phaseLevel < 10) return '初期';
    if (phaseLevel < 20) return '中期';
    return '后期';
  }

  // 判断当前阶段是否已解锁目标阶段
  isPhaseUnlocked(currentPhase, targetPhase) {
    const phases = ['初期', '中期', '后期'];
    const currentIndex = phases.indexOf(currentPhase);
    const targetIndex = phases.indexOf(targetPhase);

    return currentIndex >= targetIndex;
  }

  // 场景显示时的回调
  onShow(params) {
    // 初始化UI
    this.initUI();
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制标题
    this.drawTitle();

    // 绘制功法信息
    this.drawFunctionInfo();
  }

  // 绘制背景
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制标题
  drawTitle() {
    const headerHeight = 60;

    // 绘制标题栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制标题文本
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('功法', this.screenWidth / 2, headerHeight / 2 + 8);

    // 绘制功法修炼点
    const player = game.gameStateManager.getPlayer();
    const functionPoints = player.resources && player.resources.functionPoints ? player.resources.functionPoints : 0;

    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`功法修炼点: ${functionPoints}`, this.screenWidth - 20, headerHeight / 2 + 8);
  }

  // 绘制功法信息
  drawFunctionInfo() {
    // 在这里不需要进行额外绘制，因为功法按钮已经在createFunctionButtons方法中创建
    // 如果有其他信息需要显示，可以在这里添加
  }
}

// 修改导出方式为CommonJS
module.exports = FunctionScene;